# CID (Change Implementation Document) - Logica di Versioning ARDEC

## 🎯 **Concetto Fondamentale**

**CID = Change Implementation Document**

I **CID** rappresentano documenti di implementazione dei cambiamenti che definiscono **configurazioni/varianti specifiche** di un catalogo. Ogni CID identifica una configurazione che può coesistere simultaneamente nello stesso sistema, con parti specifiche che possono variare tra configurazioni diverse.

## 📋 **Struttura Logica Completa**

### **Gerarchia del Sistema (CORRETTA):**
```
CATALOGO (TER) - TB_Cataloghi
├── TB_Composizione (TER + Tavola) → Definisce QUALI tavole appartengono al catalogo
│   ├── Tavola "ROTORS" (ordine: nTavola=1.0)
│   ├── Tavola "ENGINE" (ordine: nTavola=2.0)
│   └── Tavola "WEAPONS" (ordine: nTavola=3.0)
│
├── TB_CID (TER + CID) → Definisce le configurazioni disponibili
│   ├── CID="STANDARD-ARMY"
│   ├── CID="DESERT-OPS" 
│   └── CID="NAVAL-CONFIG"
│
└── Per ogni Tavola nel catalogo:
    ├── TB_DettagliTavole (Tavola + Versione) → Versioni della tavola
    │   ├── "ROTORS" Versione "00" (stato, descrizioni, figure)
    │   ├── "ROTORS" Versione "01" (stato, descrizioni, figure)
    │   └── "ROTORS" Versione "02" (stato, descrizioni, figure)
    │
    └── TB_Parti (Tavola + PART + ITEM + Versione + CID) → PARTI SPECIFICHE PER CONFIGURAZIONE
        ├── Tavola="ROTORS" PART="BLADE-001" ITEM="10" Ver="01" CID="STANDARD-ARMY"
        ├── Tavola="ROTORS" PART="BLADE-001" ITEM="10" Ver="02" CID="DESERT-OPS"     ← Parte MODIFICATA per deserto!
        ├── Tavola="ROTORS" PART="BOLT-M6"   ITEM="15" Ver="01" CID="STANDARD-ARMY"
        ├── Tavola="ROTORS" PART="BOLT-M6"   ITEM="15" Ver="01" CID="DESERT-OPS"     ← Parte UGUALE per entrambe
        └── Tavola="ROTORS" PART="BLADE-001" ITEM="10" Ver="03" CID="NAVAL-CONFIG"    ← Parte DIVERSA per marina
```

## 🔧 **Implementazione Tecnica**

### **Modelli Coinvolti e Relazioni:**

#### 1. **TB_Cataloghi** (Cataloghi Base)
- **Chiave**: `TER` (Primary)
- **Scopo**: Definisce i cataloghi (es: "HELICOPTER-A129")
- **Campi**: Titoli, Descrizioni (multilingue), Metadati

#### 2. **TB_Composizione** (Collezione Tavole per Catalogo)
- **Chiave**: `TER + Tavola` (Composite)
- **Scopo**: **COLLEGA cataloghi alle tavole** - definisce QUALI tavole appartengono al catalogo
- **Campi**: `nTavola` (ordinamento), `CEDNPagina` (paginazione), Gruppi multilingue
- **Relazione**: Un catalogo → Multiple tavole

#### 3. **TB_CID** (Change Implementation Document)
- **Chiave**: `TER + CID` (Composite)
- **Scopo**: Definisce le **configurazioni/varianti** disponibili per un catalogo
- **Campi**: Titolo, Applicabilità (multilingue), Data
- **Relazione**: Un catalogo → Multiple configurazioni

#### 4. **TB_DettagliTavole** (Versioni delle Tavole)
- **Chiave**: `Tavola + Versione` (Composite)
- **Scopo**: Gestisce le **versioni multiple** di ogni tavola
- **Campi**: Stato (00=Verde, 01=Giallo, 02=Blu, 03=Rosso), Descrizioni, Figure, Date
- **Relazione**: Una tavola → Multiple versioni

#### 5. **TB_Tavole** (Tavole Base) 
- **Chiave**: `Tavola` (Primary)
- **Scopo**: Definisce le tavole base con metadati
- **Campi**: TECHNAME, ICN fields, Gruppi multilingue

#### 6. **TB_Parti** (Parti Specifiche per Configurazione) - **IL CUORE DEL SISTEMA**
- **Chiave**: `Tavola + PART + ITEM + Versione` (Composite)
- **Scopo**: **PARTI SPECIFICHE** per ogni configurazione
- **Campo CID**: `CID` (string) → **COLLEGAMENTO DIRETTO ALLA CONFIGURAZIONE**
- **Relazione**: Una parte → Una configurazione specifica

### **Relazioni Critiche (CORRETTE):**
```sql
-- 1. Catalogo definisce le sue tavole
TB_Composizione: TER + Tavola → "Questa tavola appartiene a questo catalogo"

-- 2. Catalogo definisce le sue configurazioni  
TB_CID: TER + CID → "Questa configurazione esiste per questo catalogo"

-- 3. Tavola ha multiple versioni
TB_DettagliTavole: Tavola + Versione → "Questa versione esiste per questa tavola"

-- 4. COLLEGAMENTO CRUCIALE: Parti collegano tutto insieme
TB_Parti: Tavola + PART + ITEM + Versione + CID → "Questa parte specifica appartiene a questa configurazione"

-- 5. Query completa: Parti per una configurazione specifica
SELECT p.*
FROM TB_Parti p
JOIN TB_Composizione c ON p.Tavola = c.Tavola
WHERE c.TER = 'HELICOPTER-A129' 
  AND p.CID = 'DESERT-OPS'
```

## 💼 **Scenari d'Uso Reali**

### **Esempio: Elicottero Militare A129 (LOGICA CORRETTA)**
```
CATALOGO: TER = "HELICOPTER-A129-MANGUSTA"

📋 TB_Composizione (Tavole nel catalogo):
├── "ROTORS" (nTavola=1.0) 
├── "WEAPONS" (nTavola=2.0)
└── "AVIONICS" (nTavola=3.0)

🏷️ TB_CID (Configurazioni disponibili):
├── CID="STANDARD-ARMY" (Configurazione Esercito Standard)
├── CID="DESERT-OPS" (Configurazione Operazioni Deserto)
└── CID="NAVAL-CONFIG" (Configurazione Marina)

⚙️ TB_Parti (Parti specifiche per configurazione):
Tavola="ROTORS":
├── PART="BLADE-MAIN" ITEM="010" Ver="01" CID="STANDARD-ARMY" ← Pala standard
├── PART="BLADE-MAIN" ITEM="010" Ver="02" CID="DESERT-OPS"    ← Pala rinforzata sabbia
├── PART="BLADE-MAIN" ITEM="010" Ver="03" CID="NAVAL-CONFIG"   ← Pala anti-corrosione
├── PART="BOLT-M8"    ITEM="015" Ver="01" CID="STANDARD-ARMY" ← Bullone standard
├── PART="BOLT-M8"    ITEM="015" Ver="01" CID="DESERT-OPS"    ← STESSO bullone per deserto
└── PART="BOLT-SS"    ITEM="015" Ver="01" CID="NAVAL-CONFIG"   ← Bullone inox per marina

Tavola="WEAPONS":
├── PART="CANNON-20" ITEM="001" Ver="01" CID="STANDARD-ARMY" ← Cannone standard
├── PART="CANNON-20" ITEM="001" Ver="02" CID="DESERT-OPS"    ← Cannone anti-sabbia
└── PART="TORPEDO"   ITEM="001" Ver="01" CID="NAVAL-CONFIG"   ← Siluro solo per marina
```

### **Vantaggi del Sistema:**
1. **Coesistenza**: Multiple configurazioni dello stesso equipaggiamento
2. **Granularità**: Controllo a livello di **singola parte** per ogni configurazione
3. **Tracciabilità**: Ogni cambiamento documentato e collegato tramite CID
4. **Flessibilità**: Parti possono essere condivise tra configurazioni o specifiche
5. **Efficienza**: Riuso delle parti comuni, versioning solo dove necessario
6. **Compliance**: Standard MIL-STD-1388 per documentazione militare

## 🔄 **Workflow Operativo (CORRETTO)**

### **1. Creazione Nuova CID:**
```
1. Tecnico crea nuovo CID "ARCTIC-OPS" per TER "HELICOPTER-A129"
   → Inserisce in TB_CID: TER="HELICOPTER-A129", CID="ARCTIC-OPS"

2. Sistema identifica parti esistenti che necessitano modifiche per ambiente artico

3. Per ogni parte da modificare:
   - Se serve nuova versione tavola → Crea in TB_DettagliTavole
   - Crea/modifica record in TB_Parti con CID="ARCTIC-OPS"

4. Risultato: Parti specifiche per configurazione artica
```

### **2. Modifica Parte Esistente per CID:**
```
1. Richiesta: "Pala rotore per DESERT-OPS deve essere rinforzata"

2. Sistema verifica:
   - TB_Parti: PART="BLADE-MAIN" per CID="DESERT-OPS" esiste?
   - Se sì → Modifica versione esistente
   - Se no → Crea nuova versione

3. Processo CreateNewTableVersion (dal Framework):
   - Crea TB_DettagliTavole: Tavola="ROTORS", Versione="02"
   - Copia TUTTE le TB_Parti dalla versione "01" alla "02"
   - Mantiene CID originale nelle parti copiate
   - Tecnico modifica solo le parti necessarie per CID="DESERT-OPS"

4. Mantiene versioni precedenti intatte per altre CID
```

### **3. Query Parti per CID Specifica:**
```
1. User seleziona Catalogo="HELICOPTER-A129" e CID="DESERT-OPS"

2. Sistema esegue:
   SELECT DISTINCT p.*, dt.*, c.nTavola
   FROM TB_Parti p
   JOIN TB_DettagliTavole dt ON p.Tavola = dt.Tavola AND p.Versione = dt.Versione
   JOIN TB_Composizione c ON p.Tavola = c.Tavola
   WHERE c.TER = 'HELICOPTER-A129' 
     AND p.CID = 'DESERT-OPS'
   ORDER BY c.nTavola, p.PART, p.ITEM

3. Risultato: Solo le parti specifiche per configurazione deserto
```

### **4. Query Complesse dal Framework:**
```sql
-- 1. Ottenere tutte le parti per una CID specifica (dal PartService.cs)
SELECT DISTINCT c.TER as 'CATALOGO',
       p.Tavola, T.TECHNAME, p.PART, p.ITEM, p.Versione, p.QTAV,
       com.DescrIT as 'DESC', p.CID, p.NotaRVT, p.Assieme
FROM [TB_Tavole] T, [TB_Parti] p, [TB_Composizione] c, [TB_Componenti] com, [TB_Cataloghi] ct
WHERE t.Tavola = p.Tavola 
  AND c.Tavola = T.Tavola  
  AND com.PART = p.PART 
  AND ct.TER = c.TER
  AND c.TER = 'HELICOPTER-A129'
  AND p.CID = 'DESERT-OPS'  -- ← Filtro CID specifico

-- 2. Confrontare parti tra due configurazioni
SELECT 
    p1.PART, p1.ITEM, p1.Tavola,
    p1.Versione as 'STANDARD_Version',
    p2.Versione as 'DESERT_Version',
    CASE WHEN p1.Versione = p2.Versione THEN 'IDENTICA' ELSE 'DIVERSA' END as 'Status'
FROM TB_Parti p1
FULL OUTER JOIN TB_Parti p2 ON p1.PART = p2.PART AND p1.ITEM = p2.ITEM AND p1.Tavola = p2.Tavola
JOIN TB_Composizione c1 ON p1.Tavola = c1.Tavola
JOIN TB_Composizione c2 ON p2.Tavola = c2.Tavola
WHERE c1.TER = c2.TER = 'HELICOPTER-A129'
  AND p1.CID = 'STANDARD-ARMY' 
  AND p2.CID = 'DESERT-OPS'

-- 3. Statistiche configurazioni per catalogo
SELECT 
    c.CID,
    COUNT(DISTINCT p.Tavola) as 'Tavole_Utilizzate',
    COUNT(p.PART) as 'Parti_Totali',
    COUNT(DISTINCT p.PART) as 'Parti_Uniche'
FROM TB_CID c
JOIN TB_Parti p ON c.CID = p.CID
JOIN TB_Composizione comp ON p.Tavola = comp.Tavola
WHERE c.TER = comp.TER = 'HELICOPTER-A129'
GROUP BY c.CID
```

## ⚠️ **Implicazioni per lo Sviluppo**

### **Per i Servizi REST API:**
- **CID endpoints**: Gestione configurazioni
- **Tavole endpoints**: Gestione versioni multiple  
- **Parti endpoints**: Filtri per CID + Versione
- **Composizione endpoints**: Mapping CID → Tavole

### **Per l'UI/UX:**
- **Selector CID**: Scelta configurazione attiva
- **Version comparison**: Confronto tra configurazioni
- **Status management**: Stati delle versioni (00=Verde, 01=Giallo, 02=Blu, 03=Rosso)
- **Change tracking**: Storia delle modifiche per CID

### **Per il Business Logic:**
- **Validation rules**: Consistenza tra CID e versioni
- **Dependency management**: Impatti delle modifiche
- **Approval workflows**: Workflow approvazione cambiamenti
- **Export capabilities**: Generazione documenti per configurazione specifica

## 🎯 **Conclusioni**

Il sistema **CID** è il cuore della flessibilità ARDEC, permettendo:
- **Gestione parallela** di multiple configurazioni
- **Tracciabilità completa** dei cambiamenti
- **Compliance** agli standard militari 
- **Scalabilità** per sistemi complessi

Ogni implementazione deve rispettare questa logica per garantire compatibilità con il Framework esistente e correttezza operativa.
