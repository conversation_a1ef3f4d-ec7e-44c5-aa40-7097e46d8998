# CompositionsController - API REST Documentation

## Overview
CompositionsController è il controller REST API per la gestione delle composizioni cataloghi-tavole nel sistema ARDEC Defense. Gestisce operazioni CRUD sul modello TB_Composizione per la struttura cataloghi MIL-STD-1388.

## Endpoints Implementati

### GET Endpoints (Tutti gli utenti autenticati)

#### `GET /api/compositions`
Restituisce tutte le composizioni (vista admin)

#### `GET /api/compositions/catalog/{ter}`
Restituisce tutte le tavole in una composizione di catalogo per TER

#### `GET /api/compositions/catalog/{ter}/ordered`
Restituisce le tavole ordinate per un catalogo specifico

#### `GET /api/compositions/{ter}/{tavola}`
Restituisce una specifica composizione per TER + Tavola

#### `HEAD /api/compositions/{ter}/{tavola}`
Verifica se una composizione esiste

#### `GET /api/compositions/catalog/{ter}/groups/{language}`
Restituisce i gruppi linguistici per funzionalità MAGIRUS

#### `GET /api/compositions/catalog/{ter}/statistics`
Restituisce statistiche di composizione per un catalogo

#### `GET /api/compositions/search?q={query}`
Ricerca nelle composizioni

### POST/PUT Endpoints (Admin, PowerUser, Editor)

#### `POST /api/compositions` (Admin, PowerUser)
Crea nuova composizione - Aggiunge tavola al catalogo

#### `PUT /api/compositions/{ter}/{tavola}` (Admin, PowerUser, Editor)
Aggiorna una composizione esistente

#### `PATCH /api/compositions/{ter}/{tavola}/order` (Admin, PowerUser, Editor)
Aggiorna l'ordine di una tavola nella composizione

### DELETE Endpoints (Admin only)

#### `DELETE /api/compositions/{ter}/{tavola}` (Admin)
Rimuove tavola dal catalogo

## Authorization
- **Tutti gli endpoint di lettura**: Tutti gli utenti autenticati
- **Creazione**: Admin, PowerUser
- **Aggiornamento**: Admin, PowerUser, Editor  
- **Eliminazione**: Solo Admin

## Response Formats
Tutti gli endpoint restituiscono:
- `200 OK` per operazioni riuscite
- `201 Created` per creazioni riuscite
- `400 Bad Request` per errori di validazione
- `401 Unauthorized` per utenti non autenticati
- `403 Forbidden` per autorizzazioni insufficienti
- `404 Not Found` per risorse non trovate
- `409 Conflict` per violazioni di vincoli
- `500 Internal Server Error` per errori server

## Logging
Tutti gli endpoint includono logging strutturato per:
- Operazioni riuscite con conteggio risultati
- Errori con stack trace completo
- Identificazione utente per audit

## Dependencies Injection
Il controller è registrato automaticamente in `ServiceCollectionExtensions.cs` insieme a:
- `ICompositionService` implementato da `CompositionService`
- Supporto per Entity Framework Core
- Logging tramite `ILogger<CompositionsController>`

## Testing
Per testare gli endpoint:
1. Avviare l'applicazione: `dotnet run`
2. Navigare a `/swagger` per l'interfaccia Swagger UI
3. Autenticarsi tramite `/api/account/login`
4. Testare gli endpoint con dati di esempio

## Next Steps
- Implementare unit tests per tutti gli endpoint
- Aggiungere integrazione tests per scenari complessi
- Implementare caching per operazioni di lettura frequenti
- Considerare rate limiting per endpoint pubblici
