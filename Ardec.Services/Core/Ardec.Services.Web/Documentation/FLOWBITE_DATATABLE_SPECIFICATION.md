# ARDEC - Flowbite DataTable Implementation Guide

## Overview
Questa specifica definisce come implementare tabelle moderne con Flowbite nel progetto ARDEC Services, fornendo funzionalità avanzate senza conflitti con jQuery DataTables.

## Architettura

### 1. Dependencies
```html
<!-- Layout.cshtml - HEAD section -->
<link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
```

### 2. Struttura HTML Base

#### Container principale
```html
<div class="card-ardec">
    <div class="card-ardec-body">
        <!-- Controls Section -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center pb-6 bg-white gap-4">
            <!-- Search and Filter Controls -->
        </div>
        
        <!-- Table Section -->
        <div class="overflow-x-auto shadow-sm ring-1 ring-black ring-opacity-5 rounded-lg">
            <!-- Table HTML -->
        </div>
    </div>
    
    <!-- Footer -->
    <div class="card-ardec-footer">
        <!-- Footer content -->
    </div>
</div>
```

#### Controlli di ricerca e filtri
```html
<div class="flex flex-col md:flex-row md:items-center md:space-x-4 w-full">
    <!-- Search Input -->
    <div class="relative mb-4 md:mb-0">
        <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
            <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
            </svg>
        </div>
        <input type="text" id="table-search" class="block w-full md:w-80 py-2.5 pl-4 pr-12 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 placeholder-gray-500" placeholder="Cerca...">
    </div>
    
    <!-- Filter Dropdown -->
    <div class="relative">
        <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown" class="inline-flex items-center justify-center py-2.5 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200" type="button">
            <!-- Filter Icon -->
            Filtri
            <!-- Dropdown Arrow -->
        </button>
        <!-- Dropdown Menu -->
    </div>
</div>
```

#### Struttura tabella
```html
<table id="dataTable" class="w-full table-auto text-sm text-left text-gray-500 divide-y divide-gray-300">
    <thead class="text-xs text-gray-700 uppercase bg-gray-50 divide-y divide-gray-300">
        <tr>
            <!-- Colonna sortable -->
            <th scope="col" class="w-32 px-6 py-3 cursor-pointer hover:bg-gray-100 whitespace-nowrap" onclick="sortTable(0)">
                <div class="flex items-center justify-between">
                    <span>COLUMN_NAME</span>
                    <svg class="w-3 h-3 opacity-30 hover:opacity-100 transition-opacity" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.50a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z"/>
                    </svg>
                </div>
            </th>
            
            <!-- Colonna non-sortable -->
            <th scope="col" class="w-24 px-6 py-3 text-center whitespace-nowrap">COLUMN_NAME</th>
        </tr>
    </thead>
    <tbody>
        <!-- Dynamic content -->
    </tbody>
</table>
```

### 3. JavaScript Implementation

#### Inizializzazione
```javascript
let currentSortColumn = 0;
let sortDirection = 'asc';
let tableData = [];

$(document).ready(function() {
    // Initialize Flowbite components with delay
    setTimeout(() => {
        if (window.Flowbite && typeof window.Flowbite.init === 'function') {
            window.Flowbite.init();
            console.log('✅ Flowbite initialized successfully');
        } else {
            console.warn('⚠️ Flowbite not available, using fallback functionality');
        }
    }, 100);

    // Store original data for filtering/sorting
    tableData = Array.from($('#dataTable tbody tr')).map(row => {
        return {
            element: row,
            data: Array.from(row.cells).map(cell => cell.textContent.trim()),
            id: $(row).data('id') // Attributo identificativo unico
        };
    });

    setupSearch();
    setupFilters();
    
    console.log('✅ Flowbite DataTable initialized successfully!');
});
```

#### Funzione di ricerca
```javascript
function setupSearch() {
    const searchInput = document.getElementById('table-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterTable(searchTerm);
        });
    }
}

function filterTable(searchTerm) {
    tableData.forEach(item => {
        const rowText = item.data.join(' ').toLowerCase();
        const shouldShow = !searchTerm || rowText.includes(searchTerm);
        
        if (shouldShow) {
            item.element.style.display = '';
            $(item.element).removeClass('hidden');
        } else {
            item.element.style.display = 'none';
            $(item.element).addClass('hidden');
        }
    });

    updateVisibleCount();
}
```

#### Funzione di ordinamento
```javascript
function sortTable(columnIndex) {
    // Toggle sort direction if same column
    if (currentSortColumn === columnIndex) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortDirection = 'asc';
        currentSortColumn = columnIndex;
    }

    // Sort the data
    tableData.sort((a, b) => {
        let aVal = a.data[columnIndex].toLowerCase();
        let bVal = b.data[columnIndex].toLowerCase();
        
        // Handle numeric values for sorting
        if (!isNaN(aVal) && !isNaN(bVal)) {
            aVal = parseFloat(aVal) || 0;
            bVal = parseFloat(bVal) || 0;
            return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
        }
        
        // Text sorting
        if (sortDirection === 'asc') {
            return aVal.localeCompare(bVal);
        } else {
            return bVal.localeCompare(aVal);
        }
    });

    // Re-render the table body
    const tbody = document.querySelector('#dataTable tbody');
    tbody.innerHTML = '';
    tableData.forEach(item => {
        tbody.appendChild(item.element);
    });

    updateSortIndicators(columnIndex, sortDirection);
}

function updateSortIndicators(activeColumn, direction) {
    const headers = document.querySelectorAll('#dataTable th.cursor-pointer');
    headers.forEach((header, index) => {
        const svg = header.querySelector('svg');
        if (svg) {
            svg.style.opacity = index === activeColumn ? '1' : '0.3';
            
            if (index === activeColumn) {
                svg.style.transform = direction === 'desc' ? 'rotate(180deg)' : 'rotate(0deg)';
            }
        }
    });
}
```

#### Setup filtri avanzati
```javascript
function setupFilters() {
    // Manual dropdown toggle as fallback for Flowbite
    const dropdownButton = document.getElementById('filterDropdownButton');
    const dropdownMenu = document.getElementById('filterDropdown');
    
    if (dropdownButton && dropdownMenu) {
        dropdownButton.addEventListener('click', function(e) {
            e.preventDefault();
            dropdownMenu.classList.toggle('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!dropdownButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
                dropdownMenu.classList.add('hidden');
            }
        });
    }
}
```

## Stili CSS (Classi Tailwind)

### Container e Layout
- `card-ardec`: Container principale ARDEC
- `card-ardec-body`: Corpo della card con padding
- `card-ardec-footer`: Footer con statistiche

### Tabella
- `w-full table-auto`: Tabella che occupa tutto lo spazio disponibile
- `text-sm text-left text-gray-500`: Styling base testo
- `divide-y divide-gray-300`: Divisori tra righe
- `overflow-x-auto`: Scroll orizzontale responsive

### Header tabella
- `text-xs text-gray-700 uppercase bg-gray-50`: Styling header
- `px-6 py-3`: Padding celle header
- `cursor-pointer hover:bg-gray-100`: Interattività sorting
- `whitespace-nowrap`: Previene wrapping testo

### Larghezze colonne
- `w-32`: Colonne strette (128px)
- `w-28`: Colonne medie (112px) 
- `w-24`: Colonne piccole (96px)
- Senza classe: Colonne che si espandono

### Controlli ricerca
- `py-2.5 pl-4 pr-12`: Padding ottimizzato per icona
- `focus:ring-blue-500 focus:border-blue-500`: Focus styling

## Toast Notifications

### Implementazione
```javascript
function showFlowbiteToast(title, message, type = 'info') {
    const toastId = 'toast-' + Date.now();
    const iconMap = {
        success: 'text-green-500 bg-green-100',
        error: 'text-red-500 bg-red-100', 
        warning: 'text-orange-500 bg-orange-100',
        info: 'text-blue-500 bg-blue-100'
    };
    
    const iconClass = iconMap[type] || iconMap.info;
    
    const toastHtml = `
        <div id="${toastId}" class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow" role="alert">
            <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 ${iconClass} rounded-lg">
                <!-- Success Icon -->
                <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                </svg>
            </div>
            <div class="ml-3 text-sm font-normal">
                <strong>${title}</strong><br/>${message}
            </div>
            <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8" onclick="document.getElementById('${toastId}').remove()">
                <svg class="w-3 h-3"><!-- Close icon --></svg>
            </button>
        </div>
    `;
    
    // Add to toast container
    const container = document.getElementById('toast-container') || createToastContainer();
    container.insertAdjacentHTML('beforeend', toastHtml);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        const toast = document.getElementById(toastId);
        if (toast) toast.remove();
    }, 5000);
}
```

## Best Practices

### Performance
1. **Lazy Loading**: Inizializzare Flowbite con timeout per evitare race conditions
2. **Event Delegation**: Usare delegazione per eventi su righe dinamiche
3. **Debounce Search**: Implementare debounce per ricerca in tempo reale su grandi dataset

### Accessibilità
1. **ARIA Labels**: Sempre includere `aria-label` e `role` appropriati
2. **Keyboard Navigation**: Supportare navigazione da tastiera
3. **Screen Readers**: Usare `sr-only` per testo nascosto ma accessibile

### Responsive Design
1. **Mobile First**: Iniziare con layout mobile e espandere
2. **Breakpoints**: Usare `md:` prefissi per desktop
3. **Overflow Handling**: Sempre includere `overflow-x-auto` per tabelle

## Struttura File

```
Views/
├── [Controller]/
│   └── Index.cshtml          # Vista con tabella Flowbite
├── Shared/
│   ├── _Layout.cshtml        # Include dipendenze Flowbite
│   └── _ToastContainer.cshtml # Container toast notifications
docs/
└── FLOWBITE_DATATABLE_SPECIFICATION.md # Questa specifica
```

## Troubleshooting

### Problemi Comuni

1. **Flowbite.init not a function**
   - Soluzione: Verificare caricamento script e usare setTimeout

2. **Sort indicators non funzionano**
   - Soluzione: Verificare selettori CSS e binding eventi

3. **Search non filtra**
   - Soluzione: Controllare ID elementi e event listeners

4. **Layout mobile rotto**
   - Soluzione: Verificare responsive classes e overflow handling

### Debug
```javascript
// Logging per debug
console.log('Table data:', tableData);
console.log('Flowbite available:', !!window.Flowbite);
console.log('Current sort:', { currentSortColumn, sortDirection });
```

## Esempio Completo

Vedere `Views/Catalogs/Index.cshtml` per implementazione completa di riferimento.

---

**Creato da**: ARDEC Development Team  
**Versione**: 1.0  
**Data**: 29/08/2025  
**Status**: ✅ PRODUZIONE READY
