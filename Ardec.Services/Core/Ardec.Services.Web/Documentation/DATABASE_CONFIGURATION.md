# Database Configuration - Multi-Provider Support

L'applicazione Ardec Services Core supporta ora **tre provider database** diversi per adattarsi a diversi scenari di sviluppo e produzione.

## 🎯 Provider Supportati

### 1. **SQL Server** (Produzione)
- **Uso**: Produzione e sviluppo in ufficio
- **Vantaggi**: Database condiviso con Framework, dati reali
- **Requisiti**: Accesso alla rete aziendale

### 2. **SQLite** (Sviluppo Locale)
- **Uso**: Sviluppo offline, laptop personale
- **Vantaggi**: Nessuna dipendenza esterna, veloce, portatile
- **File**: `Data/ardec-services-dev.db`

### 3. **InMemory** (Testing)
- **Uso**: Unit test, testing automatizzato
- **Vantaggi**: Velocissimo, nessuna persistenza
- **Nota**: Dati persi al riavvio

## ⚙️ Configurazione

### Configurazione Automatica per Environment

#### **Development** (SQLite)
```json
{
  "Database": {
    "Provider": "Sqlite",
    "SqliteFilePath": "Data/ardec-services-dev.db",
    "EnableSensitiveDataLogging": true,
    "EnableDetailedErrors": true,
    "EnsureCreated": true,
    "SeedSampleData": true
  }
}
```

#### **Production** (SQL Server)
```json
{
  "Database": {
    "Provider": "SqlServer",
    "SqlServerConnectionString": "data source=*************\\SQLEXPRESS2;initial catalog=IVECO_DV_PORTING;...",
    "EnableSensitiveDataLogging": false,
    "EnableDetailedErrors": false,
    "EnsureCreated": false,
    "SeedSampleData": false
  }
}
```

#### **Testing** (InMemory)
```json
{
  "Database": {
    "Provider": "InMemory",
    "EnsureCreated": true,
    "SeedSampleData": true
  }
}
```

## 🚀 Utilizzo Pratico

### Sviluppo Offline (SQLite)
```bash
# Automatico in Development environment
dotnet run --environment Development

# Il database SQLite viene creato automaticamente in Data/
# Con dati di esempio per testare l'applicazione
```

### Sviluppo in Ufficio (SQL Server)
```bash
# Modifica appsettings.Development.json
{
  "Database": {
    "Provider": "SqlServer",
    "SqlServerConnectionString": "..."
  }
}

dotnet run --environment Development
```

### Testing (InMemory)
```bash
# Nei test automatizzati
dotnet test

# Il provider InMemory viene configurato automaticamente
# nei test per velocità massima
```

## 📊 Seed Data

### Dati di Esempio Inclusi
- **2 Cataloghi di test**: TEST001, TEST002
- **Supporto multilingue**: ITA/ENG
- **Relazioni RVT**: Esempi di applicabilità
- **Schema completo**: Tutte le tabelle Framework

### Personalizzazione Seed Data
Modifica `DatabaseInitializationService.cs` per aggiungere:
- Più cataloghi di esempio
- Tavole e parti di test
- Dati specifici per il tuo scenario

## 🔧 Troubleshooting

### Database SQLite Non Creato
```bash
# Verifica permessi cartella Data/
mkdir -p Data
chmod 755 Data

# Verifica configurazione
dotnet run --environment Development
```

### Errore Connessione SQL Server
```bash
# Verifica connessione di rete
ping *************

# Testa connection string
sqlcmd -S "*************\SQLEXPRESS2" -U sa -P ardec2013
```

### Performance SQLite
```bash
# Per database grandi, considera indici aggiuntivi
# Modifica ArdecDbContext.OnModelCreating()
```

## 🎯 Vantaggi Implementazione

### ✅ **Flessibilità Totale**
- Sviluppo offline senza SQL Server
- Testing veloce con InMemory
- Produzione robusta con SQL Server

### ✅ **Modelli Framework-Compatible**
- Schema identico al Framework esistente
- Nessuna modifica ai modelli di dominio
- Condivisione database garantita

### ✅ **Seed Data Automatico**
- Dati di test pronti all'uso
- Sviluppo immediato senza setup
- Esempi realistici per UI testing

### ✅ **Configuration-Driven**
- Switch provider senza ricompilazione
- Environment-specific settings
- Deployment semplificato

## 📝 Prossimi Passi

1. **Testare tutti i provider** con l'applicazione esistente
2. **Aggiungere più seed data** per scenari complessi
3. **Configurare CI/CD** con provider appropriati
4. **Documentare migration strategy** per team
