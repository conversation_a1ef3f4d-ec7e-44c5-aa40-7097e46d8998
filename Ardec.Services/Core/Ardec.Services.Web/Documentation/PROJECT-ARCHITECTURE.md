# ARDEC Services Web - Project Architecture
## Aggiornato: 3 Settembre 2025

## 📁 Struttura del Progetto Riorganizzata

La struttura del progetto è stata riorganizzata per maggiore chiarezza e separazione delle responsabilità:

```
Ardec.Services.Web/
├── Controllers/           # Tutti i controller
│   ├── Mvc/              # MVC Controllers per Views (UI Web)
│   │   ├── CatalogsController.cs     # CRUD UI per Cataloghi
│   │   └── HomeController.cs         # Dashboard e pagine generali
│   │
│   └── Api/              # API REST Controllers
│       ├── CatalogsController.cs     # REST API per Cataloghi
│       ├── PartsController.cs        # REST API per Parti
│       ├── TablesController.cs       # REST API per Tavole
│       ├── CompositionsController.cs # REST API per Composizioni
│       └── ... (altri API controllers)
│
├── Services/             # Business Logic Services
│   ├── Catalogs/
│   │   ├── ICatalogService.cs
│   │   └── CatalogService.cs
│   ├── Parts/
│   │   ├── IPartService.cs
│   │   └── PartService.cs
│   ├── Tables/
│   │   ├── ITableService.cs
│   │   └── TableService.cs
│   └── ... (altri services)
│
├── Views/                # Razor Views (UI)
│   ├── Catalogs/
│   │   ├── Index.cshtml       # Lista cataloghi
│   │   ├── Create.cshtml      # Form creazione
│   │   ├── Edit.cshtml        # Form modifica
│   │   ├── Details.cshtml     # Vista dettaglio
│   │   └── Delete.cshtml      # Conferma eliminazione
│   ├── Home/
│   └── Shared/
│
├── Data/                 # Entity Framework
│   ├── Models/          # Entità DB (ESATTE dal Framework!)
│   │   ├── TB_Cataloghi.cs
│   │   ├── TB_Parti.cs
│   │   ├── TB_Tavole.cs
│   │   └── ...
│   └── ArdecDbContext.cs
│
├── Shared/               # Componenti condivisi
│   ├── Configuration/
│   ├── Extensions/
│   └── Services/
│
└── wwwroot/             # Asset statici
    ├── css/
    ├── js/
    └── images/
```

## 🎯 Architettura e Pattern

### Separazione delle Responsabilità

1. **Controllers/Mvc/** - Controller MVC tradizionali per le UI web
   - Gestiscono le Views Razor
   - Ritornano `IActionResult` con HTML
   - Usano `[Route("[controller]")]` per routing convenzionale
   - Esempio: `/Catalogs/Index`, `/Catalogs/Create`

2. **Controllers/Api/** - Controller REST API
   - Decorati con `[ApiController]`
   - Ritornano JSON/XML
   - Usano `[Route("api/[controller]")]`
   - Esempio: `GET /api/catalogs`, `POST /api/catalogs`

3. **Services/** - Business Logic Layer
   - Interfacce + Implementazioni
   - Logica di business separata dai controller
   - Riutilizzabile sia da MVC che da API
   - Accesso diretto a DbContext (no Repository pattern)

## 📊 Stato Implementazione Attuale

### ✅ Entità con API Complete
- TB_Cataloghi (MVC + REST API)
- TB_Parti (REST API)
- TB_Tavole (REST API)
- TB_Composizione (REST API)
- TB_CID (REST API)
- TB_CataloghiRVT (REST API)
- TB_Componenti (REST API)
- TB_DescrizioniCustom (REST API)
- TB_Subfornitori (REST API)
- TB_RVT (REST API)

### ✅ UI Web Implementate
- Catalogs:
  - Index.cshtml ✅
  - Create.cshtml ✅
  - Edit.cshtml ✅
  - Details.cshtml ✅
  - Delete.cshtml ✅
- Home:
  - Index.cshtml ✅
  - Dashboard.cshtml ✅

### 🔄 UI Da Implementare
- Tables (Tavole)
- Parts (Parti)
- Compositions (Composizioni)
- Altri moduli

## 🔧 Configurazione e Dependency Injection

### Service Registration (Program.cs)
```csharp
// Services registration
builder.Services.AddScoped<ICatalogService, CatalogService>();
builder.Services.AddScoped<IPartService, PartService>();
builder.Services.AddScoped<ITableService, TableService>();
// ... altri services
```

### Namespace Convention
- **MVC Controllers**: `Ardec.Services.Web.Controllers.Mvc`
- **API Controllers**: `Ardec.Services.Web.Controllers.Api`
- **Services**: `Ardec.Services.Web.Services.[Feature]`
- **Models**: `Ardec.Services.Web.Data.Models`

## 🌐 API Endpoints

### Catalogs API
- `GET /api/catalogs` - Lista tutti i cataloghi
- `GET /api/catalogs/{ter}` - Dettaglio catalogo
- `POST /api/catalogs` - Crea nuovo catalogo
- `PUT /api/catalogs/{ter}` - Aggiorna catalogo
- `DELETE /api/catalogs/{ter}` - Elimina catalogo
- `GET /api/catalogs/search?q={term}` - Ricerca cataloghi
- `GET /api/catalogs/statistics` - Statistiche cataloghi

### MVC Routes
- `/Catalogs` - Lista cataloghi (UI)
- `/Catalogs/Details/{id}` - Dettagli catalogo
- `/Catalogs/Create` - Form creazione
- `/Catalogs/Edit/{id}` - Form modifica
- `/Catalogs/Delete/{id}` - Conferma eliminazione

## 🔒 Autorizzazione

### API Controllers
- Tutti richiedono autenticazione base `[Authorize]`
- Operazioni CRUD limitate per ruolo:
  - `Create/Delete`: Admin, PowerUser
  - `Update`: Admin, PowerUser, Editor
  - `Read`: Tutti gli utenti autenticati

### MVC Controllers
- Richiedono autenticazione `[Authorize]`
- Controllo ruoli a livello di action

## 📝 Note Importanti

### ⚠️ REGOLA FONDAMENTALE
**I modelli in Data/Models DEVONO essere IDENTICI al Framework!**
- MAI modificare i campi
- MAI aggiungere proprietà
- Il database è condiviso con l'app desktop

### Vantaggi della Riorganizzazione
1. **Chiarezza**: Immediato capire cosa sono API e cosa MVC
2. **Manutenibilità**: Services separati e riutilizzabili
3. **Scalabilità**: Facile aggiungere nuove features
4. **Testing**: Più facile testare services isolati
5. **Documentazione**: Struttura auto-documentante

### Migration Path
Per aggiungere una nuova feature:
1. Creare il Service in `Services/[Feature]/`
2. Creare l'API Controller in `Controllers/Api/`
3. Creare il MVC Controller in `Controllers/Mvc/`
4. Creare le Views in `Views/[Feature]/`
5. Registrare il service in Program.cs

## 🚀 Prossimi Passi

1. Completare namespace update per tutti i controller
2. Testare compilazione completa
3. Verificare tutti i riferimenti nei file
4. Aggiornare ServiceCollectionExtensions.cs
5. Testare funzionalità end-to-end
