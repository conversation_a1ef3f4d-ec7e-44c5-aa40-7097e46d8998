{"ConnectionStrings": {"DefaultConnection": "data source=*************\\SQLEXPRESS2;initial catalog=IVECO_DV_PORTING;persist security info=True;user id=sa;password=*********;MultipleActiveResultSets=True;TrustServerCertificate=True"}, "Database": {"Provider": "SqlServer", "SqlServerConnectionString": "data source=*************\\SQLEXPRESS2;initial catalog=IVECO_DV_PORTING;persist security info=True;user id=sa;password=*********;MultipleActiveResultSets=True;TrustServerCertificate=True", "SqliteFilePath": "Data/ardec-services.db", "EnableSensitiveDataLogging": false, "EnableDetailedErrors": false, "CommandTimeoutSeconds": 120, "EnsureCreated": false, "SeedSampleData": false}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "ardec.com", "TenantId": "your-tenant-id", "ClientId": "your-client-id", "ClientSecret": "your-client-secret", "CallbackPath": "/signin-oidc"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/ardec-services-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "AllowedHosts": "*", "Application": {"Name": "MIL-STD-1388 Services", "Version": "2.0.0", "Environment": "Development"}, "Features": {"EnableSignalR": true, "EnableSwagger": true, "EnableFileUploads": true, "MaxFileUploadSizeMB": 50}, "Storage": {"ImagesBasePath": "\\\\*************\\Images\\", "TempPath": "temp/", "ExportPath": "exports/"}}