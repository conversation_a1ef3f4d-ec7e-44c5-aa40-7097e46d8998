{"Database": {"Provider": "InMemory", "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "CommandTimeoutSeconds": 30, "EnsureCreated": true, "SeedSampleData": true}, "Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Error", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Error"}}}, "Features": {"EnableSignalR": false, "EnableSwagger": false, "EnableFileUploads": false, "MaxFileUploadSizeMB": 1}}