using System.ComponentModel.DataAnnotations;

namespace Ardec.Services.Web.DTOs.DescrizioniCustom;

/// <summary>
/// DTO per la creazione di una nuova descrizione custom
/// </summary>
public record CreateDescrizioniCustomRequest
{
    [Required]
    [StringLength(255, ErrorMessage = "PART non può superare i 255 caratteri")]
    public required string PART { get; init; }

    [Required]
    [StringLength(155, ErrorMessage = "TER non può superare i 155 caratteri")]
    public required string TER { get; init; }

    public string? DescrIT { get; init; }
    public string? DescrES { get; init; }
    public string? DescrFR { get; init; }
    public string? DescrEN { get; init; }
    public string? DescrPORT { get; init; }
    public string? DescrTED { get; init; }
    public string? DescrUSA { get; init; }
}

/// <summary>
/// DTO per l'aggiornamento di una descrizione custom esistente
/// </summary>
public record UpdateDescrizioniCustomRequest
{
    public string? DescrIT { get; init; }
    public string? DescrES { get; init; }
    public string? DescrFR { get; init; }
    public string? DescrEN { get; init; }
    public string? DescrPORT { get; init; }
    public string? DescrTED { get; init; }
    public string? DescrUSA { get; init; }
}

/// <summary>
/// DTO di risposta per TB_DescrizioniCustom
/// </summary>
public record DescrizioniCustomResponse
{
    public required string PART { get; init; }
    public required string TER { get; init; }
    public string? DescrIT { get; init; }
    public string? DescrES { get; init; }
    public string? DescrFR { get; init; }
    public string? DescrEN { get; init; }
    public string? DescrPORT { get; init; }
    public string? DescrTED { get; init; }
    public string? DescrUSA { get; init; }
}

/// <summary>
/// DTO per la ricerca e filtri di TB_DescrizioniCustom
/// </summary>
public record DescrizioniCustomSearchRequest
{
    public string? PART { get; init; }
    public string? TER { get; init; }
    public string? SearchTerm { get; init; }
    public string? Language { get; init; } = "IT"; // Default language for search
    public bool OnlyWithDescriptions { get; init; } = false; // Filtra solo record con almeno una descrizione
    public int Skip { get; init; } = 0;
    public int Take { get; init; } = 20;
    public string SortBy { get; init; } = "PART";
    public bool SortDescending { get; init; } = false;
}

/// <summary>
/// DTO per risultati paginati di TB_DescrizioniCustom
/// </summary>
public record DescrizioniCustomPagedResult
{
    public required List<DescrizioniCustomResponse> Items { get; init; }
    public required int TotalCount { get; init; }
    public required int Skip { get; init; }
    public required int Take { get; init; }
    public bool HasNext => Skip + Take < TotalCount;
    public bool HasPrevious => Skip > 0;
}

/// <summary>
/// DTO per le statistiche di TB_DescrizioniCustom
/// </summary>
public record DescrizioniCustomStatsResponse
{
    public int TotalRecords { get; init; }
    public int UniqueParts { get; init; }
    public int UniqueTER { get; init; }
    public int RecordsWithDescriptions { get; init; }
    public int MultiLanguageRecords { get; init; }
    public List<LanguageCustomStats> LanguageDistribution { get; init; } = [];
    public List<string> TopParts { get; init; } = [];
    public List<string> TopTER { get; init; } = [];
    public Dictionary<string, int> PartsByTER { get; init; } = new();
}

/// <summary>
/// Statistiche per lingua per le descrizioni custom
/// </summary>
public record LanguageCustomStats
{
    public required string Language { get; init; }
    public int DescriptionCount { get; init; }
    public double Percentage { get; init; }
}

/// <summary>
/// DTO per la ricerca di descrizioni per una parte specifica
/// </summary>
public record PartDescriptionsRequest
{
    public required string PART { get; init; }
    public string? Language { get; init; } = "IT";
    public bool IncludeAllLanguages { get; init; } = false;
}

/// <summary>
/// DTO per la risposta delle descrizioni di una parte
/// </summary>
public record PartDescriptionsResponse
{
    public required string PART { get; init; }
    public required List<TERDescriptionInfo> Descriptions { get; init; }
}

/// <summary>
/// Informazioni descrizione per TER
/// </summary>
public record TERDescriptionInfo
{
    public required string TER { get; init; }
    public string? DescrIT { get; init; }
    public string? DescrES { get; init; }
    public string? DescrFR { get; init; }
    public string? DescrEN { get; init; }
    public string? DescrPORT { get; init; }
    public string? DescrTED { get; init; }
    public string? DescrUSA { get; init; }
    public string? PreferredDescription { get; init; }
}

/// <summary>
/// DTO per l'aggiornamento batch di descrizioni
/// </summary>
public record BatchUpdateDescrizioniCustomRequest
{
    public required List<UpdateDescrizioniCustomItem> Updates { get; init; }
}

/// <summary>
/// Item per aggiornamento batch
/// </summary>
public record UpdateDescrizioniCustomItem
{
    public required string PART { get; init; }
    public required string TER { get; init; }
    public string? DescrIT { get; init; }
    public string? DescrES { get; init; }
    public string? DescrFR { get; init; }
    public string? DescrEN { get; init; }
    public string? DescrPORT { get; init; }
    public string? DescrTED { get; init; }
    public string? DescrUSA { get; init; }
}
