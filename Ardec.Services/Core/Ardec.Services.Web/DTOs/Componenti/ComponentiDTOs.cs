using System.ComponentModel.DataAnnotations;

namespace Ardec.Services.Web.DTOs.Componenti;

/// <summary>
/// DTO per la creazione di un nuovo componente
/// </summary>
public record CreateComponentiRequest
{
    [Required]
    [StringLength(255, ErrorMessage = "PART non può superare i 255 caratteri")]
    public required string PART { get; init; }

    // Descrizioni multilingue
    public string? DescrIT { get; init; }
    public string? DescrES { get; init; }
    public string? DescrEN { get; init; }
    public string? DescrPORT { get; init; }
    public string? DescrFR { get; init; }
    public string? DescrTED { get; init; }
    public string? DescrUSA { get; init; }

    // Note multilingue
    public string? NotaIT { get; init; }
    public string? NotaES { get; init; }
    public string? NotaEN { get; init; }
    public string? NotaPORT { get; init; }
    public string? NotaFR { get; init; }
    public string? NotaTED { get; init; }
    public string? NotaUSA { get; init; }

    // Codici identificativi
    [StringLength(255, ErrorMessage = "NUC non può superare i 255 caratteri")]
    public string? NUC { get; init; }

    [StringLength(255, ErrorMessage = "CodF non può superare i 255 caratteri")]
    public string? CodF { get; init; }

    [StringLength(255, ErrorMessage = "PARTF non può superare i 255 caratteri")]
    public string? PARTF { get; init; }

    [StringLength(255, ErrorMessage = "Tabella non può superare i 255 caratteri")]
    public string? Tabella { get; init; }

    [StringLength(255, ErrorMessage = "Origine non può superare i 255 caratteri")]
    public string? Origine { get; init; }

    [StringLength(255, ErrorMessage = "TONumber non può superare i 255 caratteri")]
    public string? TONumber { get; init; }
}

/// <summary>
/// DTO per l'aggiornamento di un componente esistente
/// </summary>
public record UpdateComponentiRequest
{
    // Descrizioni multilingue
    public string? DescrIT { get; init; }
    public string? DescrES { get; init; }
    public string? DescrEN { get; init; }
    public string? DescrPORT { get; init; }
    public string? DescrFR { get; init; }
    public string? DescrTED { get; init; }
    public string? DescrUSA { get; init; }

    // Note multilingue
    public string? NotaIT { get; init; }
    public string? NotaES { get; init; }
    public string? NotaEN { get; init; }
    public string? NotaPORT { get; init; }
    public string? NotaFR { get; init; }
    public string? NotaTED { get; init; }
    public string? NotaUSA { get; init; }

    // Codici identificativi
    [StringLength(255, ErrorMessage = "NUC non può superare i 255 caratteri")]
    public string? NUC { get; init; }

    [StringLength(255, ErrorMessage = "CodF non può superare i 255 caratteri")]
    public string? CodF { get; init; }

    [StringLength(255, ErrorMessage = "PARTF non può superare i 255 caratteri")]
    public string? PARTF { get; init; }

    [StringLength(255, ErrorMessage = "Tabella non può superare i 255 caratteri")]
    public string? Tabella { get; init; }

    [StringLength(255, ErrorMessage = "Origine non può superare i 255 caratteri")]
    public string? Origine { get; init; }

    [StringLength(255, ErrorMessage = "TONumber non può superare i 255 caratteri")]
    public string? TONumber { get; init; }
}

/// <summary>
/// DTO di risposta per TB_Componenti
/// </summary>
public record ComponentiResponse
{
    public required string PART { get; init; }

    // Descrizioni multilingue
    public string? DescrIT { get; init; }
    public string? DescrES { get; init; }
    public string? DescrEN { get; init; }
    public string? DescrPORT { get; init; }
    public string? DescrFR { get; init; }
    public string? DescrTED { get; init; }
    public string? DescrUSA { get; init; }

    // Note multilingue
    public string? NotaIT { get; init; }
    public string? NotaES { get; init; }
    public string? NotaEN { get; init; }
    public string? NotaPORT { get; init; }
    public string? NotaFR { get; init; }
    public string? NotaTED { get; init; }
    public string? NotaUSA { get; init; }

    // Codici identificativi
    public string? NUC { get; init; }
    public string? CodF { get; init; }
    public string? PARTF { get; init; }
    public string? Tabella { get; init; }
    public string? Origine { get; init; }
    public string? TONumber { get; init; }
}

/// <summary>
/// DTO per la ricerca e filtri di TB_Componenti
/// </summary>
public record ComponentiSearchRequest
{
    public string? PART { get; init; }
    public string? NUC { get; init; }
    public string? CodF { get; init; }
    public string? PARTF { get; init; }
    public string? Tabella { get; init; }
    public string? Origine { get; init; }
    public string? TONumber { get; init; }
    public string? SearchTerm { get; init; }
    public string? Language { get; init; } = "IT"; // Default language for search
    public int Skip { get; init; } = 0;
    public int Take { get; init; } = 20;
    public string SortBy { get; init; } = "PART";
    public bool SortDescending { get; init; } = false;
}

/// <summary>
/// DTO per risultati paginati di TB_Componenti
/// </summary>
public record ComponentiPagedResult
{
    public required List<ComponentiResponse> Items { get; init; }
    public required int TotalCount { get; init; }
    public required int Skip { get; init; }
    public required int Take { get; init; }
    public bool HasNext => Skip + Take < TotalCount;
    public bool HasPrevious => Skip > 0;
}

/// <summary>
/// DTO per le statistiche di TB_Componenti
/// </summary>
public record ComponentiStatsResponse
{
    public int TotalComponents { get; init; }
    public int ComponentsWithDescriptions { get; init; }
    public int ComponentsWithNotes { get; init; }
    public int ComponentsWithNUC { get; init; }
    public int ComponentsWithCodF { get; init; }
    public int ComponentsWithPARTF { get; init; }
    public int ComponentsWithTONumber { get; init; }
    public int MultiLanguageComponents { get; init; }
    public List<string> TopOrigins { get; init; } = [];
    public List<string> TopTables { get; init; } = [];
    public List<LanguageStats> LanguageDistribution { get; init; } = [];
}

/// <summary>
/// Statistiche per lingua
/// </summary>
public record LanguageStats
{
    public required string Language { get; init; }
    public int DescriptionCount { get; init; }
    public int NoteCount { get; init; }
}
