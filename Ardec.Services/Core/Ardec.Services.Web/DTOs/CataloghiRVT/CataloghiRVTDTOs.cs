using System.ComponentModel.DataAnnotations;

namespace Ardec.Services.Web.DTOs.CataloghiRVT;

/// <summary>
/// DTO per la creazione di un nuovo record TB_CataloghiRVT
/// </summary>
public record CreateCataloghiRVTRequest
{
    [Required]
    [StringLength(155, ErrorMessage = "TER non può superare i 155 caratteri")]
    public required string TER { get; init; }

    [Required]
    [StringLength(255, ErrorMessage = "RVT non può superare i 255 caratteri")]
    public required string RVT { get; init; }

    [StringLength(255, ErrorMessage = "Applicabilità non può superare i 255 caratteri")]
    public string? Applicabilità { get; init; }

    public string? ApplicabilitàEN { get; init; }
    public string? ApplicabilitàES { get; init; }
    public string? ApplicabilitàPT { get; init; }
    public string? ApplicabilitàFR { get; init; }
    public string? ApplicabilitàUSA { get; init; }
}

/// <summary>
/// DTO per l'aggiornamento di un record TB_CataloghiRVT
/// </summary>
public record UpdateCataloghiRVTRequest
{
    [Required]
    [StringLength(255, ErrorMessage = "RVT non può superare i 255 caratteri")]
    public required string RVT { get; init; }

    [StringLength(255, ErrorMessage = "Applicabilità non può superare i 255 caratteri")]
    public string? Applicabilità { get; init; }

    public string? ApplicabilitàEN { get; init; }
    public string? ApplicabilitàES { get; init; }
    public string? ApplicabilitàPT { get; init; }
    public string? ApplicabilitàFR { get; init; }
    public string? ApplicabilitàUSA { get; init; }
}

/// <summary>
/// DTO di risposta per TB_CataloghiRVT
/// </summary>
public record CataloghiRVTResponse
{
    public required string TER { get; init; }
    public required string RVT { get; init; }
    public string? Applicabilità { get; init; }
    public string? ApplicabilitàEN { get; init; }
    public string? ApplicabilitàES { get; init; }
    public string? ApplicabilitàPT { get; init; }
    public string? ApplicabilitàFR { get; init; }
    public string? ApplicabilitàUSA { get; init; }
}

/// <summary>
/// DTO per la ricerca e filtri di TB_CataloghiRVT
/// </summary>
public record CataloghiRVTSearchRequest
{
    public string? TER { get; init; }
    public string? RVT { get; init; }
    public string? Applicabilità { get; init; }
    public string? SearchTerm { get; init; }
    public int Skip { get; init; } = 0;
    public int Take { get; init; } = 20;
    public string SortBy { get; init; } = "TER";
    public bool SortDescending { get; init; } = false;
}

/// <summary>
/// DTO per risultati paginati di TB_CataloghiRVT
/// </summary>
public record CataloghiRVTPagedResult
{
    public required List<CataloghiRVTResponse> Items { get; init; }
    public required int TotalCount { get; init; }
    public required int Skip { get; init; }
    public required int Take { get; init; }
    public bool HasNext => Skip + Take < TotalCount;
    public bool HasPrevious => Skip > 0;
}
