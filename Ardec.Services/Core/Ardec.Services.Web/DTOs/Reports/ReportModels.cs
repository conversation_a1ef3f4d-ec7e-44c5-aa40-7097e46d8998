using System.ComponentModel.DataAnnotations;

namespace Ardec.Services.Web.DTOs.Reports;

/// <summary>
/// Richiesta per l'export di un catalogo in Excel
/// </summary>
public class ExportCatalogRequest
    {
        /// <summary>
        /// TER del catalogo da esportare
        /// </summary>
        [Required]
        public string TER { get; set; } = string.Empty;

        /// <summary>
        /// Se esportare in formato S1000D
        /// </summary>
        public bool IsS1000D { get; set; } = false;

        /// <summary>
        /// Se includere immagini mancanti nel report
        /// </summary>
        public bool CheckMissingImages { get; set; } = true;
    }

    /// <summary>
    /// Richiesta per l'export di Data Modules XML
    /// </summary>
    public class ExportDMRequest
    {
        /// <summary>
        /// Lista delle tavole da esportare
        /// </summary>
        [Required]
        [MinLength(1, ErrorMessage = "Almeno una tavola deve essere specificata")]
        public List<string> Tavole { get; set; } = new();

        /// <summary>
        /// TER del catalogo
        /// </summary>
        [Required]
        public string TER { get; set; } = string.Empty;

        /// <summary>
        /// Lingua per l'export (Italian, English, French, German, Spanish, Portuguese, USA English)
        /// </summary>
        [Required]
        public string Language { get; set; } = "Italian";

        /// <summary>
        /// Parametri opzionali per l'export
        /// </summary>
        public DMExportOptions? Options { get; set; }
    }

    /// <summary>
    /// Opzioni aggiuntive per l'export DM
    /// </summary>
    public class DMExportOptions
    {
        /// <summary>
        /// Numero inwork (default: "00")
        /// </summary>
        public string InWork { get; set; } = "00";

        /// <summary>
        /// Testo RFU (Reason for Update)
        /// </summary>
        public string RFU { get; set; } = "Aggiornamento tecnico - Scatta issue per inserimento SMRCODE";

        /// <summary>
        /// Tipo documento (default: "zzz")
        /// </summary>
        public string Type { get; set; } = "zzz";

        /// <summary>
        /// Numero issue
        /// </summary>
        public string IssueNumber { get; set; } = string.Empty;
    }

    /// <summary>
    /// Richiesta per l'export CSV
    /// </summary>
    public class ExportCsvRequest
    {
        /// <summary>
        /// TER del catalogo
        /// </summary>
        [Required]
        public string TER { get; set; } = string.Empty;

        /// <summary>
        /// Tipo di export (tavole, parti, singola-tavola)
        /// </summary>
        [Required]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Nome della tavola (obbligatorio per tipo 'singola-tavola')
        /// </summary>
        public string? Tavola { get; set; }

        /// <summary>
        /// Se includere colonne specifiche S1000D
        /// </summary>
        public bool IsS1000D { get; set; } = false;
    }

    /// <summary>
    /// Risultato di un export
    /// </summary>
    public class ExportResult
    {
        /// <summary>
        /// Contenuto del file
        /// </summary>
        public byte[] FileContent { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// Nome del file
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Tipo MIME del file
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// Eventuale file aggiuntivo per immagini mancanti
        /// </summary>
        public ExportResult? AdditionalFile { get; set; }
    }

    /// <summary>
    /// Informazioni su un catalogo disponibile
    /// </summary>
    public class CatalogInfo
    {
        /// <summary>
        /// TER del catalogo
        /// </summary>
        public string TER { get; set; } = string.Empty;

        /// <summary>
        /// Titolo del catalogo
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Descrizione breve
        /// </summary>
        public string ShortTitle { get; set; } = string.Empty;

        /// <summary>
        /// Se è un catalogo S1000D
        /// </summary>
        public bool IsS1000D { get; set; } = false;

        /// <summary>
        /// Numero di tavole nel catalogo
        /// </summary>
        public int TablesCount { get; set; } = 0;

        /// <summary>
        /// Data ultima modifica
        /// </summary>
        public DateTime? LastModified { get; set; }
    }

    /// <summary>
    /// Informazioni su una tavola
    /// </summary>
    public class TableInfo
    {
        /// <summary>
        /// Nome della tavola
        /// </summary>
        public string Tavola { get; set; } = string.Empty;

        /// <summary>
        /// Codice tecnico
        /// </summary>
        public string CodiceTecnico { get; set; } = string.Empty;

        /// <summary>
        /// Descrizione della tavola
        /// </summary>
        public string Descrizione { get; set; } = string.Empty;

        /// <summary>
        /// DMC per S1000D
        /// </summary>
        public string? DMC { get; set; }

        /// <summary>
        /// TECHNAME per S1000D
        /// </summary>
        public string? TechName { get; set; }

        /// <summary>
        /// Numero di parti nella tavola
        /// </summary>
        public int PartsCount { get; set; } = 0;
    }

    /// <summary>
    /// Lingue supportate per l'export DM
    /// </summary>
    public static class SupportedLanguages
    {
        public const string Italian = "Italian";
        public const string English = "English";
        public const string French = "French";
        public const string German = "German";
        public const string Spanish = "Spanish";
        public const string Portuguese = "Portuguese";
        public const string USAEnglish = "USA English";

        public static readonly string[] All = {
            Italian, English, French, German, Spanish, Portuguese, USAEnglish
        };

        public static readonly Dictionary<string, string> CountryCodes = new()
        {
            { Italian, "IT" },
            { English, "UK" },
            { French, "FR" },
            { German, "DE" },
            { Spanish, "ES" },
            { Portuguese, "PT" },
            { USAEnglish, "US" }
        };

        public static readonly Dictionary<string, string> LanguageCodes = new()
        {
            { Italian, "it" },
            { English, "en" },
            { French, "fr" },
            { German, "de" },
            { Spanish, "es" },
            { Portuguese, "pt" },
            { USAEnglish, "en" }
        };
    }

    /// <summary>
    /// Tipi di export CSV supportati
    /// </summary>
    public static class CsvExportTypes
    {
        public const string Tavole = "tavole";
        public const string Parti = "parti";
        public const string SingolaTavola = "singola-tavola";

        public static readonly string[] All = { Tavole, Parti, SingolaTavola };
    }
