using System.ComponentModel.DataAnnotations;

namespace Ardec.Services.Web.DTOs.Parts;

/// <summary>
/// Search criteria for parts
/// </summary>
public record PartSearchCriteria
{
    public string? Tavola { get; init; }
    public string? PartNumber { get; init; }
    public string? Item { get; init; }
    public string? Csnref { get; init; }
    public string? Lcn { get; init; }
    public string? Assieme { get; init; }
    public bool? ILS { get; init; }
    public string? Versione { get; init; }
    public int Skip { get; init; } = 0;
    public int Take { get; init; } = 20;
}
