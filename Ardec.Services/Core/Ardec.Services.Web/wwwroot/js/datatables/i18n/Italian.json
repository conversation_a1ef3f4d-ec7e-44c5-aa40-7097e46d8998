{"aria": {"sortAscending": ": attiva per ordinare la colonna in ordine crescente", "sortDescending": ": attiva per ordinare la colonna in ordine decrescente"}, "autoFill": {"cancel": "<PERSON><PERSON><PERSON>", "fill": "<PERSON><PERSON><PERSON><PERSON> tutte le celle con <i>%d</i>", "fillHorizontal": "Riempi celle orizzontalmente", "fillVertical": "Riempi celle verticalmente"}, "buttons": {"collection": "Collezione <span class=\"ui-button-icon-primary ui-icon ui-icon-triangle-1-s\"></span>", "colvis": "Visibilità Colonna", "colvisRestore": "Ripristina visibil<PERSON>", "copy": "Copia", "copyKeys": "Premi ctrl o u2318 + C per copiare i dati della tabella nella clipboard di sistema.<br /><br />Per annullare, clicca su questo messaggio o premi ESC.", "copySuccess": {"1": "Copiata 1 riga nella clipboard", "_": "Copiate %d righe nella clipboard"}, "copyTitle": "Copia nella Clipboard", "csv": "CSV", "excel": "Excel", "pageLength": {"-1": "<PERSON>ra tutte le righe", "_": "Mostra %d righe"}, "pdf": "PDF", "print": "Stampa", "renameState": "Rinomina", "updateState": "Aggiorna"}, "datetime": {"previous": "Precedente", "next": "Prossimo", "hours": "Ore", "minutes": "Minuti", "seconds": "Secondi", "unknown": "-", "amPm": ["AM", "PM"], "months": {"0": "Gennaio", "1": "<PERSON><PERSON><PERSON>", "10": "Novembre", "11": "Dicembre", "2": "<PERSON><PERSON>", "3": "<PERSON>e", "4": "Maggio", "5": "<PERSON><PERSON><PERSON>", "6": "<PERSON><PERSON><PERSON>", "7": "Agosto", "8": "Settembre", "9": "Ottobre"}, "weekdays": ["Dom", "<PERSON>n", "Mar", "<PERSON><PERSON>", "Gio", "Ven", "<PERSON>b"]}, "decimal": ",", "emptyTable": "<PERSON><PERSON><PERSON> dato disponibile nella tabella", "info": "Mostra da _START_ a _END_ di _TOTAL_ elementi", "infoEmpty": "Mostra da 0 a 0 di 0 elementi", "infoFiltered": "(filtrato da _MAX_ elementi totali)", "infoPostFix": "", "thousands": ".", "lengthMenu": "Mostra _MENU_ elementi", "loadingRecords": "Caricamento...", "paginate": {"first": "Primo", "last": "Ultimo", "next": "Prossimo", "previous": "Precedente"}, "processing": "Elaborazione...", "search": "Cerca:", "searchBuilder": {"add": "Aggiungi Condizione", "button": {"0": "Generatore di Ricerca", "_": "Generatore di Ricerca (%d)"}, "clearAll": "<PERSON><PERSON><PERSON>", "condition": "Condizione", "conditions": {"date": {"after": "<PERSON><PERSON>", "before": "Prima", "between": "Tra", "empty": "<PERSON><PERSON><PERSON>", "equals": "Uguale a", "notBetween": "Non tra", "notEmpty": "Non vuoto", "not": "Non"}, "number": {"between": "Tra", "empty": "<PERSON><PERSON><PERSON>", "equals": "Uguale a", "gt": "Maggiore di", "gte": "Maggiore o uguale a", "lt": "<PERSON>e di", "lte": "Minore o uguale a", "notBetween": "Non tra", "notEmpty": "Non vuoto", "not": "Non"}, "string": {"contains": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "endsWith": "Finisce con", "equals": "Uguale a", "notEmpty": "Non vuoto", "startsWith": "Inizia con", "not": "Non", "notContains": "Non contiene", "notStartsWith": "Non inizia con", "notEndsWith": "Non finisce con"}, "array": {"not": "Non", "equals": "Uguale a", "empty": "<PERSON><PERSON><PERSON>", "contains": "<PERSON><PERSON><PERSON>", "notEmpty": "Non vuoto", "without": "Sen<PERSON>"}}, "data": "<PERSON><PERSON>", "deleteTitle": "Elimina regola di filtro", "leftTitle": "<PERSON><PERSON><PERSON><PERSON> Criterio", "logicAnd": "E", "logicOr": "O", "rightTitle": "Aumenta Criterio", "title": {"0": "Generatore di Ricerca", "_": "Generatore di Ricerca (%d)"}, "value": "Valore"}, "searchPanes": {"clearMessage": "<PERSON><PERSON><PERSON>", "collapse": {"0": "Pannelli di Ricerca", "_": "Pannelli di Ricerca (%d)"}, "count": "{total}", "countFiltered": "{shown} ({total})", "emptyPanes": "<PERSON><PERSON><PERSON> Ricerca", "loadMessage": "Caricamento Pannelli di Ricerca", "title": "<PERSON><PERSON><PERSON> Attivi - %d", "showMessage": "<PERSON><PERSON>", "collapseMessage": "<PERSON><PERSON><PERSON><PERSON>"}, "select": {"cells": {"1": "1 cella selezionata", "_": "%d celle selezionate"}, "columns": {"1": "1 colonna selezionata", "_": "%d colonne selezionate"}, "rows": {"1": "1 riga selezionata", "_": "%d righe selezionate"}}, "zeroRecords": "Nessun elemento corrispondente trovato", "stateRestore": {"creationModal": {"button": "<PERSON><PERSON>", "name": "Nome:", "order": "Ordinamento", "paging": "Paginazione", "search": "Ricerca", "select": "Selezione", "columns": {"search": "Ricerca Colonna", "visible": "Visibilità Colonna"}, "title": "Crea Nuovo Stato", "toggleLabel": "Includi:"}, "emptyError": "Il nome non può essere vuoto", "removeConfirm": "Sei sicuro di voler rimuovere questo %s?", "removeError": "Errore nel rimuovere lo stato", "removeJoiner": "e", "removeSubmit": "<PERSON><PERSON><PERSON><PERSON>", "renameButton": "Rinomina", "renameLabel": "Nuovo nome per %s", "duplicateError": "Esiste già uno stato con questo nome.", "renameTitle": "Rinomina Stato"}}