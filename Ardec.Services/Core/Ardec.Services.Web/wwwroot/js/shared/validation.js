/**
 * ARDEC Services - Validation Module
 * Validazione real-time per form con feedback visivo Tailwind/Flowbite
 */

window.Validation = (function() {
    'use strict';

    // Configuration
    const config = {
        validClass: 'border-green-500 focus:border-green-500 focus:ring-green-500',
        invalidClass: 'border-red-500 focus:border-red-500 focus:ring-red-500',
        neutralClass: 'border-gray-300 focus:border-ardec-primary focus:ring-ardec-primary',
        errorMessageClass: 'mt-1 text-sm text-red-600',
        successMessageClass: 'mt-1 text-sm text-green-600',
        debounceDelay: 300
    };

    // Validation rules
    const validationRules = {
        required: {
            test: (value) => value && value.trim().length > 0,
            message: 'Questo campo è obbligatorio'
        },
        email: {
            test: (value) => !value || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value.trim()),
            message: 'Inserisci un indirizzo email valido'
        },
        minLength: {
            test: (value, min) => !value || value.length >= min,
            message: (min) => `Deve contenere almeno ${min} caratteri`
        },
        maxLength: {
            test: (value, max) => !value || value.length <= max,
            message: (max) => `Deve contenere al massimo ${max} caratteri`
        },
        password: {
            test: (value) => !value || /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(value),
            message: 'La password deve contenere almeno 8 caratteri, una maiuscola, una minuscola e un numero'
        },
        confirmPassword: {
            test: (value, originalPasswordValue) => !value || value === originalPasswordValue,
            message: 'Le password non corrispondono'
        },
        username: {
            test: (value) => !value || /^[a-zA-Z0-9._-]{3,50}$/.test(value),
            message: 'Username deve contenere 3-50 caratteri alfanumerici, punti, trattini o underscore'
        },
        phone: {
            test: (value) => !value || /^[\+]?[\d\s\-\(\)]{8,15}$/.test(value.replace(/\s/g, '')),
            message: 'Inserisci un numero di telefono valido'
        },
        url: {
            test: (value) => !value || /^https?:\/\/.+\..+/.test(value.trim()),
            message: 'Inserisci un URL valido (http:// o https://)'
        }
    };

    // Debounce function
    function debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    // Get form field container
    function getFieldContainer(field) {
        return field.closest('.form-field') || field.parentElement;
    }

    // Clear previous validation state
    function clearFieldValidation(field) {
        const container = getFieldContainer(field);
        
        // Remove CSS classes
        field.classList.remove(...config.validClass.split(' '));
        field.classList.remove(...config.invalidClass.split(' '));
        
        // Remove validation messages
        const existingMessages = container.querySelectorAll('.validation-message');
        existingMessages.forEach(msg => msg.remove());
        
        // Remove error icons
        const errorIcons = container.querySelectorAll('.validation-icon');
        errorIcons.forEach(icon => icon.remove());
    }

    // Apply validation state
    function applyValidationState(field, isValid, message) {
        const container = getFieldContainer(field);
        
        // Clear previous state
        clearFieldValidation(field);
        
        if (isValid === null) {
            // Neutral state
            field.classList.add(...config.neutralClass.split(' '));
            return;
        }
        
        // Apply appropriate classes
        if (isValid) {
            field.classList.add(...config.validClass.split(' '));
            
            // Add success icon for certain field types
            if (field.type === 'email' || field.type === 'password' || field.hasAttribute('data-success-icon')) {
                const icon = document.createElement('div');
                icon.className = 'absolute inset-y-0 right-0 flex items-center pr-3 validation-icon';
                icon.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
                
                if (container.classList.contains('relative')) {
                    container.appendChild(icon);
                }
            }
        } else {
            field.classList.add(...config.invalidClass.split(' '));
            
            // Add error icon
            const icon = document.createElement('div');
            icon.className = 'absolute inset-y-0 right-0 flex items-center pr-3 validation-icon';
            icon.innerHTML = '<i class="fas fa-exclamation-circle text-red-500"></i>';
            
            if (container.classList.contains('relative')) {
                container.appendChild(icon);
            }
            
            // Add error message
            if (message) {
                const messageElement = document.createElement('div');
                messageElement.className = config.errorMessageClass + ' validation-message';
                messageElement.innerHTML = `<i class="fas fa-exclamation-triangle mr-1"></i>${message}`;
                container.appendChild(messageElement);
            }
        }
    }

    // Validate single field
    function validateField(field, options = {}) {
        const value = field.value;
        const fieldName = field.name || field.id;
        const rules = parseValidationRules(field);
        
        // Skip validation if field is disabled or readonly
        if (field.disabled || field.readOnly) {
            return { isValid: true, message: null };
        }
        
        // Run validation rules
        for (const rule of rules) {
            const ruleConfig = validationRules[rule.name];
            if (!ruleConfig) continue;
            
            let isValid = false;
            let message = '';
            
            if (rule.name === 'confirmPassword' && rule.targetField) {
                const targetValue = document.querySelector(`[name="${rule.targetField}"]`)?.value || '';
                isValid = ruleConfig.test(value, targetValue);
            } else if (rule.param !== undefined) {
                isValid = ruleConfig.test(value, rule.param);
                message = typeof ruleConfig.message === 'function' 
                    ? ruleConfig.message(rule.param) 
                    : ruleConfig.message;
            } else {
                isValid = ruleConfig.test(value);
                message = ruleConfig.message;
            }
            
            if (!isValid) {
                if (!options.silent) {
                    applyValidationState(field, false, message);
                }
                return { isValid: false, message };
            }
        }
        
        // All rules passed
        if (!options.silent) {
            applyValidationState(field, rules.length > 0, null);
        }
        return { isValid: true, message: null };
    }

    // Parse validation rules from HTML attributes
    function parseValidationRules(field) {
        const rules = [];
        
        // Required
        if (field.hasAttribute('required')) {
            rules.push({ name: 'required' });
        }
        
        // Email
        if (field.type === 'email') {
            rules.push({ name: 'email' });
        }
        
        // MinLength
        if (field.hasAttribute('minlength')) {
            rules.push({ name: 'minLength', param: parseInt(field.getAttribute('minlength')) });
        }
        
        // MaxLength
        if (field.hasAttribute('maxlength')) {
            rules.push({ name: 'maxLength', param: parseInt(field.getAttribute('maxlength')) });
        }
        
        // Password
        if (field.type === 'password' && !field.hasAttribute('data-confirm-password')) {
            rules.push({ name: 'password' });
        }
        
        // Confirm Password
        if (field.hasAttribute('data-confirm-password')) {
            rules.push({ 
                name: 'confirmPassword', 
                targetField: field.getAttribute('data-confirm-password') 
            });
        }
        
        // Username
        if (field.hasAttribute('data-validation') && field.getAttribute('data-validation').includes('username')) {
            rules.push({ name: 'username' });
        }
        
        // Phone
        if (field.type === 'tel' || field.hasAttribute('data-validation') && field.getAttribute('data-validation').includes('phone')) {
            rules.push({ name: 'phone' });
        }
        
        // URL
        if (field.type === 'url') {
            rules.push({ name: 'url' });
        }
        
        return rules;
    }

    // Setup real-time validation for a form
    function setupFormValidation(form) {
        if (typeof form === 'string') {
            form = document.querySelector(form);
        }
        
        if (!form) return;
        
        // Get all validatable fields
        const fields = form.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], input[type="tel"], input[type="url"], textarea, select');
        
        fields.forEach(field => {
            // Create debounced validation function
            const debouncedValidate = debounce(() => {
                validateField(field);
            }, config.debounceDelay);
            
            // Setup event listeners
            field.addEventListener('input', debouncedValidate);
            field.addEventListener('blur', () => validateField(field));
            
            // Special handling for confirm password fields
            if (field.hasAttribute('data-confirm-password')) {
                const passwordField = form.querySelector(`[name="${field.getAttribute('data-confirm-password')}"]`);
                if (passwordField) {
                    passwordField.addEventListener('input', debouncedValidate);
                }
            }
        });
        
        // Form submission validation
        form.addEventListener('submit', function(event) {
            const isFormValid = validateForm(form, { showErrors: true });
            if (!isFormValid) {
                event.preventDefault();
                event.stopPropagation();
                
                // Focus first invalid field
                const firstInvalidField = form.querySelector('.border-red-500');
                if (firstInvalidField) {
                    firstInvalidField.focus();
                    firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                
                // Show error notification
                if (window.Notifications) {
                    window.Notifications.error('Alcuni campi contengono errori. Correggi e riprova.', 'Errore di Validazione');
                }
            }
        });
    }

    // Validate entire form
    function validateForm(form, options = {}) {
        if (typeof form === 'string') {
            form = document.querySelector(form);
        }
        
        if (!form) return false;
        
        const fields = form.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], input[type="tel"], input[type="url"], textarea, select');
        let isFormValid = true;
        const errors = [];
        
        fields.forEach(field => {
            const result = validateField(field, { silent: !options.showErrors });
            if (!result.isValid) {
                isFormValid = false;
                errors.push({
                    field: field.name || field.id,
                    message: result.message
                });
            }
        });
        
        return isFormValid;
    }

    // Public API
    return {
        // Setup validation for a form
        setupForm(formSelector) {
            setupFormValidation(formSelector);
        },
        
        // Validate single field
        validateField(fieldSelector, options) {
            const field = typeof fieldSelector === 'string' 
                ? document.querySelector(fieldSelector) 
                : fieldSelector;
            return validateField(field, options);
        },
        
        // Validate entire form
        validateForm(formSelector, options) {
            return validateForm(formSelector, options);
        },
        
        // Clear validation state
        clearField(fieldSelector) {
            const field = typeof fieldSelector === 'string' 
                ? document.querySelector(fieldSelector) 
                : fieldSelector;
            if (field) {
                clearFieldValidation(field);
                applyValidationState(field, null);
            }
        },
        
        // Add custom validation rule
        addRule(name, testFunction, message) {
            validationRules[name] = {
                test: testFunction,
                message: message
            };
        },
        
        // Set field as valid/invalid manually
        setFieldState(fieldSelector, isValid, message) {
            const field = typeof fieldSelector === 'string' 
                ? document.querySelector(fieldSelector) 
                : fieldSelector;
            if (field) {
                applyValidationState(field, isValid, message);
            }
        }
    };
})();

// Auto-initialize validation on DOM ready
$(document).ready(function() {
    // Auto-setup validation for forms with data-validate attribute
    $('form[data-validate="true"]').each(function() {
        window.Validation.setupForm(this);
    });
});

// Expose globally
window.Validation = window.Validation;

console.log('✅ Validation module loaded successfully');
