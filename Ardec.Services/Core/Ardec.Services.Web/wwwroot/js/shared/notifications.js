/**
 * ARDEC Services - Notifications Module
 * Sistema di toast notifications con Flowbite styling
 */

window.Notifications = (function() {
    'use strict';

    // Configuration
    const config = {
        containerClass: 'fixed bottom-4 right-4 z-50 space-y-2',
        containerId: 'toast-container',
        defaultDuration: 5000,
        maxToasts: 5
    };

    // Icons and styles for different toast types
    const toastStyles = {
        success: {
            iconClass: 'text-green-500 bg-green-100',
            icon: `<path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>`,
            title: 'Successo'
        },
        error: {
            iconClass: 'text-red-500 bg-red-100',
            icon: `<path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 0 1-1-1V9a1 1 0 0 1 2 0v5a1 1 0 0 1-1 1Zm0-8a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z"/>`,
            title: 'Errore'
        },
        warning: {
            iconClass: 'text-orange-500 bg-orange-100',
            icon: `<path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 0 1-1-1V9a1 1 0 0 1 2 0v5a1 1 0 0 1-1 1Zm0-8a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z"/>`,
            title: 'Attenzione'
        },
        info: {
            iconClass: 'text-blue-500 bg-blue-100',
            icon: `<path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>`,
            title: 'Informazione'
        }
    };

    // Private functions
    function ensureContainer() {
        let container = document.getElementById(config.containerId);
        if (!container) {
            container = document.createElement('div');
            container.id = config.containerId;
            container.className = config.containerClass;
            document.body.appendChild(container);
        }
        return container;
    }

    function createToastElement(title, message, type, duration) {
        const toastId = 'toast-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        const style = toastStyles[type] || toastStyles.info;
        const displayTitle = title || style.title;

        const toastHTML = `
            <div id="${toastId}" class="flex items-center w-full max-w-xs p-4 text-gray-500 bg-white rounded-lg shadow animate-pulse" role="alert">
                <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 ${style.iconClass} rounded-lg">
                    <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        ${style.icon}
                    </svg>
                    <span class="sr-only">${type} icon</span>
                </div>
                <div class="ml-3 text-sm font-normal">
                    <div class="text-sm font-semibold text-gray-900">${displayTitle}</div>
                    <div class="text-sm font-normal">${message}</div>
                </div>
                <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8" 
                        onclick="Notifications.dismiss('${toastId}')" 
                        aria-label="Close">
                    <span class="sr-only">Close</span>
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                </button>
            </div>
        `;

        return { id: toastId, html: toastHTML };
    }

    function removeOldToasts() {
        const container = document.getElementById(config.containerId);
        if (!container) return;

        const toasts = container.children;
        while (toasts.length >= config.maxToasts) {
            const oldestToast = toasts[0];
            oldestToast.style.transition = 'transform 0.3s ease-out, opacity 0.3s ease-out';
            oldestToast.style.transform = 'translateX(100%)';
            oldestToast.style.opacity = '0';
            setTimeout(() => {
                if (oldestToast.parentNode) {
                    oldestToast.remove();
                }
            }, 300);
        }
    }

    function animateToastIn(toastElement) {
        // Initial state
        toastElement.style.transform = 'translateX(100%)';
        toastElement.style.opacity = '0';
        
        // Remove animate-pulse after showing
        setTimeout(() => {
            toastElement.classList.remove('animate-pulse');
        }, 100);

        // Animate in
        setTimeout(() => {
            toastElement.style.transition = 'transform 0.3s ease-out, opacity 0.3s ease-out';
            toastElement.style.transform = 'translateX(0)';
            toastElement.style.opacity = '1';
        }, 50);
    }

    // Public API
    return {
        /**
         * Show a toast notification
         */
        show(title, message, type = 'info', duration = null) {
            if (!message && title) {
                message = title;
                title = null;
            }

            duration = duration || config.defaultDuration;
            const container = ensureContainer();
            
            // Remove old toasts if we're at the limit
            removeOldToasts();

            // Create toast
            const toast = createToastElement(title, message, type, duration);
            container.insertAdjacentHTML('beforeend', toast.html);
            
            // Get the actual element and animate it
            const toastElement = document.getElementById(toast.id);
            animateToastIn(toastElement);

            // Auto-dismiss after duration
            if (duration > 0) {
                setTimeout(() => {
                    this.dismiss(toast.id);
                }, duration);
            }

            return toast.id;
        },

        /**
         * Dismiss a specific toast
         */
        dismiss(toastId) {
            const toast = document.getElementById(toastId);
            if (!toast) return;

            toast.style.transition = 'transform 0.3s ease-out, opacity 0.3s ease-out';
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';

            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        },

        /**
         * Clear all toasts
         */
        clear() {
            const container = document.getElementById(config.containerId);
            if (container) {
                const toasts = Array.from(container.children);
                toasts.forEach(toast => {
                    this.dismiss(toast.id);
                });
            }
        },

        /**
         * Convenience methods for different toast types
         */
        success(message, title = null, duration = null) {
            return this.show(title, message, 'success', duration);
        },

        error(message, title = null, duration = null) {
            return this.show(title, message, 'error', duration);
        },

        warning(message, title = null, duration = null) {
            return this.show(title, message, 'warning', duration);
        },

        info(message, title = null, duration = null) {
            return this.show(title, message, 'info', duration);
        },

        /**
         * Show loading toast (doesn't auto-dismiss)
         */
        loading(message = 'Caricamento...', title = null) {
            const toastId = this.show(title, `<i class="fas fa-spinner fa-spin mr-2"></i>${message}`, 'info', 0);
            return toastId;
        }
    };
})();

// Global function for backward compatibility
window.showFlowbiteToast = function(title, message, type) {
    return window.Notifications.show(title, message, type);
};

// Expose globally
window.Notifications = window.Notifications;

console.log('✅ Notifications module loaded successfully');
