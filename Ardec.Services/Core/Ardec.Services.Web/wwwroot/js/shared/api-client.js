/**
 * ARDEC Services - API Client Module
 * Wrapper per chiamate AJAX con error handling centralizzato
 */

window.ApiClient = (function() {
    'use strict';

    // Private functions
    function getCSRFToken() {
        return $('input[name="__RequestVerificationToken"]').val() || 
               $('meta[name="csrf-token"]').attr('content');
    }

    function handleError(xhr, defaultMessage) {
        let message = defaultMessage || 'Si è verificato un errore';
        
        if (xhr.responseJSON && xhr.responseJSON.message) {
            message = xhr.responseJSON.message;
        } else if (xhr.status === 401) {
            message = 'Sessione scaduta. Effettua nuovamente il login.';
            setTimeout(() => {
                window.location.href = '/Account/Login';
            }, 2000);
        } else if (xhr.status === 403) {
            message = 'Non hai i permessi per eseguire questa operazione.';
        } else if (xhr.status === 404) {
            message = 'Risorsa non trovata.';
        } else if (xhr.status >= 500) {
            message = 'Errore interno del server. Riprova più tardi.';
        }

        return message;
    }

    function makeRequest(options) {
        const defaults = {
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': getCSRFToken()
            },
            dataType: 'json'
        };

        return $.ajax($.extend(true, defaults, options));
    }

    // Public API
    return {
        /**
         * GET request
         */
        get(url, options = {}) {
            return makeRequest({
                url: url,
                method: 'GET',
                ...options
            });
        },

        /**
         * POST request
         */
        post(url, data, options = {}) {
            return makeRequest({
                url: url,
                method: 'POST',
                data: typeof data === 'string' ? data : JSON.stringify(data),
                ...options
            });
        },

        /**
         * PUT request
         */
        put(url, data, options = {}) {
            return makeRequest({
                url: url,
                method: 'PUT',
                data: typeof data === 'string' ? data : JSON.stringify(data),
                ...options
            });
        },

        /**
         * DELETE request
         */
        delete(url, options = {}) {
            return makeRequest({
                url: url,
                method: 'DELETE',
                ...options
            });
        },

        /**
         * Handle API response with toast notifications
         */
        handleResponse(promise, successMessage, errorMessage) {
            return promise
                .done(function(response) {
                    if (response.success) {
                        if (successMessage && window.showFlowbiteToast) {
                            window.showFlowbiteToast('Successo', response.message || successMessage, 'success');
                        }
                        return response;
                    } else {
                        throw new Error(response.message || errorMessage || 'Operazione fallita');
                    }
                })
                .fail(function(xhr) {
                    const message = handleError(xhr, errorMessage);
                    if (window.showFlowbiteToast) {
                        window.showFlowbiteToast('Errore', message, 'error');
                    }
                    throw new Error(message);
                });
        },

        /**
         * Upload file with progress
         */
        uploadFile(url, formData, onProgress) {
            return $.ajax({
                url: url,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'RequestVerificationToken': getCSRFToken()
                },
                xhr: function() {
                    const xhr = new window.XMLHttpRequest();
                    if (onProgress && xhr.upload) {
                        xhr.upload.addEventListener('progress', function(evt) {
                            if (evt.lengthComputable) {
                                const percentComplete = (evt.loaded / evt.total) * 100;
                                onProgress(percentComplete);
                            }
                        });
                    }
                    return xhr;
                }
            });
        },

        /**
         * Utility function to handle common API patterns
         */
        async confirmAndExecute(confirmMessage, apiCall, successMessage) {
            if (confirm(confirmMessage)) {
                try {
                    const response = await apiCall();
                    if (window.showFlowbiteToast) {
                        window.showFlowbiteToast('Successo', successMessage || 'Operazione completata', 'success');
                    }
                    return response;
                } catch (error) {
                    if (window.showFlowbiteToast) {
                        window.showFlowbiteToast('Errore', error.message || 'Operazione fallita', 'error');
                    }
                    throw error;
                }
            }
            return null;
        }
    };
})();

// Expose globally
window.ApiClient = window.ApiClient;

console.log('✅ ApiClient module loaded successfully');
