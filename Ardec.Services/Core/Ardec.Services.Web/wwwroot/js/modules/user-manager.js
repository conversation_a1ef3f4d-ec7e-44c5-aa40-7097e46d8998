/**
 * ARDEC Services - User Manager Module
 * Gestione interazioni utenti con API calls e aggiornamenti UI
 */

window.UserManager = (function() {
    'use strict';

    // Configuration
    const config = {
        apiBasePath: '/UserManagement/api/users',
        confirmMessages: {
            delete: 'Sei sicuro di voler eliminare questo utente?\n\nQuesta azione non può essere annullata!',
            lock: 'Sei sicuro di voler bloccare questo utente?',
            unlock: 'Sei sicuro di voler sbloccare questo utente?',
            resetPassword: 'Sei sicuro di voler resettare la password di questo utente?'
        }
    };

    // Private functions
    function updateUserRowStatus(userId, newStatus) {
        const row = document.querySelector(`tr[data-user-id="${userId}"]`);
        if (!row) return;

        const statusCell = row.querySelector('.user-status');
        if (statusCell) {
            const isLocked = newStatus === 'locked';
            statusCell.innerHTML = isLocked 
                ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"><i class="fas fa-lock mr-1"></i>Bloccato</span>'
                : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"><i class="fas fa-check-circle mr-1"></i>Attivo</span>';
        }

        // Update action buttons
        const actionsCell = row.querySelector('.user-actions');
        if (actionsCell) {
            const lockButton = actionsCell.querySelector('.lock-user-btn');
            if (lockButton) {
                const isLocked = newStatus === 'locked';
                lockButton.className = `lock-user-btn inline-flex items-center px-3 py-1 text-sm font-medium rounded-lg transition-colors ${
                    isLocked 
                        ? 'text-green-700 bg-green-50 hover:bg-green-100 border border-green-200' 
                        : 'text-orange-700 bg-orange-50 hover:bg-orange-100 border border-orange-200'
                }`;
                lockButton.innerHTML = isLocked 
                    ? '<i class="fas fa-unlock mr-1"></i>Sblocca'
                    : '<i class="fas fa-lock mr-1"></i>Blocca';
                lockButton.onclick = () => toggleUserLockout(userId, !isLocked);
            }
        }
    }

    function removeUserRow(userId) {
        const row = document.querySelector(`tr[data-user-id="${userId}"]`);
        if (row) {
            row.style.transition = 'opacity 0.3s ease-out';
            row.style.opacity = '0';
            setTimeout(() => {
                row.remove();
                // Update table counters if they exist
                updateTableCounters();
            }, 300);
        }
    }

    function updateTableCounters() {
        const table = document.querySelector('#usersTable');
        if (!table) return;

        const visibleRows = table.querySelectorAll('tbody tr').length;
        const counterElements = document.querySelectorAll('.users-count');
        counterElements.forEach(element => {
            element.textContent = visibleRows;
        });
    }

    function showLoadingState(element, originalText) {
        if (element) {
            element.disabled = true;
            element.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Elaborazione...';
            element.dataset.originalText = originalText;
        }
    }

    function hideLoadingState(element) {
        if (element && element.dataset.originalText) {
            element.disabled = false;
            element.innerHTML = element.dataset.originalText;
            delete element.dataset.originalText;
        }
    }

    // API functions
    async function toggleUserLockout(userId, shouldLock) {
        const user = await getUserInfo(userId);
        if (!user) return;

        const action = shouldLock ? 'bloccare' : 'sbloccare';
        const confirmMessage = `Sei sicuro di voler ${action} l'utente ${user.email}?`;

        if (!confirm(confirmMessage)) {
            return;
        }

        try {
            const response = await window.ApiClient.post(`${config.apiBasePath}/${userId}/toggle-lockout`);
            
            if (response.success) {
                window.Notifications.success(response.message || `Utente ${action} con successo`);
                updateUserRowStatus(userId, shouldLock ? 'locked' : 'active');
                
                // Refresh page data if we're on the details page
                if (window.location.pathname.includes('/Details/')) {
                    setTimeout(() => window.location.reload(), 1000);
                }
            } else {
                throw new Error(response.message || 'Operazione fallita');
            }
        } catch (error) {
            window.Notifications.error(error.message || 'Errore durante l\'operazione');
        }
    }

    async function deleteUser(userId) {
        const user = await getUserInfo(userId);
        if (!user) return;

        if (!confirm(`${config.confirmMessages.delete}\n\nUtente: ${user.email}`)) {
            return;
        }

        try {
            const response = await window.ApiClient.delete(`${config.apiBasePath}/${userId}`);
            
            if (response.success) {
                window.Notifications.success(response.message || 'Utente eliminato con successo');
                removeUserRow(userId);
                
                // Redirect to index if we're on the details page
                if (window.location.pathname.includes('/Details/')) {
                    setTimeout(() => {
                        window.location.href = '/UserManagement';
                    }, 2000);
                }
            } else {
                throw new Error(response.message || 'Eliminazione fallita');
            }
        } catch (error) {
            window.Notifications.error(error.message || 'Errore durante l\'eliminazione');
        }
    }

    async function resetUserPassword(userId) {
        const user = await getUserInfo(userId);
        if (!user) return;

        if (!confirm(`${config.confirmMessages.resetPassword}\n\nUtente: ${user.email}`)) {
            return;
        }

        try {
            const response = await window.ApiClient.post(`${config.apiBasePath}/${userId}/reset-password`);
            
            if (response.success) {
                window.Notifications.success(
                    response.message || 'Password resettata con successo. L\'utente riceverà una email con le istruzioni.'
                );
            } else {
                throw new Error(response.message || 'Reset password fallito');
            }
        } catch (error) {
            window.Notifications.error(error.message || 'Errore durante il reset della password');
        }
    }

    async function confirmUserEmail(userId) {
        const user = await getUserInfo(userId);
        if (!user) return;

        if (!confirm(`Confermare l'email per l'utente ${user.email}?`)) {
            return;
        }

        try {
            const response = await window.ApiClient.post(`${config.apiBasePath}/${userId}/confirm-email`);
            
            if (response.success) {
                window.Notifications.success(response.message || 'Email confermata con successo');
                
                // Refresh page to update UI
                setTimeout(() => window.location.reload(), 1000);
            } else {
                throw new Error(response.message || 'Conferma email fallita');
            }
        } catch (error) {
            window.Notifications.error(error.message || 'Errore durante la conferma email');
        }
    }

    async function getUserInfo(userId) {
        try {
            // Try to get user info from the page first (faster)
            const userRow = document.querySelector(`tr[data-user-id="${userId}"]`);
            if (userRow) {
                const emailCell = userRow.querySelector('.user-email');
                if (emailCell) {
                    return { 
                        id: userId, 
                        email: emailCell.textContent.trim() 
                    };
                }
            }

            // Fallback to API call
            const response = await window.ApiClient.get(`${config.apiBasePath}/${userId}`);
            return response.data;
        } catch (error) {
            console.warn('Could not fetch user info:', error);
            return { id: userId, email: 'Utente' };
        }
    }

    // Bulk operations
    async function bulkOperation(operation, userIds, confirmMessage) {
        if (!userIds || userIds.length === 0) {
            window.Notifications.warning('Seleziona almeno un utente');
            return;
        }

        if (!confirm(`${confirmMessage}\n\nUtenti selezionati: ${userIds.length}`)) {
            return;
        }

        const loadingToastId = window.Notifications.loading(`Elaborazione ${userIds.length} utenti...`);

        try {
            const promises = userIds.map(userId => 
                window.ApiClient.post(`${config.apiBasePath}/${userId}/${operation}`)
            );

            const results = await Promise.allSettled(promises);
            const successes = results.filter(r => r.status === 'fulfilled').length;
            const failures = results.filter(r => r.status === 'rejected').length;

            // Dismiss loading toast
            window.Notifications.dismiss(loadingToastId);

            if (successes > 0) {
                window.Notifications.success(`${successes} utenti elaborati con successo`);
            }

            if (failures > 0) {
                window.Notifications.warning(`${failures} operazioni fallite`);
            }

            // Refresh table data
            setTimeout(() => window.location.reload(), 1500);

        } catch (error) {
            window.Notifications.dismiss(loadingToastId);
            window.Notifications.error('Errore durante l\'operazione bulk');
        }
    }

    // Table management
    function setupTableInteractions() {
        // Setup select all checkbox
        const selectAllCheckbox = document.getElementById('select-all-users');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('input[name="selectedUsers"]');
                checkboxes.forEach(cb => {
                    cb.checked = this.checked;
                });
                updateBulkActionButtons();
            });
        }

        // Setup individual checkboxes
        document.addEventListener('change', function(e) {
            if (e.target.name === 'selectedUsers') {
                updateBulkActionButtons();
                
                // Update select all checkbox state
                const selectAll = document.getElementById('select-all-users');
                if (selectAll) {
                    const checkboxes = document.querySelectorAll('input[name="selectedUsers"]');
                    const checkedBoxes = document.querySelectorAll('input[name="selectedUsers"]:checked');
                    selectAll.checked = checkboxes.length === checkedBoxes.length;
                    selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
                }
            }
        });

        // Setup bulk action buttons
        const bulkButtons = document.querySelectorAll('.bulk-action-btn');
        bulkButtons.forEach(button => {
            button.addEventListener('click', function() {
                const selectedIds = Array.from(document.querySelectorAll('input[name="selectedUsers"]:checked'))
                    .map(cb => cb.value);
                const action = this.dataset.action;
                
                switch(action) {
                    case 'lock':
                        bulkOperation('toggle-lockout', selectedIds, 'Bloccare gli utenti selezionati?');
                        break;
                    case 'unlock':
                        bulkOperation('toggle-lockout', selectedIds, 'Sbloccare gli utenti selezionati?');
                        break;
                    case 'delete':
                        bulkOperation('delete', selectedIds, 'Eliminare definitivamente gli utenti selezionati?');
                        break;
                }
            });
        });
    }

    function updateBulkActionButtons() {
        const selectedCount = document.querySelectorAll('input[name="selectedUsers"]:checked').length;
        const bulkActions = document.querySelector('.bulk-actions');
        
        if (bulkActions) {
            bulkActions.style.display = selectedCount > 0 ? 'block' : 'none';
            
            const countSpan = bulkActions.querySelector('.selected-count');
            if (countSpan) {
                countSpan.textContent = selectedCount;
            }
        }
    }

    // Public API
    return {
        // User operations
        toggleLockout: toggleUserLockout,
        deleteUser: deleteUser,
        resetPassword: resetUserPassword,
        confirmEmail: confirmUserEmail,

        // Bulk operations
        bulkLock: (userIds) => bulkOperation('toggle-lockout', userIds, 'Bloccare gli utenti selezionati?'),
        bulkUnlock: (userIds) => bulkOperation('toggle-lockout', userIds, 'Sbloccare gli utenti selezionati?'),
        bulkDelete: (userIds) => bulkOperation('delete', userIds, 'Eliminare definitivamente gli utenti selezionati?'),

        // Table management
        setupTable: setupTableInteractions,

        // Utilities
        refreshUserRow: (userId) => {
            // Force refresh of user row data
            window.location.reload();
        },

        getSelectedUsers: () => {
            return Array.from(document.querySelectorAll('input[name="selectedUsers"]:checked'))
                .map(cb => cb.value);
        }
    };
})();

// Global functions for backward compatibility with existing views
window.toggleLockout = function(userId, isCurrentlyLocked) {
    window.UserManager.toggleLockout(userId, !isCurrentlyLocked);
};

window.deleteUser = function(userId, userEmail) {
    window.UserManager.deleteUser(userId);
};

window.resetPassword = function(userId, userEmail) {
    window.UserManager.resetPassword(userId);
};

window.confirmEmail = function(userId, userEmail) {
    window.UserManager.confirmEmail(userId);
};

// Auto-initialize on DOM ready
$(document).ready(function() {
    if (window.location.pathname.includes('/UserManagement')) {
        window.UserManager.setupTable();
    }
});

// Expose globally
window.UserManager = window.UserManager;

console.log('✅ UserManager module loaded successfully');
