/**
 * LocalStateManager - Desktop-like state management for MIL-STD-1388 Services
 * 
 * Features:
 * - Real-time change tracking
 * - Auto-save with debouncing
 * - Undo/Redo stack
 * - Offline-first with IndexedDB cache
 * - Visual feedback for dirty states
 * - Batch operations
 */
class LocalStateManager {
    constructor(options = {}) {
        this.options = {
            autoSaveDelay: options.autoSaveDelay || 2000,
            maxUndoSteps: options.maxUndoSteps || 20,
            debugMode: options.debugMode || false,
            endpoint: options.endpoint || '/api',
            entityType: options.entityType || 'entity'
        };

        // State tracking
        this.currentData = {};
        this.originalData = {};
        this.dirtyFields = new Set();
        this.isModified = false;
        this.isSaving = false;
        
        // Undo/Redo stacks
        this.undoStack = [];
        this.redoStack = [];
        
        // Timers and queues
        this.autoSaveTimer = null;
        this.saveQueue = new Map();
        
        // Event callbacks
        this.callbacks = {
            onFieldChanged: [],
            onSaved: [],
            onError: [],
            onStateChanged: []
        };

        // Initialize
        this.initializeKeyboardShortcuts();
        this.initializeIndexedDB();
        
        if (this.options.debugMode) {
            console.log('LocalStateManager initialized', this.options);
        }
    }

    /**
     * Initialize keyboard shortcuts for desktop-like experience
     */
    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+S - Save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveNow();
                return;
            }
            
            // Ctrl+Z - Undo
            if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
                e.preventDefault();
                this.undo();
                return;
            }
            
            // Ctrl+Shift+Z or Ctrl+Y - Redo
            if (e.ctrlKey && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
                e.preventDefault();
                this.redo();
                return;
            }
        });
    }

    /**
     * Initialize IndexedDB for offline caching
     */
    async initializeIndexedDB() {
        try {
            // Simple IndexedDB wrapper for caching
            this.dbName = `milstd1388_${this.options.entityType}`;
            this.dbVersion = 1;
            
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(this.dbName, this.dbVersion);
                
                request.onerror = () => reject(request.error);
                request.onsuccess = () => {
                    this.db = request.result;
                    resolve(this.db);
                };
                
                request.onupgradeneeded = (e) => {
                    const db = e.target.result;
                    if (!db.objectStoreNames.contains('cache')) {
                        const store = db.createObjectStore('cache', { keyPath: 'id' });
                        store.createIndex('timestamp', 'timestamp', { unique: false });
                    }
                    if (!db.objectStoreNames.contains('pendingChanges')) {
                        db.createObjectStore('pendingChanges', { keyPath: 'id' });
                    }
                };
            });
        } catch (error) {
            console.warn('IndexedDB not available, falling back to localStorage', error);
            this.useLocalStorage = true;
        }
    }

    /**
     * Load data and initialize tracking
     */
    loadData(data, entityId = null) {
        // Create snapshot for undo
        this.createUndoSnapshot();
        
        // Deep clone to avoid reference issues
        this.originalData = JSON.parse(JSON.stringify(data));
        this.currentData = JSON.parse(JSON.stringify(data));
        this.entityId = entityId;
        
        // Clear dirty state
        this.dirtyFields.clear();
        this.isModified = false;
        
        // Cache to IndexedDB/localStorage
        this.cacheData(data, entityId);
        
        // Notify listeners
        this.notifyStateChanged();
        
        if (this.options.debugMode) {
            console.log('Data loaded:', data);
        }
    }

    /**
     * Track field changes (call from input event handlers)
     */
    trackFieldChange(fieldName, newValue, element = null) {
        const oldValue = this.currentData[fieldName];
        
        // No change, skip
        if (oldValue === newValue) {
            return;
        }
        
        // Create undo snapshot before first change
        if (!this.isModified) {
            this.createUndoSnapshot();
        }
        
        // Update current data
        this.currentData[fieldName] = newValue;
        
        // Mark field as dirty
        this.dirtyFields.add(fieldName);
        this.isModified = true;
        
        // Visual feedback
        if (element) {
            this.markFieldAsDirty(element);
        }
        
        // Schedule auto-save
        this.scheduleAutoSave();
        
        // Notify callbacks
        this.notifyFieldChanged(fieldName, newValue, oldValue);
        this.notifyStateChanged();
        
        if (this.options.debugMode) {
            console.log('Field changed:', fieldName, newValue);
        }
    }

    /**
     * Manual save trigger
     */
    async saveNow() {
        if (!this.isModified) {
            return { success: true, message: 'No changes to save' };
        }
        
        if (this.isSaving) {
            return { success: false, message: 'Save already in progress' };
        }

        try {
            this.isSaving = true;
            this.showSavingIndicator();
            
            // Prepare save data (only dirty fields for efficiency)
            const saveData = { ...this.currentData };
            
            // Call server
            const response = await fetch(`${this.options.endpoint}/save`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(saveData)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                // Save successful
                this.originalData = JSON.parse(JSON.stringify(this.currentData));
                this.dirtyFields.clear();
                this.isModified = false;
                this.clearDirtyMarkers();
                
                // Clear redo stack after successful save
                this.redoStack = [];
                
                this.showSuccessIndicator(result.message || 'Saved successfully');
                this.notifySaved(result);
                this.notifyStateChanged();
                
                return { success: true, data: result };
            } else {
                throw new Error(result.error || 'Save failed');
            }
            
        } catch (error) {
            console.error('Save error:', error);
            this.showErrorIndicator(error.message);
            this.notifyError(error);
            return { success: false, error: error.message };
        } finally {
            this.isSaving = false;
            this.hideSavingIndicator();
        }
    }

    /**
     * Schedule auto-save with debouncing
     */
    scheduleAutoSave() {
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }
        
        this.autoSaveTimer = setTimeout(() => {
            this.saveNow();
        }, this.options.autoSaveDelay);
    }

    /**
     * Create undo snapshot
     */
    createUndoSnapshot() {
        const snapshot = {
            data: JSON.parse(JSON.stringify(this.currentData)),
            dirtyFields: new Set(this.dirtyFields),
            timestamp: Date.now()
        };
        
        this.undoStack.push(snapshot);
        
        // Limit stack size
        if (this.undoStack.length > this.options.maxUndoSteps) {
            this.undoStack.shift();
        }
        
        // Clear redo stack on new change
        this.redoStack = [];
    }

    /**
     * Undo last change
     */
    undo() {
        if (this.undoStack.length === 0) {
            return false;
        }
        
        // Save current state to redo stack
        const currentSnapshot = {
            data: JSON.parse(JSON.stringify(this.currentData)),
            dirtyFields: new Set(this.dirtyFields),
            timestamp: Date.now()
        };
        this.redoStack.push(currentSnapshot);
        
        // Restore previous state
        const snapshot = this.undoStack.pop();
        this.currentData = snapshot.data;
        this.dirtyFields = snapshot.dirtyFields;
        this.isModified = this.dirtyFields.size > 0;
        
        // Update UI
        this.updateFormFields();
        this.updateDirtyMarkers();
        this.notifyStateChanged();
        
        this.showToast('Undo completed', 'info');
        return true;
    }

    /**
     * Redo last undone change
     */
    redo() {
        if (this.redoStack.length === 0) {
            return false;
        }
        
        // Save current state to undo stack
        this.createUndoSnapshot();
        
        // Restore next state
        const snapshot = this.redoStack.pop();
        this.currentData = snapshot.data;
        this.dirtyFields = snapshot.dirtyFields;
        this.isModified = this.dirtyFields.size > 0;
        
        // Update UI
        this.updateFormFields();
        this.updateDirtyMarkers();
        this.notifyStateChanged();
        
        this.showToast('Redo completed', 'info');
        return true;
    }

    /**
     * Visual feedback methods
     */
    markFieldAsDirty(element) {
        element.classList.add('field-dirty');
        element.title = 'Campo modificato (Ctrl+S per salvare)';
        
        // Add asterisk to label if not present
        const label = this.findLabelForField(element);
        if (label && !label.textContent.includes('*')) {
            label.innerHTML += ' <span class="dirty-indicator">*</span>';
        }
    }

    clearDirtyMarkers() {
        document.querySelectorAll('.field-dirty').forEach(el => {
            el.classList.remove('field-dirty');
            el.title = '';
        });
        
        document.querySelectorAll('.dirty-indicator').forEach(el => {
            el.remove();
        });
    }

    updateDirtyMarkers() {
        this.clearDirtyMarkers();
        
        this.dirtyFields.forEach(fieldName => {
            const element = document.querySelector(`[name="${fieldName}"], #${fieldName}`);
            if (element) {
                this.markFieldAsDirty(element);
            }
        });
    }

    findLabelForField(element) {
        // Try to find associated label
        if (element.id) {
            return document.querySelector(`label[for="${element.id}"]`);
        }
        
        // Look for parent label
        let parent = element.parentNode;
        while (parent) {
            if (parent.tagName === 'LABEL') {
                return parent;
            }
            parent = parent.parentNode;
        }
        
        return null;
    }

    /**
     * Update form fields from current data
     */
    updateFormFields() {
        Object.keys(this.currentData).forEach(fieldName => {
            const element = document.querySelector(`[name="${fieldName}"], #${fieldName}`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = !!this.currentData[fieldName];
                } else {
                    element.value = this.currentData[fieldName] || '';
                }
            }
        });
    }

    /**
     * Cache data locally
     */
    async cacheData(data, entityId) {
        const cacheEntry = {
            id: entityId || 'default',
            data: data,
            timestamp: Date.now()
        };
        
        try {
            if (this.db) {
                const transaction = this.db.transaction(['cache'], 'readwrite');
                const store = transaction.objectStore('cache');
                store.put(cacheEntry);
            } else {
                localStorage.setItem(`${this.dbName}_cache_${cacheEntry.id}`, JSON.stringify(cacheEntry));
            }
        } catch (error) {
            console.warn('Failed to cache data:', error);
        }
    }

    /**
     * UI feedback methods
     */
    showSavingIndicator() {
        this.showToast('Saving...', 'info', { persist: true, id: 'saving-toast' });
    }

    hideSavingIndicator() {
        this.hideToast('saving-toast');
    }

    showSuccessIndicator(message) {
        this.showToast(message, 'success', { duration: 3000 });
    }

    showErrorIndicator(message) {
        this.showToast(message, 'error', { duration: 5000 });
    }

    showToast(message, type = 'info', options = {}) {
        // Simple toast implementation
        const toastId = options.id || `toast_${Date.now()}`;
        const existingToast = document.getElementById(toastId);
        
        if (existingToast) {
            existingToast.remove();
        }
        
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${this.getToastIcon(type)}"></i>
                <span>${message}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
            </div>
        `;
        
        // Add to page
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
        
        container.appendChild(toast);
        
        // Auto-remove if not persistent
        if (!options.persist && options.duration !== 0) {
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, options.duration || 4000);
        }
    }

    hideToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.remove();
        }
    }

    getToastIcon(type) {
        const icons = {
            info: 'info-circle',
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Event callback system
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    notifyFieldChanged(fieldName, newValue, oldValue) {
        this.callbacks.onFieldChanged.forEach(cb => cb({ fieldName, newValue, oldValue }));
    }

    notifySaved(result) {
        this.callbacks.onSaved.forEach(cb => cb(result));
    }

    notifyError(error) {
        this.callbacks.onError.forEach(cb => cb(error));
    }

    notifyStateChanged() {
        const state = {
            isModified: this.isModified,
            isSaving: this.isSaving,
            dirtyFieldsCount: this.dirtyFields.size,
            canUndo: this.undoStack.length > 0,
            canRedo: this.redoStack.length > 0
        };
        this.callbacks.onStateChanged.forEach(cb => cb(state));
    }

    /**
     * Utility methods
     */
    reset() {
        this.loadData(this.originalData, this.entityId);
    }

    getDirtyFields() {
        return Array.from(this.dirtyFields);
    }

    hasUnsavedChanges() {
        return this.isModified;
    }

    getState() {
        return {
            isModified: this.isModified,
            isSaving: this.isSaving,
            dirtyFieldsCount: this.dirtyFields.size,
            canUndo: this.undoStack.length > 0,
            canRedo: this.redoStack.length > 0,
            currentData: { ...this.currentData },
            originalData: { ...this.originalData }
        };
    }
}

// Export for use
window.LocalStateManager = LocalStateManager;