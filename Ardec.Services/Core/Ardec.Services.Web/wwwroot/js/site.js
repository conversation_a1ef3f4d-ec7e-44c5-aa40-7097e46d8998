// ARDEC Services - Main JavaScript File

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $($(this).attr('href'));
        if(target.length) {
            event.preventDefault();
            $('html, body').animate({
                scrollTop: target.offset().top - 80
            }, 500);
        }
    });

    // Auto-hide alerts after 5 seconds
    $('.alert:not(.alert-permanent)').delay(5000).fadeOut();
    
    // Loading states for buttons
    $(document).on('click', '.btn[type="submit"]', function(e) {
        var $btn = $(this);
        var originalText = $btn.html();
        
        if (!$btn.hasClass('no-loading')) {
            $btn.prop('disabled', true);
            $btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Elaborazione...');
            
            // Re-enable after form submission or timeout
            setTimeout(function() {
                $btn.prop('disabled', false);
                $btn.html(originalText);
            }, 5000);
        }
    });

    // Confirm delete actions
    $(document).on('click', '.btn-delete, [data-action="delete"]', function(e) {
        e.preventDefault();
        
        var $element = $(this);
        var itemName = $element.data('item-name') || 'questo elemento';
        var confirmText = $element.data('confirm-text') || `Sei sicuro di voler eliminare ${itemName}?`;
        
        if (confirm(confirmText)) {
            if ($element.is('form')) {
                $element.submit();
            } else if ($element.is('a')) {
                window.location.href = $element.attr('href');
            } else {
                // Look for parent form or trigger custom event
                var $form = $element.closest('form');
                if ($form.length) {
                    $form.submit();
                } else {
                    $element.trigger('confirmed-delete');
                }
            }
        }
    });
});

// Toast notification system
window.ArdecToast = {
    show: function(message, type = 'info', duration = 5000) {
        var toastId = 'toast-' + Date.now();
        var iconClass = this.getIconClass(type);
        var bgClass = this.getBgClass(type);
        
        var toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white ${bgClass}" role="alert" data-bs-delay="${duration}">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="${iconClass} me-2"></i>${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        // Ensure toast container exists
        if (!document.getElementById('toast-container')) {
            $('body').append('<div class="toast-container position-fixed bottom-0 end-0 p-3" id="toast-container"></div>');
        }
        
        $('#toast-container').append(toastHtml);
        
        var toast = new bootstrap.Toast(document.getElementById(toastId));
        toast.show();
        
        // Remove toast element after it's hidden
        document.getElementById(toastId).addEventListener('hidden.bs.toast', function() {
            this.remove();
        });
    },
    
    success: function(message, duration) {
        this.show(message, 'success', duration);
    },
    
    error: function(message, duration) {
        this.show(message, 'error', duration);
    },
    
    warning: function(message, duration) {
        this.show(message, 'warning', duration);
    },
    
    info: function(message, duration) {
        this.show(message, 'info', duration);
    },
    
    getIconClass: function(type) {
        switch(type) {
            case 'success': return 'fas fa-check-circle';
            case 'error': return 'fas fa-exclamation-circle';
            case 'warning': return 'fas fa-exclamation-triangle';
            case 'info': 
            default: return 'fas fa-info-circle';
        }
    },
    
    getBgClass: function(type) {
        switch(type) {
            case 'success': return 'bg-success';
            case 'error': return 'bg-danger';
            case 'warning': return 'bg-warning';
            case 'info': 
            default: return 'bg-primary';
        }
    }
};

// AJAX utilities
window.ArdecAjax = {
    get: function(url, success, error) {
        $.get(url)
            .done(success || function(data) {
                ArdecToast.success('Operazione completata con successo');
            })
            .fail(error || function(xhr) {
                var message = xhr.responseJSON?.error || 'Errore durante il caricamento';
                ArdecToast.error(message);
            });
    },
    
    post: function(url, data, success, error) {
        $.post(url, data)
            .done(success || function(data) {
                ArdecToast.success('Dati salvati con successo');
            })
            .fail(error || function(xhr) {
                var message = xhr.responseJSON?.error || 'Errore durante il salvataggio';
                ArdecToast.error(message);
            });
    },
    
    delete: function(url, success, error) {
        $.ajax({
            url: url,
            method: 'DELETE',
            headers: {
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            }
        })
        .done(success || function(data) {
            ArdecToast.success('Elemento eliminato con successo');
        })
        .fail(error || function(xhr) {
            var message = xhr.responseJSON?.error || 'Errore durante l\'eliminazione';
            ArdecToast.error(message);
        });
    }
};

// Form utilities
window.ArdecForms = {
    validate: function($form) {
        var isValid = true;
        
        // Clear previous validation
        $form.find('.is-invalid').removeClass('is-invalid');
        $form.find('.invalid-feedback').remove();
        
        // Basic validation
        $form.find('[required]').each(function() {
            var $field = $(this);
            if (!$field.val().trim()) {
                isValid = false;
                $field.addClass('is-invalid');
                $field.after('<div class="invalid-feedback">Questo campo è obbligatorio</div>');
            }
        });
        
        // Email validation
        $form.find('[type="email"]').each(function() {
            var $field = $(this);
            var email = $field.val().trim();
            if (email && !ArdecForms.isValidEmail(email)) {
                isValid = false;
                $field.addClass('is-invalid');
                $field.after('<div class="invalid-feedback">Inserisci un indirizzo email valido</div>');
            }
        });
        
        return isValid;
    },
    
    isValidEmail: function(email) {
        var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    serialize: function($form) {
        var formData = {};
        $form.serializeArray().forEach(function(item) {
            formData[item.name] = item.value;
        });
        return formData;
    }
};

// Development utilities (only in development)
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.ArdecDev = {
        fillDemoData: function() {
            // Fill forms with demo data for testing
            $('input[type="email"]').val('<EMAIL>');
            $('input[type="password"]').val('Admin123!');
            ArdecToast.info('Demo data filled');
        },
        
        showToastDemo: function() {
            ArdecToast.success('Success message');
            setTimeout(() => ArdecToast.error('Error message'), 1000);
            setTimeout(() => ArdecToast.warning('Warning message'), 2000);
            setTimeout(() => ArdecToast.info('Info message'), 3000);
        }
    };
    
    // Console helper
    console.log('🔧 ARDEC Services Development Mode');
    console.log('Available dev utilities: ArdecDev.fillDemoData(), ArdecDev.showToastDemo()');
    console.log('Keyboard shortcuts: Ctrl+D = Fill demo data');
}

// Global error handler for AJAX requests
$(document).ajaxError(function(event, xhr, settings) {
    if (xhr.status === 401) {
        ArdecToast.error('Sessione scaduta. Effettua nuovamente il login.');
        setTimeout(() => {
            window.location.href = '/Account/Login';
        }, 2000);
    } else if (xhr.status === 403) {
        ArdecToast.error('Non hai i permessi per eseguire questa operazione.');
    } else if (xhr.status >= 500) {
        ArdecToast.error('Errore interno del server. Riprova più tardi.');
    }
});

// Auto-refresh CSRF tokens for long-running sessions
setInterval(function() {
    if ($('input[name="__RequestVerificationToken"]').length) {
        $.get('/Account/RefreshToken')
            .done(function(data) {
                $('input[name="__RequestVerificationToken"]').val(data.token);
            })
            .fail(function() {
                // Token refresh failed - user might need to login again
                console.warn('Failed to refresh CSRF token');
            });
    }
}, 30 * 60 * 1000); // Every 30 minutes