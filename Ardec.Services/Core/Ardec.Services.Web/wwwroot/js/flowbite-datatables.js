/**
 * Flowbite DataTables Integration for ARDEC Services
 * Professional tables with built-in Tailwind CSS styling
 */

// Global configuration for all Flowbite DataTables
const flowbiteDataTablesDefaults = {
    // Enable Flowbite styling automatically
    pagingType: 'simple_numbers',
    responsive: true,
    autoWidth: false,
    
    // Italian language support
    language: {
        "emptyTable": "Nessun dato disponibile nella tabella",
        "info": "Mostra da _START_ a _END_ di _TOTAL_ elementi",
        "infoEmpty": "Mostra da 0 a 0 di 0 elementi",
        "infoFiltered": "(filtrato da _MAX_ elementi totali)",
        "lengthMenu": "Mostra _MENU_ elementi",
        "loadingRecords": "Caricamento...",
        "processing": "Elaborazione...",
        "search": "Cerca:",
        "zeroRecords": "Nessun elemento corrispondente trovato",
        "paginate": {
            "first": "Primo",
            "last": "Ultimo",
            "next": "Successivo",
            "previous": "Precedente"
        }
    },
    
    // Professional settings
    pageLength: 25,
    lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Tutti"]],
    
    // Professional DOM layout with Flowbite classes
    dom: 
        '<"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4"' +
            '<"flex items-center"l>' +
            '<"flex items-center"f>' +
        '>' +
        '<"relative overflow-hidden bg-white shadow-sm rounded-lg border border-gray-200"t>' +
        '<"flex flex-col sm:flex-row justify-between items-center mt-6 pt-4 border-t border-gray-100 gap-4"' +
            '<"text-sm text-gray-600"i>' +
            '<"flex justify-center"p>' +
        '>',
    
    // Flowbite-compatible classes
    oClasses: {
        "sPageButton": "flex items-center justify-center px-3 h-8 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700",
        "sPageButtonActive": "z-10 flex items-center justify-center px-3 h-8 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700",
        "sPageButtonDisabled": "flex items-center justify-center px-3 h-8 ml-0 leading-tight text-gray-300 bg-white border border-gray-300 cursor-not-allowed"
    }
};

/**
 * Initialize Flowbite DataTable with ARDEC styling
 * @param {string} selector - Table selector (e.g., '#catalogsTable')
 * @param {Object} customConfig - Additional configuration options
 * @returns {DataTable} Initialized DataTable instance
 */
function initFlowbiteDataTable(selector, customConfig = {}) {
    // Merge default config with custom config
    const config = Object.assign({}, flowbiteDataTablesDefaults, customConfig);
    
    // Initialize the DataTable
    const table = $(selector).DataTable(config);
    
    // Apply Flowbite-specific styling after initialization
    applyFlowbiteStyling(table);
    
    // Re-apply styling on draw events (pagination, sorting, etc.)
    table.on('draw', function() {
        applyFlowbiteStyling(table);
    });
    
    console.log(`Flowbite DataTable initialized: ${selector}`);
    return table;
}

/**
 * Apply Flowbite-specific styling to DataTable elements
 * @param {DataTable} table - The DataTable instance
 */
function applyFlowbiteStyling(table) {
    const $wrapper = $(table.table().container());
    
    // Style search input with Flowbite classes
    $wrapper.find('.dataTables_filter input')
        .removeClass()
        .addClass('bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5')
        .attr('placeholder', 'Cerca nella tabella...');
    
    // Style length select with Flowbite classes  
    $wrapper.find('.dataTables_length select')
        .removeClass()
        .addClass('bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5');
    
    // Style pagination container
    $wrapper.find('.dataTables_paginate')
        .addClass('flex items-center');
    
    // Style pagination buttons with Flowbite pagination classes
    $wrapper.find('.dataTables_paginate .paginate_button')
        .removeClass()
        .addClass('flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700');
    
    // Style first/previous buttons
    $wrapper.find('.dataTables_paginate .paginate_button.previous')
        .addClass('rounded-l-lg');
    
    // Style last/next buttons  
    $wrapper.find('.dataTables_paginate .paginate_button.next')
        .addClass('rounded-r-lg');
    
    // Style current page button
    $wrapper.find('.dataTables_paginate .paginate_button.current')
        .removeClass()
        .addClass('z-10 flex items-center justify-center px-3 h-8 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700');
    
    // Style disabled buttons
    $wrapper.find('.dataTables_paginate .paginate_button.disabled')
        .removeClass()
        .addClass('flex items-center justify-center px-3 h-8 leading-tight text-gray-300 bg-white border border-gray-300 cursor-not-allowed')
        .first().addClass('rounded-l-lg')
        .last().addClass('rounded-r-lg');
    
    // Style info text
    $wrapper.find('.dataTables_info')
        .removeClass()
        .addClass('text-sm font-normal text-gray-500 mb-4 md:mb-0');
    
    // Style labels with Flowbite typography
    $wrapper.find('.dataTables_length label')
        .removeClass()
        .addClass('text-sm font-medium text-gray-900 flex items-center gap-2');
        
    $wrapper.find('.dataTables_filter label')
        .removeClass() 
        .addClass('text-sm font-medium text-gray-900 flex items-center gap-2');
    
    // Apply ARDEC brand colors to active elements
    $wrapper.find('.focus\\:ring-blue-500')
        .removeClass('focus:ring-blue-500')
        .addClass('focus:ring-ardec-500');
        
    $wrapper.find('.focus\\:border-blue-500')
        .removeClass('focus:border-blue-500') 
        .addClass('focus:border-ardec-500');
        
    console.log('Flowbite styling applied to DataTable');
}

/**
 * Quick initialization function for standard ARDEC tables
 * @param {string} selector - Table selector
 * @returns {DataTable} Initialized DataTable instance
 */
function initArdecTable(selector) {
    return initFlowbiteDataTable(selector, {
        // Standard ARDEC table settings
        order: [[0, 'asc']],
        columnDefs: [
            {
                // Typically the last column (Actions)
                targets: -1,
                orderable: false,
                searchable: false,
                className: 'text-center'
            }
        ]
    });
}

// Auto-initialize tables with class 'flowbite-datatable'
$(document).ready(function() {
    // Import Flowbite JS for interactive components
    if (typeof Flowbite !== 'undefined') {
        console.log('Flowbite found, ready for DataTables integration');
    }
    
    // Auto-initialize any table with flowbite-datatable class
    $('.flowbite-datatable').each(function() {
        const tableId = $(this).attr('id');
        if (tableId) {
            initArdecTable(`#${tableId}`);
        }
    });
});
