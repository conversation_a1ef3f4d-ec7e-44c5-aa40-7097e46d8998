/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./Views/**/*.cshtml",
    "./wwwroot/js/**/*.js",
    "./Areas/**/*.cshtml",
    "./wwwroot/css/tailwind.css",
    "./node_modules/flowbite/**/*.js"
  ],
  safelist: [
    'navbar-ardec',
    'hero-section',
    'hero-title',
    'hero-subtitle',
    'card-ardec',
    'card-ardec-header',
    'card-ardec-body',
    'card-ardec-footer',
    'feature-icon',
    'stats-card',
    'stats-number',
    'stats-label',
    'btn-ardec-primary',
    'btn-ardec-outline',
    'btn-ardec-secondary',
    'nav-link-ardec',
    'footer-ardec',
    'form-control-ardec',
    'form-label-ardec',
    'fade-in-up',
    'fade-in-up-delay-1',
    'fade-in-up-delay-2',
    'fade-in-up-delay-3',
    'min-vh-60',
    'bg-ardec-50',
    'bg-ardec-100',
    'hover:bg-ardec-100',
    'hover:bg-ardec-200'
  ],
  theme: {
    extend: {
      colors: {
        // ARDEC Brand Colors
        ardec: {
          primary: '#044687',
          'primary-dark': '#033659',
          'primary-light': '#1e5fa3',
          50: '#e8f2ff',
          100: '#d1e5ff',
          200: '#a3cbff',
          300: '#75b1ff',
          400: '#4797ff',
          500: '#044687',
          600: '#033f7a',
          700: '#03386d',
          800: '#033160',
          900: '#033659'
        },
        // Professional Neutrals
        neutral: {
          50: '#fafbfc',
          100: '#f8f9fa',
          200: '#e9ecef',
          300: '#dee2e6',
          400: '#ced4da',
          500: '#6c757d',
          600: '#495057',
          700: '#343a40',
          800: '#212529',
          900: '#000000'
        },
        // Semantic Colors
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545',
        info: '#17a2b8'
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        'hero': ['3.5rem', { lineHeight: '1.1', fontWeight: '800' }],
        'display': ['2.5rem', { lineHeight: '1.2', fontWeight: '700' }],
        'title': ['2rem', { lineHeight: '1.3', fontWeight: '600' }],
        'subtitle': ['1.25rem', { lineHeight: '1.4', fontWeight: '500' }]
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem'
      },
      borderRadius: {
        'xl': '12px',
        '2xl': '16px',
        '3xl': '24px'
      },
      boxShadow: {
        'ardec': '0 2px 4px rgba(4, 70, 135, 0.1)',
        'ardec-lg': '0 8px 25px rgba(4, 70, 135, 0.15)',
        'ardec-xl': '0 20px 40px rgba(4, 70, 135, 0.2)'
      },
      animation: {
        'fade-in-up': 'fadeInUp 0.6s ease-out forwards',
        'fade-in-up-delay': 'fadeInUp 0.6s ease-out 0.2s forwards',
        'fade-in-up-delay-2': 'fadeInUp 0.6s ease-out 0.4s forwards',
        'fade-in-up-delay-3': 'fadeInUp 0.6s ease-out 0.6s forwards'
      },
      keyframes: {
        fadeInUp: {
          '0%': {
            opacity: '0',
            transform: 'translateY(30px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        }
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('flowbite/plugin')
  ],
}
