using Microsoft.EntityFrameworkCore;
using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.DTOs.DescrizioniCustom;

namespace Ardec.Services.Web.Services.DescrizioniCustom;

public class DescrizioniCustomService : IDescrizioniCustomService
{
    private readonly ArdecDbContext _context;
    private readonly ILogger<DescrizioniCustomService> _logger;

    public DescrizioniCustomService(ArdecDbContext context, ILogger<DescrizioniCustomService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<DescrizioniCustomPagedResult> GetAllAsync(int skip = 0, int take = 20, CancellationToken cancellationToken = default)
    {
        var query = _context.TB_DescrizioniCustom.AsNoTracking();
        var totalCount = await query.CountAsync(cancellationToken);
        
        var entities = await query
            .OrderBy(x => x.PART)
            .ThenBy(x => x.TER)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
        
        var items = entities.Select(MapToResponse).ToList();

        return new DescrizioniCustomPagedResult { Items = items, TotalCount = totalCount, Skip = skip, Take = take };
    }

    public async Task<DescrizioniCustomResponse?> GetByKeysAsync(string part, string ter, CancellationToken cancellationToken = default)
    {
        var entity = await _context.TB_DescrizioniCustom
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.PART == part && x.TER == ter, cancellationToken);

        return entity != null ? MapToResponse(entity) : null;
    }

    public async Task<DescrizioniCustomPagedResult> SearchAsync(DescrizioniCustomSearchRequest request, CancellationToken cancellationToken = default)
    {
        var query = _context.TB_DescrizioniCustom.AsNoTracking();

        if (!string.IsNullOrWhiteSpace(request.PART))
            query = query.Where(x => EF.Functions.Like(x.PART, $"%{request.PART}%"));

        if (!string.IsNullOrWhiteSpace(request.TER))
            query = query.Where(x => EF.Functions.Like(x.TER, $"%{request.TER}%"));

        if (request.OnlyWithDescriptions)
        {
            query = query.Where(x => 
                !string.IsNullOrWhiteSpace(x.DescrIT) ||
                !string.IsNullOrWhiteSpace(x.DescrES) ||
                !string.IsNullOrWhiteSpace(x.DescrFR) ||
                !string.IsNullOrWhiteSpace(x.DescrEN) ||
                !string.IsNullOrWhiteSpace(x.DescrPORT) ||
                !string.IsNullOrWhiteSpace(x.DescrTED) ||
                !string.IsNullOrWhiteSpace(x.DescrUSA));
        }

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = $"%{request.SearchTerm}%";
            query = query.Where(x => 
                EF.Functions.Like(x.PART, searchTerm) ||
                EF.Functions.Like(x.TER, searchTerm) ||
                (x.DescrIT != null && EF.Functions.Like(x.DescrIT, searchTerm)) ||
                (x.DescrES != null && EF.Functions.Like(x.DescrES, searchTerm)) ||
                (x.DescrFR != null && EF.Functions.Like(x.DescrFR, searchTerm)) ||
                (x.DescrEN != null && EF.Functions.Like(x.DescrEN, searchTerm)) ||
                (x.DescrPORT != null && EF.Functions.Like(x.DescrPORT, searchTerm)) ||
                (x.DescrTED != null && EF.Functions.Like(x.DescrTED, searchTerm)) ||
                (x.DescrUSA != null && EF.Functions.Like(x.DescrUSA, searchTerm)));
        }

        query = request.SortBy.ToLower() switch
        {
            "ter" => request.SortDescending ? query.OrderByDescending(x => x.TER) : query.OrderBy(x => x.TER),
            _ => request.SortDescending ? query.OrderByDescending(x => x.PART) : query.OrderBy(x => x.PART)
        };

        var totalCount = await query.CountAsync(cancellationToken);
        var entities = await query
            .Skip(request.Skip)
            .Take(request.Take)
            .ToListAsync(cancellationToken);
        
        var items = entities.Select(MapToResponse).ToList();

        return new DescrizioniCustomPagedResult { Items = items, TotalCount = totalCount, Skip = request.Skip, Take = request.Take };
    }

    public async Task<List<DescrizioniCustomResponse>> GetByPartAsync(string part, CancellationToken cancellationToken = default)
    {
        var entities = await _context.TB_DescrizioniCustom
            .AsNoTracking()
            .Where(x => x.PART == part)
            .OrderBy(x => x.TER)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<List<DescrizioniCustomResponse>> GetByTerAsync(string ter, CancellationToken cancellationToken = default)
    {
        var entities = await _context.TB_DescrizioniCustom
            .AsNoTracking()
            .Where(x => x.TER == ter)
            .OrderBy(x => x.PART)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<PartDescriptionsResponse?> GetPartDescriptionsAsync(PartDescriptionsRequest request, CancellationToken cancellationToken = default)
    {
        var entities = await _context.TB_DescrizioniCustom
            .AsNoTracking()
            .Where(x => x.PART == request.PART)
            .ToListAsync(cancellationToken);
        
        var descriptions = entities.Select(x => new TERDescriptionInfo
        {
            TER = x.TER,
            DescrIT = x.DescrIT,
            DescrES = x.DescrES,
            DescrFR = x.DescrFR,
            DescrEN = x.DescrEN,
            DescrPORT = x.DescrPORT,
            DescrTED = x.DescrTED,
            DescrUSA = x.DescrUSA,
            PreferredDescription = request.Language.ToUpper() switch
            {
                "EN" => x.DescrEN ?? x.DescrIT,
                "ES" => x.DescrES ?? x.DescrIT,
                "FR" => x.DescrFR ?? x.DescrIT,
                "PORT" => x.DescrPORT ?? x.DescrIT,
                "TED" => x.DescrTED ?? x.DescrIT,
                "USA" => x.DescrUSA ?? x.DescrIT,
                _ => x.DescrIT
            }
        }).ToList();

        return descriptions.Any() ? new PartDescriptionsResponse { PART = request.PART, Descriptions = descriptions } : null;
    }

    public async Task<string?> GetLocalizedDescriptionAsync(string part, string ter, string language = "IT", CancellationToken cancellationToken = default)
    {
        var entity = await _context.TB_DescrizioniCustom
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.PART == part && x.TER == ter, cancellationToken);

        if (entity == null) return null;

        return language.ToUpper() switch
        {
            "EN" => entity.DescrEN ?? entity.DescrIT,
            "ES" => entity.DescrES ?? entity.DescrIT,
            "FR" => entity.DescrFR ?? entity.DescrIT,
            "PORT" => entity.DescrPORT ?? entity.DescrIT,
            "TED" => entity.DescrTED ?? entity.DescrIT,
            "USA" => entity.DescrUSA ?? entity.DescrIT,
            _ => entity.DescrIT
        };
    }

    public async Task<List<DescrizioniCustomResponse>> SearchByDescriptionContentAsync(string searchTerm, string language = "IT", CancellationToken cancellationToken = default)
    {
        var query = _context.TB_DescrizioniCustom.AsNoTracking();
        var searchPattern = $"%{searchTerm}%";

        query = language.ToUpper() switch
        {
            "EN" => query.Where(x => x.DescrEN != null && EF.Functions.Like(x.DescrEN, searchPattern)),
            "ES" => query.Where(x => x.DescrES != null && EF.Functions.Like(x.DescrES, searchPattern)),
            "FR" => query.Where(x => x.DescrFR != null && EF.Functions.Like(x.DescrFR, searchPattern)),
            "PORT" => query.Where(x => x.DescrPORT != null && EF.Functions.Like(x.DescrPORT, searchPattern)),
            "TED" => query.Where(x => x.DescrTED != null && EF.Functions.Like(x.DescrTED, searchPattern)),
            "USA" => query.Where(x => x.DescrUSA != null && EF.Functions.Like(x.DescrUSA, searchPattern)),
            _ => query.Where(x => x.DescrIT != null && EF.Functions.Like(x.DescrIT, searchPattern))
        };

        var entities = await query.OrderBy(x => x.PART).ThenBy(x => x.TER).ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<DescrizioniCustomResponse> CreateAsync(CreateDescrizioniCustomRequest request, CancellationToken cancellationToken = default)
    {
        var exists = await ExistsAsync(request.PART, request.TER, cancellationToken);
        if (exists)
            throw new InvalidOperationException($"Descrizione custom per PART '{request.PART}' e TER '{request.TER}' già esistente");

        var entity = new TB_DescrizioniCustom
        {
            PART = request.PART,
            TER = request.TER,
            DescrIT = request.DescrIT,
            DescrES = request.DescrES,
            DescrFR = request.DescrFR,
            DescrEN = request.DescrEN,
            DescrPORT = request.DescrPORT,
            DescrTED = request.DescrTED,
            DescrUSA = request.DescrUSA
        };

        _context.TB_DescrizioniCustom.Add(entity);
        await _context.SaveChangesAsync(cancellationToken);

        return MapToResponse(entity);
    }

    public async Task<DescrizioniCustomResponse?> UpdateAsync(string part, string ter, UpdateDescrizioniCustomRequest request, CancellationToken cancellationToken = default)
    {
        var entity = await _context.TB_DescrizioniCustom
            .FirstOrDefaultAsync(x => x.PART == part && x.TER == ter, cancellationToken);

        if (entity == null) return null;

        entity.DescrIT = request.DescrIT;
        entity.DescrES = request.DescrES;
        entity.DescrFR = request.DescrFR;
        entity.DescrEN = request.DescrEN;
        entity.DescrPORT = request.DescrPORT;
        entity.DescrTED = request.DescrTED;
        entity.DescrUSA = request.DescrUSA;

        await _context.SaveChangesAsync(cancellationToken);
        return MapToResponse(entity);
    }

    public async Task<bool> DeleteAsync(string part, string ter, CancellationToken cancellationToken = default)
    {
        var entity = await _context.TB_DescrizioniCustom
            .FirstOrDefaultAsync(x => x.PART == part && x.TER == ter, cancellationToken);

        if (entity == null) return false;

        _context.TB_DescrizioniCustom.Remove(entity);
        await _context.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<bool> ExistsAsync(string part, string ter, CancellationToken cancellationToken = default)
    {
        return await _context.TB_DescrizioniCustom
            .AsNoTracking()
            .AnyAsync(x => x.PART == part && x.TER == ter, cancellationToken);
    }

    public async Task<DescrizioniCustomStatsResponse> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        var totalRecords = await _context.TB_DescrizioniCustom.CountAsync(cancellationToken);
        var uniqueParts = await _context.TB_DescrizioniCustom.Select(x => x.PART).Distinct().CountAsync(cancellationToken);
        var uniqueTER = await _context.TB_DescrizioniCustom.Select(x => x.TER).Distinct().CountAsync(cancellationToken);

        var recordsWithDescriptions = await _context.TB_DescrizioniCustom
            .Where(x => !string.IsNullOrWhiteSpace(x.DescrIT) ||
                       !string.IsNullOrWhiteSpace(x.DescrES) ||
                       !string.IsNullOrWhiteSpace(x.DescrFR) ||
                       !string.IsNullOrWhiteSpace(x.DescrEN) ||
                       !string.IsNullOrWhiteSpace(x.DescrPORT) ||
                       !string.IsNullOrWhiteSpace(x.DescrTED) ||
                       !string.IsNullOrWhiteSpace(x.DescrUSA))
            .CountAsync(cancellationToken);

        var multiLanguageRecords = await _context.TB_DescrizioniCustom
            .Where(x => (string.IsNullOrWhiteSpace(x.DescrIT) ? 0 : 1) +
                       (string.IsNullOrWhiteSpace(x.DescrES) ? 0 : 1) +
                       (string.IsNullOrWhiteSpace(x.DescrFR) ? 0 : 1) +
                       (string.IsNullOrWhiteSpace(x.DescrEN) ? 0 : 1) +
                       (string.IsNullOrWhiteSpace(x.DescrPORT) ? 0 : 1) +
                       (string.IsNullOrWhiteSpace(x.DescrTED) ? 0 : 1) +
                       (string.IsNullOrWhiteSpace(x.DescrUSA) ? 0 : 1) > 1)
            .CountAsync(cancellationToken);

        var languageStats = new List<LanguageCustomStats>
        {
            new() { Language = "IT", DescriptionCount = await _context.TB_DescrizioniCustom.Where(x => !string.IsNullOrWhiteSpace(x.DescrIT)).CountAsync(cancellationToken), Percentage = 0 },
            new() { Language = "ES", DescriptionCount = await _context.TB_DescrizioniCustom.Where(x => !string.IsNullOrWhiteSpace(x.DescrES)).CountAsync(cancellationToken), Percentage = 0 },
            new() { Language = "FR", DescriptionCount = await _context.TB_DescrizioniCustom.Where(x => !string.IsNullOrWhiteSpace(x.DescrFR)).CountAsync(cancellationToken), Percentage = 0 },
            new() { Language = "EN", DescriptionCount = await _context.TB_DescrizioniCustom.Where(x => !string.IsNullOrWhiteSpace(x.DescrEN)).CountAsync(cancellationToken), Percentage = 0 },
            new() { Language = "PORT", DescriptionCount = await _context.TB_DescrizioniCustom.Where(x => !string.IsNullOrWhiteSpace(x.DescrPORT)).CountAsync(cancellationToken), Percentage = 0 },
            new() { Language = "TED", DescriptionCount = await _context.TB_DescrizioniCustom.Where(x => !string.IsNullOrWhiteSpace(x.DescrTED)).CountAsync(cancellationToken), Percentage = 0 },
            new() { Language = "USA", DescriptionCount = await _context.TB_DescrizioniCustom.Where(x => !string.IsNullOrWhiteSpace(x.DescrUSA)).CountAsync(cancellationToken), Percentage = 0 }
        };

        for (int i = 0; i < languageStats.Count; i++)
        {
            languageStats[i] = languageStats[i] with { Percentage = totalRecords > 0 ? (double)languageStats[i].DescriptionCount / totalRecords * 100 : 0 };
        }

        return new DescrizioniCustomStatsResponse
        {
            TotalRecords = totalRecords,
            UniqueParts = uniqueParts,
            UniqueTER = uniqueTER,
            RecordsWithDescriptions = recordsWithDescriptions,
            MultiLanguageRecords = multiLanguageRecords,
            LanguageDistribution = languageStats
        };
    }

    // Implementazioni semplificate per gli altri metodi
    public Task<List<DescrizioniCustomResponse>> GetDuplicatesAsync(CancellationToken cancellationToken = default) => Task.FromResult(new List<DescrizioniCustomResponse>());
    public Task<List<DescrizioniCustomResponse>> GetEmptyDescriptionsAsync(CancellationToken cancellationToken = default) => Task.FromResult(new List<DescrizioniCustomResponse>());
    public Task<List<DescrizioniCustomResponse>> GetSingleLanguageDescriptionsAsync(string language = "IT", CancellationToken cancellationToken = default) => Task.FromResult(new List<DescrizioniCustomResponse>());
    public Task<List<DescrizioniCustomResponse>> GetMultiLanguageDescriptionsAsync(CancellationToken cancellationToken = default) => Task.FromResult(new List<DescrizioniCustomResponse>());
    public Task<List<DescrizioniCustomResponse>> BatchUpdateAsync(BatchUpdateDescrizioniCustomRequest request, CancellationToken cancellationToken = default) => Task.FromResult(new List<DescrizioniCustomResponse>());
    public Task<int> DeleteByPartAsync(string part, CancellationToken cancellationToken = default) => Task.FromResult(0);
    public Task<int> DeleteByTerAsync(string ter, CancellationToken cancellationToken = default) => Task.FromResult(0);
    public Task<List<DescrizioniCustomResponse>> CopyDescriptionsAsync(string sourcePart, string targetPart, bool overwriteExisting = false, CancellationToken cancellationToken = default) => Task.FromResult(new List<DescrizioniCustomResponse>());
    public Task<List<DescrizioniCustomResponse>> SyncDescriptionsForPartAsync(string part, string sourceLanguage = "IT", List<string>? targetLanguages = null, CancellationToken cancellationToken = default) => Task.FromResult(new List<DescrizioniCustomResponse>());

    private static readonly Func<TB_DescrizioniCustom, DescrizioniCustomResponse> MapToResponse = entity => new()
    {
        PART = entity.PART,
        TER = entity.TER,
        DescrIT = entity.DescrIT,
        DescrES = entity.DescrES,
        DescrFR = entity.DescrFR,
        DescrEN = entity.DescrEN,
        DescrPORT = entity.DescrPORT,
        DescrTED = entity.DescrTED,
        DescrUSA = entity.DescrUSA
    };
}
