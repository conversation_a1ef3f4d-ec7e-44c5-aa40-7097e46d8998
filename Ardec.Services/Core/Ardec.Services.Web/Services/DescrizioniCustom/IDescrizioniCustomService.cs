using Ardec.Services.Web.DTOs.DescrizioniCustom;

namespace Ardec.Services.Web.Services.DescrizioniCustom;

/// <summary>
/// Interface per il servizio di gestione TB_DescrizioniCustom
/// </summary>
public interface IDescrizioniCustomService
{
    /// <summary>
    /// Ottiene tutte le descrizioni custom con paginazione
    /// </summary>
    Task<DescrizioniCustomPagedResult> GetAllAsync(int skip = 0, int take = 20, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene una descrizione custom per PART e TER
    /// </summary>
    Task<DescrizioniCustomResponse?> GetByKeysAsync(string part, string ter, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cerca descrizioni custom con filtri avanzati
    /// </summary>
    Task<DescrizioniCustomPagedResult> SearchAsync(DescrizioniCustomSearchRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene tutte le descrizioni custom per una parte specifica
    /// </summary>
    Task<List<DescrizioniCustomResponse>> GetByPartAsync(string part, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene tutte le descrizioni custom per un TER specifico
    /// </summary>
    Task<List<DescrizioniCustomResponse>> GetByTerAsync(string ter, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene le descrizioni per una parte con informazioni dettagliate per TER
    /// </summary>
    Task<PartDescriptionsResponse?> GetPartDescriptionsAsync(PartDescriptionsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene la descrizione localizzata per una specifica combinazione PART/TER
    /// </summary>
    Task<string?> GetLocalizedDescriptionAsync(string part, string ter, string language = "IT", CancellationToken cancellationToken = default);

    /// <summary>
    /// Ricerca descrizioni per contenuto testuale in una lingua specifica
    /// </summary>
    Task<List<DescrizioniCustomResponse>> SearchByDescriptionContentAsync(string searchTerm, string language = "IT", CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene descrizioni duplicate (stesso PART/TER con descrizioni diverse)
    /// </summary>
    Task<List<DescrizioniCustomResponse>> GetDuplicatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene descrizioni senza alcun contenuto
    /// </summary>
    Task<List<DescrizioniCustomResponse>> GetEmptyDescriptionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene descrizioni che hanno solo una lingua specificata
    /// </summary>
    Task<List<DescrizioniCustomResponse>> GetSingleLanguageDescriptionsAsync(string language = "IT", CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene descrizioni multilingue (con più di una lingua)
    /// </summary>
    Task<List<DescrizioniCustomResponse>> GetMultiLanguageDescriptionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Crea una nuova descrizione custom
    /// </summary>
    Task<DescrizioniCustomResponse> CreateAsync(CreateDescrizioniCustomRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Aggiorna una descrizione custom esistente
    /// </summary>
    Task<DescrizioniCustomResponse?> UpdateAsync(string part, string ter, UpdateDescrizioniCustomRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Aggiornamento batch di più descrizioni
    /// </summary>
    Task<List<DescrizioniCustomResponse>> BatchUpdateAsync(BatchUpdateDescrizioniCustomRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Elimina una descrizione custom
    /// </summary>
    Task<bool> DeleteAsync(string part, string ter, CancellationToken cancellationToken = default);

    /// <summary>
    /// Elimina tutte le descrizioni per una parte
    /// </summary>
    Task<int> DeleteByPartAsync(string part, CancellationToken cancellationToken = default);

    /// <summary>
    /// Elimina tutte le descrizioni per un TER
    /// </summary>
    Task<int> DeleteByTerAsync(string ter, CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica se esiste una descrizione custom
    /// </summary>
    Task<bool> ExistsAsync(string part, string ter, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene statistiche sulle descrizioni custom
    /// </summary>
    Task<DescrizioniCustomStatsResponse> GetStatsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Copia descrizioni da una parte a un'altra
    /// </summary>
    Task<List<DescrizioniCustomResponse>> CopyDescriptionsAsync(string sourcePart, string targetPart, bool overwriteExisting = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sincronizza descrizioni tra TER per la stessa parte
    /// </summary>
    Task<List<DescrizioniCustomResponse>> SyncDescriptionsForPartAsync(string part, string sourceLanguage = "IT", List<string>? targetLanguages = null, CancellationToken cancellationToken = default);
}
