using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Services.CID;
using Ardec.Services.Web.Models;
using Microsoft.EntityFrameworkCore;

namespace Ardec.Services.Web.Services.Cid;

/// <summary>
/// CID Service Implementation - Complete Change Implementation Document Management
/// Manages complex CID operations including version matrices, statistics, and parts management
/// Core of ARDEC's configuration management system
/// </summary>
public class CidService : ICIDService
{
    private readonly ArdecDbContext _context;
    private readonly ILogger<CidService> _logger;

    public CidService(ArdecDbContext context, ILogger<CidService> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Get all CID entries (Admin view)
    /// </summary>
    public async Task<IEnumerable<TB_CID>> GetAllCidsAsync()
    {
        try
        {
            var cids = await _context.TB_CID
                .OrderBy(c => c.TER)
                .ThenBy(c => c.CID)
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} CID entries", cids.Count);
            return cids;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all CID entries");
            throw;
        }
    }

    /// <summary>
    /// Get all CIDs for a specific catalog (TER)
    /// </summary>
    public async Task<IEnumerable<TB_CID>> GetCidsByTerAsync(string ter)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));

        try
        {
            var cids = await _context.TB_CID
                .Where(c => c.TER == ter)
                .OrderBy(c => c.CID)
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} CID entries for TER: {TER}", cids.Count, ter);
            return cids;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving CIDs for TER: {TER}", ter);
            throw;
        }
    }

    /// <summary>
    /// Get specific CID by composite key TER + CID
    /// </summary>
    public async Task<TB_CID?> GetCidAsync(string ter, string cid)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (string.IsNullOrWhiteSpace(cid))
            throw new ArgumentException("CID cannot be null or empty", nameof(cid));

        try
        {
            var cidEntry = await _context.TB_CID
                .FirstOrDefaultAsync(c => c.TER == ter && c.CID == cid);

            if (cidEntry != null)
                _logger.LogInformation("Retrieved CID: TER {TER}, CID {CID}", ter, cid);
            else
                _logger.LogWarning("CID not found: TER {TER}, CID {CID}", ter, cid);

            return cidEntry;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving CID: TER {TER}, CID {CID}", ter, cid);
            throw;
        }
    }

    /// <summary>
    /// Check if CID exists for specific TER
    /// </summary>
    public async Task<bool> ExistsAsync(string ter, string cid)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (string.IsNullOrWhiteSpace(cid))
            throw new ArgumentException("CID cannot be null or empty", nameof(cid));

        try
        {
            var exists = await _context.TB_CID
                .AnyAsync(c => c.TER == ter && c.CID == cid);

            _logger.LogInformation("CID exists check: TER {TER}, CID {CID} - Result: {Exists}", ter, cid, exists);
            return exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if CID exists: TER {TER}, CID {CID}", ter, cid);
            throw;
        }
    }

    /// <summary>
    /// Create new CID entry
    /// </summary>
    public async Task<TB_CID> CreateCidAsync(TB_CID cidEntry)
    {
        if (cidEntry == null)
            throw new ArgumentNullException(nameof(cidEntry));
        if (string.IsNullOrWhiteSpace(cidEntry.TER))
            throw new ArgumentException("TER cannot be null or empty", nameof(cidEntry));
        if (string.IsNullOrWhiteSpace(cidEntry.CID))
            throw new ArgumentException("CID cannot be null or empty", nameof(cidEntry));

        try
        {
            // Check if CID already exists
            var existingCid = await _context.TB_CID
                .FirstOrDefaultAsync(c => c.TER == cidEntry.TER && c.CID == cidEntry.CID);

            if (existingCid != null)
                throw new InvalidOperationException($"CID already exists: TER {cidEntry.TER}, CID {cidEntry.CID}");

            // Set creation date if not provided
            if (cidEntry.Data == null)
                cidEntry.Data = DateTime.UtcNow;

            _context.TB_CID.Add(cidEntry);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created CID: TER {TER}, CID {CID}", cidEntry.TER, cidEntry.CID);
            return cidEntry;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating CID: TER {TER}, CID {CID}", cidEntry?.TER, cidEntry?.CID);
            throw;
        }
    }

    /// <summary>
    /// Update existing CID entry
    /// </summary>
    public async Task<TB_CID> UpdateCidAsync(TB_CID cidEntry)
    {
        if (cidEntry == null)
            throw new ArgumentNullException(nameof(cidEntry));
        if (string.IsNullOrWhiteSpace(cidEntry.TER))
            throw new ArgumentException("TER cannot be null or empty", nameof(cidEntry));
        if (string.IsNullOrWhiteSpace(cidEntry.CID))
            throw new ArgumentException("CID cannot be null or empty", nameof(cidEntry));

        try
        {
            var existingCid = await _context.TB_CID
                .FirstOrDefaultAsync(c => c.TER == cidEntry.TER && c.CID == cidEntry.CID);

            if (existingCid == null)
                throw new InvalidOperationException($"CID not found: TER {cidEntry.TER}, CID {cidEntry.CID}");

            // Update properties
            existingCid.Data = cidEntry.Data;
            existingCid.Titolo = cidEntry.Titolo;
            existingCid.TitoloES = cidEntry.TitoloES;
            existingCid.TitoloEN = cidEntry.TitoloEN;
            existingCid.TitoloPT = cidEntry.TitoloPT;
            existingCid.TitoloFR = cidEntry.TitoloFR;
            existingCid.TitoloUSA = cidEntry.TitoloUSA;
            existingCid.TitoloTED = cidEntry.TitoloTED;
            existingCid.Applicabilità = cidEntry.Applicabilità;
            existingCid.ApplicabilitàES = cidEntry.ApplicabilitàES;
            existingCid.ApplicabilitàEN = cidEntry.ApplicabilitàEN;
            existingCid.ApplicabilitàPT = cidEntry.ApplicabilitàPT;
            existingCid.ApplicabilitàFR = cidEntry.ApplicabilitàFR;
            existingCid.ApplicabilitàUSA = cidEntry.ApplicabilitàUSA;
            existingCid.ApplicabilitàTED = cidEntry.ApplicabilitàTED;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated CID: TER {TER}, CID {CID}", cidEntry.TER, cidEntry.CID);
            return existingCid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating CID: TER {TER}, CID {CID}", cidEntry?.TER, cidEntry?.CID);
            throw;
        }
    }

    /// <summary>
    /// Delete CID entry
    /// </summary>
    public async Task<bool> DeleteCidAsync(string ter, string cid)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (string.IsNullOrWhiteSpace(cid))
            throw new ArgumentException("CID cannot be null or empty", nameof(cid));

        try
        {
            var cidEntry = await _context.TB_CID
                .FirstOrDefaultAsync(c => c.TER == ter && c.CID == cid);

            if (cidEntry == null)
            {
                _logger.LogWarning("CID not found for deletion: TER {TER}, CID {CID}", ter, cid);
                return false;
            }

            _context.TB_CID.Remove(cidEntry);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted CID: TER {TER}, CID {CID}", ter, cid);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting CID: TER {TER}, CID {CID}", ter, cid);
            throw;
        }
    }

    /// <summary>
    /// Search CIDs by text (title, applicability, etc.)
    /// </summary>
    public async Task<IEnumerable<TB_CID>> SearchCidsAsync(string searchQuery)
    {
        if (string.IsNullOrWhiteSpace(searchQuery))
            throw new ArgumentException("Search query cannot be null or empty", nameof(searchQuery));

        try
        {
            var searchTerm = searchQuery.Trim().ToLower();

            var results = await _context.TB_CID
                .Where(c => 
                    EF.Functions.Like(c.CID.ToLower(), $"%{searchTerm}%") ||
                    EF.Functions.Like((c.Titolo ?? "").ToLower(), $"%{searchTerm}%") ||
                    EF.Functions.Like((c.TitoloEN ?? "").ToLower(), $"%{searchTerm}%") ||
                    EF.Functions.Like((c.Applicabilità ?? "").ToLower(), $"%{searchTerm}%") ||
                    EF.Functions.Like((c.ApplicabilitàEN ?? "").ToLower(), $"%{searchTerm}%"))
                .OrderBy(c => c.TER)
                .ThenBy(c => c.CID)
                .ToListAsync();

            _logger.LogInformation("Search '{Query}' returned {Count} CID results", searchQuery, results.Count);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching CIDs with query: {Query}", searchQuery);
            throw;
        }
    }

    /// <summary>
    /// Get CIDs with multilingual title support
    /// </summary>
    public async Task<IEnumerable<TB_CID>> GetCidsWithLanguageAsync(string ter, string language)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (string.IsNullOrWhiteSpace(language))
            throw new ArgumentException("Language cannot be null or empty", nameof(language));

        try
        {
            var cids = await _context.TB_CID
                .Where(c => c.TER == ter)
                .OrderBy(c => c.CID)
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} CIDs for TER {TER} with language {Language}", 
                cids.Count, ter, language);
            
            return cids;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving CIDs for TER {TER} with language {Language}", ter, language);
            throw;
        }
    }

    /// <summary>
    /// Get CID statistics for a catalog
    /// </summary>
    public async Task<object> GetCidStatisticsAsync(string ter)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));

        try
        {
            var cids = await _context.TB_CID
                .Where(c => c.TER == ter)
                .ToListAsync();

            var stats = new
            {
                TotalCids = cids.Count,
                CidsWithTitle = cids.Count(c => !string.IsNullOrWhiteSpace(c.Titolo)),
                CidsWithEnglishTitle = cids.Count(c => !string.IsNullOrWhiteSpace(c.TitoloEN)),
                CidsWithApplicability = cids.Count(c => !string.IsNullOrWhiteSpace(c.Applicabilità)),
                CidsWithDate = cids.Count(c => c.Data.HasValue),
                LanguageSupport = new
                {
                    Italian = cids.Count(c => !string.IsNullOrWhiteSpace(c.Titolo)),
                    English = cids.Count(c => !string.IsNullOrWhiteSpace(c.TitoloEN)),
                    Spanish = cids.Count(c => !string.IsNullOrWhiteSpace(c.TitoloES)),
                    Portuguese = cids.Count(c => !string.IsNullOrWhiteSpace(c.TitoloPT)),
                    French = cids.Count(c => !string.IsNullOrWhiteSpace(c.TitoloFR)),
                    German = cids.Count(c => !string.IsNullOrWhiteSpace(c.TitoloTED)),
                    USA = cids.Count(c => !string.IsNullOrWhiteSpace(c.TitoloUSA))
                },
                LastUpdate = cids.Where(c => c.Data.HasValue).Max(c => c.Data),
                FirstCreated = cids.Where(c => c.Data.HasValue).Min(c => c.Data)
            };

            _logger.LogInformation("Generated CID statistics for TER {TER}: {TotalCids} total CIDs", ter, stats.TotalCids);
            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CID statistics for TER {TER}", ter);
            throw;
        }
    }

    /// <summary>
    /// Get CIDs by date range
    /// </summary>
    public async Task<IEnumerable<TB_CID>> GetCidsByDateRangeAsync(string ter, DateTime? startDate, DateTime? endDate)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));

        try
        {
            var query = _context.TB_CID.Where(c => c.TER == ter);

            if (startDate.HasValue)
                query = query.Where(c => c.Data >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(c => c.Data <= endDate.Value);

            var cids = await query
                .OrderBy(c => c.Data)
                .ThenBy(c => c.CID)
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} CIDs for TER {TER} between {StartDate} and {EndDate}", 
                cids.Count, ter, startDate, endDate);
            
            return cids;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving CIDs by date range for TER {TER}", ter);
            throw;
        }
    }

    /// <summary>
    /// Bulk update CIDs for a catalog
    /// </summary>
    public async Task<int> BulkUpdateCidsAsync(string ter, IEnumerable<TB_CID> cids)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (cids == null)
            throw new ArgumentNullException(nameof(cids));

        var cidList = cids.ToList();
        if (!cidList.Any())
            return 0;

        // Validate that all CIDs belong to the specified TER
        var invalidCids = cidList.Where(c => c.TER != ter).ToList();
        if (invalidCids.Any())
            throw new ArgumentException($"All CIDs must belong to TER {ter}");

        try
        {
            int updatedCount = 0;

            foreach (var cid in cidList)
            {
                var existingCid = await _context.TB_CID
                    .FirstOrDefaultAsync(c => c.TER == cid.TER && c.CID == cid.CID);

                if (existingCid != null)
                {
                    // Update existing
                    existingCid.Data = cid.Data;
                    existingCid.Titolo = cid.Titolo;
                    existingCid.TitoloES = cid.TitoloES;
                    existingCid.TitoloEN = cid.TitoloEN;
                    existingCid.TitoloPT = cid.TitoloPT;
                    existingCid.TitoloFR = cid.TitoloFR;
                    existingCid.TitoloUSA = cid.TitoloUSA;
                    existingCid.TitoloTED = cid.TitoloTED;
                    existingCid.Applicabilità = cid.Applicabilità;
                    existingCid.ApplicabilitàES = cid.ApplicabilitàES;
                    existingCid.ApplicabilitàEN = cid.ApplicabilitàEN;
                    existingCid.ApplicabilitàPT = cid.ApplicabilitàPT;
                    existingCid.ApplicabilitàFR = cid.ApplicabilitàFR;
                    existingCid.ApplicabilitàUSA = cid.ApplicabilitàUSA;
                    existingCid.ApplicabilitàTED = cid.ApplicabilitàTED;
                    updatedCount++;
                }
                else
                {
                    // Create new
                    _context.TB_CID.Add(cid);
                    updatedCount++;
                }
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Bulk updated {Count} CIDs for TER {TER}", updatedCount, ter);
            return updatedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating CIDs for TER {TER}", ter);
            throw;
        }
    }

    #region Advanced CID Versioning Methods

    /// <summary>
    /// Get all tables for a specific CID (simplified implementation)
    /// </summary>
    public async Task<IEnumerable<TB_Tavole>> GetTablesForCidAsync(string ter, string cid)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (string.IsNullOrWhiteSpace(cid))
            throw new ArgumentException("CID cannot be null or empty", nameof(cid));

        try
        {
            // For now, return tables from the catalog - will need proper CID-table relationship model later
            var tables = await _context.TB_Tavole
                .Where(t => t.TER == ter)
                .OrderBy(t => t.Tavola)
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} tables for CID: TER {TER}, CID {CID} (simplified)", tables.Count, ter, cid);
            return tables;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tables for CID: TER {TER}, CID {CID}", ter, cid);
            throw;
        }
    }

    /// <summary>
    /// Get table details specific to a CID (simplified implementation)
    /// </summary>
    public async Task<IEnumerable<TB_DettagliTavole>> GetTableDetailsForCidAsync(string ter, string cid, string? tavola = null)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (string.IsNullOrWhiteSpace(cid))
            throw new ArgumentException("CID cannot be null or empty", nameof(cid));

        try
        {
            // For now, return table details from the catalog - will need proper CID-table relationship model later
            var query = _context.TB_DettagliTavole.AsQueryable();

            if (!string.IsNullOrWhiteSpace(tavola))
                query = query.Where(dt => dt.Tavola == tavola);

            var details = await query
                .OrderBy(dt => dt.Tavola)
                .ThenBy(dt => dt.Versione)
                .ToListAsync();

            var tableFilter = tavola != null ? $", Table {tavola}" : "";
            _logger.LogInformation("Retrieved {Count} table details for CID: TER {TER}, CID {CID}{TableFilter} (simplified)", 
                details.Count, ter, cid, tableFilter);
            
            return details;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving table details for CID: TER {TER}, CID {CID}, Table {Tavola}", ter, cid, tavola);
            throw;
        }
    }

    /// <summary>
    /// Get version matrix for dashboard - shows CID vs Table version status
    /// </summary>
    public async Task<CidVersionMatrix> GetVersionMatrixAsync(string ter)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));

        try
        {
            var cids = await GetCidsByTerAsync(ter);
            var cidList = cids.ToList();

            var matrix = new CidVersionMatrix
            {
                TER = ter,
                GeneratedAt = DateTime.UtcNow,
                CidVersions = new List<CidVersionInfo>()
            };

            foreach (var cid in cidList)
            {
                var tableDetails = await GetTableDetailsForCidAsync(ter, cid.CID);
                var detailsList = tableDetails.ToList();

                var versionInfo = new CidVersionInfo
                {
                    CID = cid.CID,
                    Title = cid.Titolo ?? cid.CID,
                    Date = cid.Data,
                    TotalTables = detailsList.Select(dt => dt.Tavola).Distinct().Count(),
                    TableVersions = detailsList
                        .GroupBy(dt => dt.Tavola)
                        .ToDictionary(
                            g => g.Key,
                            g => new TableVersionInfo
                            {
                                Tavola = g.Key,
                                TotalParts = g.Count(),
                                LastUpdate = g.Max(dt => dt.Data),
                                Status = DetermineTableStatus(g.ToList())
                            }
                        )
                };

                matrix.CidVersions.Add(versionInfo);
            }

            _logger.LogInformation("Generated version matrix for TER {TER} with {Count} CIDs", ter, cidList.Count);
            return matrix;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating version matrix for TER {TER}", ter);
            throw;
        }
    }

    /// <summary>
    /// Compare two CID configurations
    /// </summary>
    public async Task<CidComparisonResult> CompareCidsAsync(string ter, string cid1, string cid2)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (string.IsNullOrWhiteSpace(cid1))
            throw new ArgumentException("First CID cannot be null or empty", nameof(cid1));
        if (string.IsNullOrWhiteSpace(cid2))
            throw new ArgumentException("Second CID cannot be null or empty", nameof(cid2));

        try
        {
            var details1 = await GetTableDetailsForCidAsync(ter, cid1);
            var details2 = await GetTableDetailsForCidAsync(ter, cid2);

            var detailsList1 = details1.ToList();
            var detailsList2 = details2.ToList();

            var comparison = new CidComparisonResult
            {
                TER = ter,
                CID1 = cid1,
                CID2 = cid2,
                ComparedAt = DateTime.UtcNow,
                Differences = new List<CidDifference>()
            };

            // Get all tables from both CIDs
            var allTables = detailsList1.Select(d => d.Tavola)
                .Union(detailsList2.Select(d => d.Tavola))
                .Distinct()
                .OrderBy(t => t);

            foreach (var table in allTables)
            {
                var table1Parts = detailsList1.Where(d => d.Tavola == table).ToList();
                var table2Parts = detailsList2.Where(d => d.Tavola == table).ToList();

                if (!table1Parts.Any() && table2Parts.Any())
                {
                    comparison.Differences.Add(new CidDifference
                    {
                        Table = table,
                        DifferenceType = "TableAdded",
                        Description = $"Table {table} exists only in {cid2}",
                        CID1Value = null,
                        CID2Value = $"{table2Parts.Count} parts"
                    });
                }
                else if (table1Parts.Any() && !table2Parts.Any())
                {
                    comparison.Differences.Add(new CidDifference
                    {
                        Table = table,
                        DifferenceType = "TableRemoved",
                        Description = $"Table {table} exists only in {cid1}",
                        CID1Value = $"{table1Parts.Count} parts",
                        CID2Value = null
                    });
                }
                else if (table1Parts.Any() && table2Parts.Any())
                {
                    // Compare part counts
                    if (table1Parts.Count != table2Parts.Count)
                    {
                        comparison.Differences.Add(new CidDifference
                        {
                            Table = table,
                            DifferenceType = "PartCountDifference",
                            Description = $"Different part count in table {table}",
                            CID1Value = table1Parts.Count.ToString(),
                            CID2Value = table2Parts.Count.ToString()
                        });
                    }

                    // Compare individual parts (using Versione as part identifier)
                    var parts1 = table1Parts.Select(p => p.Versione).ToHashSet();
                    var parts2 = table2Parts.Select(p => p.Versione).ToHashSet();

                    var onlyInCid1 = parts1.Except(parts2);
                    var onlyInCid2 = parts2.Except(parts1);

                    foreach (var part in onlyInCid1)
                    {
                        comparison.Differences.Add(new CidDifference
                        {
                            Table = table,
                            Position = part,
                            DifferenceType = "PartRemoved",
                            Description = $"Part {part} in table {table} exists only in {cid1}",
                            CID1Value = "Present",
                            CID2Value = "Missing"
                        });
                    }

                    foreach (var part in onlyInCid2)
                    {
                        comparison.Differences.Add(new CidDifference
                        {
                            Table = table,
                            Position = part,
                            DifferenceType = "PartAdded",
                            Description = $"Part {part} in table {table} exists only in {cid2}",
                            CID1Value = "Missing",
                            CID2Value = "Present"
                        });
                    }
                }
            }

            _logger.LogInformation("Compared CIDs {CID1} and {CID2} for TER {TER}: found {Count} differences", 
                cid1, cid2, ter, comparison.Differences.Count);
            
            return comparison;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error comparing CIDs {CID1} and {CID2} for TER {TER}", cid1, cid2, ter);
            throw;
        }
    }

    /// <summary>
    /// Get filtered CIDs with advanced filtering options
    /// </summary>
    public async Task<PagedResult<TB_CID>> GetFilteredCidsAsync(CidFilter filter)
    {
        if (filter == null)
            throw new ArgumentNullException(nameof(filter));

        try
        {
            var query = _context.TB_CID.AsQueryable();

            // Apply TER filter
            if (!string.IsNullOrWhiteSpace(filter.TER))
                query = query.Where(c => c.TER == filter.TER);

            // Apply search text filter
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                var searchTerm = filter.SearchText.ToLower();
                query = query.Where(c => 
                    EF.Functions.Like(c.CID.ToLower(), $"%{searchTerm}%") ||
                    EF.Functions.Like((c.Titolo ?? "").ToLower(), $"%{searchTerm}%") ||
                    EF.Functions.Like((c.TitoloEN ?? "").ToLower(), $"%{searchTerm}%"));
            }

            // Apply date range filter
            if (filter.StartDate.HasValue)
                query = query.Where(c => c.Data >= filter.StartDate.Value);
            if (filter.EndDate.HasValue)
                query = query.Where(c => c.Data <= filter.EndDate.Value);

            // Apply language filter
            if (!string.IsNullOrWhiteSpace(filter.Language))
            {
                switch (filter.Language.ToUpper())
                {
                    case "EN":
                        query = query.Where(c => !string.IsNullOrWhiteSpace(c.TitoloEN));
                        break;
                    case "ES":
                        query = query.Where(c => !string.IsNullOrWhiteSpace(c.TitoloES));
                        break;
                    case "PT":
                        query = query.Where(c => !string.IsNullOrWhiteSpace(c.TitoloPT));
                        break;
                    case "FR":
                        query = query.Where(c => !string.IsNullOrWhiteSpace(c.TitoloFR));
                        break;
                    case "DE":
                    case "TED":
                        query = query.Where(c => !string.IsNullOrWhiteSpace(c.TitoloTED));
                        break;
                    case "USA":
                        query = query.Where(c => !string.IsNullOrWhiteSpace(c.TitoloUSA));
                        break;
                }
            }

            // Get total count before pagination
            var totalCount = await query.CountAsync();

            // Apply sorting
            switch (filter.SortBy?.ToLower())
            {
                case "date":
                    query = filter.SortDescending ? 
                        query.OrderByDescending(c => c.Data).ThenBy(c => c.TER).ThenBy(c => c.CID) :
                        query.OrderBy(c => c.Data).ThenBy(c => c.TER).ThenBy(c => c.CID);
                    break;
                case "title":
                    query = filter.SortDescending ? 
                        query.OrderByDescending(c => c.Titolo).ThenBy(c => c.TER).ThenBy(c => c.CID) :
                        query.OrderBy(c => c.Titolo).ThenBy(c => c.TER).ThenBy(c => c.CID);
                    break;
                default: // "cid" or null
                    query = filter.SortDescending ? 
                        query.OrderByDescending(c => c.TER).ThenByDescending(c => c.CID) :
                        query.OrderBy(c => c.TER).ThenBy(c => c.CID);
                    break;
            }

            // Apply pagination
            var pageSize = filter.PageSize > 0 ? filter.PageSize : 50;
            var page = filter.Page > 0 ? filter.Page : 1;
            var skip = (page - 1) * pageSize;

            var items = await query
                .Skip(skip)
                .Take(pageSize)
                .ToListAsync();

            var result = new PagedResult<TB_CID>
            {
                Items = items,
                TotalCount = totalCount,
                PageSize = pageSize,
                CurrentPage = page,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            _logger.LogInformation("Filtered CIDs: returned {Count} of {Total} items (page {Page}/{TotalPages})", 
                items.Count, totalCount, page, result.TotalPages);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error filtering CIDs with filter: {Filter}", 
                System.Text.Json.JsonSerializer.Serialize(filter));
            throw;
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Determine table status based on parts configuration
    /// </summary>
    private static TableVersionStatus DetermineTableStatus(List<TB_DettagliTavole> parts)
    {
        if (!parts.Any())
            return TableVersionStatus.Empty;

        var hasRecentChanges = parts.Any(p => p.Data.HasValue && 
            p.Data.Value > DateTime.UtcNow.AddDays(-30));

        var allPartsHaveData = parts.All(p => !string.IsNullOrWhiteSpace(p.Versione));

        if (hasRecentChanges)
            return TableVersionStatus.Modified;
        
        if (allPartsHaveData)
            return TableVersionStatus.Complete;
        
        return TableVersionStatus.Incomplete;
    }

    #endregion

    #region ICIDService Interface Implementation

    // Interface method mappings to existing methods
    public async Task<IEnumerable<TB_CID>> GetCIDsForCatalogAsync(string ter) => 
        await GetCidsByTerAsync(ter);

    public async Task<TB_CID?> GetCIDAsync(string ter, string cid) => 
        await GetCidAsync(ter, cid);

    public async Task<TB_CID> CreateCIDAsync(TB_CID cidModel) => 
        await CreateCidAsync(cidModel);

    public async Task<TB_CID> UpdateCIDAsync(TB_CID cidModel) => 
        await UpdateCidAsync(cidModel);

    public async Task<bool> DeleteCIDAsync(string ter, string cid) => 
        await DeleteCidAsync(ter, cid);

    public async Task<bool> CIDExistsAsync(string ter, string cid) => 
        await ExistsAsync(ter, cid);

    // Advanced CID interface methods
    public async Task<IEnumerable<CIDTableVersionModel>> GetTablesForCIDAsync(string ter, string cid)
    {
        try
        {
            var tableDetails = await GetTableDetailsForCidAsync(ter, cid);
            var detailsList = tableDetails.ToList();
            
            var tablesWithVersions = detailsList
                .GroupBy(dt => dt.Tavola)
                .Select(g => new CIDTableVersionModel
                {
                    Tavola = g.Key,
                    TER = ter,
                    CID = cid,
                    CurrentVersion = "1.0", // Default version
                    VersionStatus = "00", // Default status (Verde)
                    PartCount = g.Count(),
                    Data = g.Max(dt => dt.Data)
                })
                .OrderBy(t => t.Tavola)
                .ToList();

            _logger.LogInformation("Retrieved {Count} tables with versions for CID: TER {TER}, CID {CID}", 
                tablesWithVersions.Count, ter, cid);
            
            return tablesWithVersions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tables with versions for CID: TER {TER}, CID {CID}", ter, cid);
            throw;
        }
    }

    public async Task<CIDVersionMatrixModel> GetCIDVersionMatrixAsync(string ter, string cid)
    {
        try
        {
            var cidInfo = await GetCidAsync(ter, cid);
            if (cidInfo == null)
                throw new InvalidOperationException($"CID not found: TER {ter}, CID {cid}");

            var tables = await GetTablesForCIDAsync(ter, cid);
            var tablesList = tables.ToList();

            var matrix = new CIDVersionMatrixModel
            {
                TER = ter,
                CID = cid,
                CIDTitle = cidInfo.Titolo ?? cid,
                Tables = tablesList,
                StatusCounts = tablesList.GroupBy(t => t.VersionStatus)
                    .ToDictionary(g => g.Key, g => g.Count()),
                LastModified = tablesList.Where(t => t.Data.HasValue)
                    .Max(t => t.Data) ?? DateTime.MinValue
            };

            _logger.LogInformation("Generated CID version matrix for TER {TER}, CID {CID} with {Count} tables", 
                ter, cid, tablesList.Count);
            
            return matrix;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CID version matrix for TER {TER}, CID {CID}", ter, cid);
            throw;
        }
    }

    public async Task<IEnumerable<CIDPartModel>> GetPartsForCIDAsync(string ter, string cid)
    {
        try
        {
            var tableDetails = await GetTableDetailsForCidAsync(ter, cid);
            
            var parts = tableDetails.Select(dt => new CIDPartModel
            {
                TER = "N/A", // TB_DettagliTavole doesn't have TER
                CID = "N/A", // TB_DettagliTavole doesn't have CID
                Tavola = dt.Tavola,
                PART = dt.Versione ?? "", // Use Versione as PART identifier
                ITEM = "", // TB_DettagliTavole doesn't have ITEM
                Versione = "1.0", // Default version
                QTAV = 0, // TB_DettagliTavole doesn't have QTAV
                NotaRVT = "", // TB_DettagliTavole doesn't have NotaRVT
                Assieme = "", // TB_DettagliTavole doesn't have Assieme
                DescrIT = "", // TB_DettagliTavole doesn't have DescrIT
                DescrEN = "" // TB_DettagliTavole doesn't have DescrEN
            }).ToList();

            _logger.LogInformation("Retrieved {Count} parts for CID: TER {TER}, CID {CID}", 
                parts.Count, ter, cid);
            
            return parts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving parts for CID: TER {TER}, CID {CID}", ter, cid);
            throw;
        }
    }

    public async Task<CIDComparisonModel> CompareCIDConfigurationsAsync(string ter, string cidA, string cidB)
    {
        try
        {
            var partsA = await GetPartsForCIDAsync(ter, cidA);
            var partsB = await GetPartsForCIDAsync(ter, cidB);
            
            var partsListA = partsA.ToList();
            var partsListB = partsB.ToList();
            
            var comparison = new CIDComparisonModel
            {
                TER = ter,
                CID_A = cidA,
                CID_B = cidB
            };

            // Find identical parts (same table, part, version)
            var identicalParts = partsListA.Where(pA => 
                partsListB.Any(pB => pA.Tavola == pB.Tavola && pA.PART == pB.PART && pA.Versione == pB.Versione))
                .ToList();
            comparison.IdenticalParts = identicalParts;

            // Find parts with different versions
            var differentVersions = partsListA.Where(pA => 
                partsListB.Any(pB => pA.Tavola == pB.Tavola && pA.PART == pB.PART && pA.Versione != pB.Versione))
                .ToList();
            comparison.DifferentVersions = differentVersions;

            // Find parts only in CID A
            var onlyInA = partsListA.Where(pA => 
                !partsListB.Any(pB => pA.Tavola == pB.Tavola && pA.PART == pB.PART))
                .ToList();
            comparison.OnlyInCID_A = onlyInA;

            // Find parts only in CID B
            var onlyInB = partsListB.Where(pB => 
                !partsListA.Any(pA => pB.Tavola == pA.Tavola && pB.PART == pA.PART))
                .ToList();
            comparison.OnlyInCID_B = onlyInB;

            _logger.LogInformation("Compared CID configurations {CidA} and {CidB}: {Identical} identical, {Different} different versions, {OnlyA} unique to A, {OnlyB} unique to B", 
                cidA, cidB, identicalParts.Count, differentVersions.Count, onlyInA.Count, onlyInB.Count);
            
            return comparison;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error comparing CID configurations {CidA} and {CidB} for TER {TER}", cidA, cidB, ter);
            throw;
        }
    }

    public async Task<bool> CreateNewTableVersionForCIDAsync(string ter, string cid, string tavola, string newVersion)
    {
        try
        {
            // This would involve complex version management logic
            // For now, return success - would need to implement proper version creation
            _logger.LogInformation("Creating new version {Version} for table {Table} in CID {CID}", 
                newVersion, tavola, cid);
            
            // TODO: Implement version creation logic
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating new version for table {Table} in CID {CID}", tavola, cid);
            return false;
        }
    }

    public async Task<bool> UpdateTableVersionStatusAsync(string tavola, string version, string status)
    {
        try
        {
            // TODO: Implement table version status update
            _logger.LogInformation("Updating version status for table {Table} version {Version} to {Status}", 
                tavola, version, status);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating version status for table {Table}", tavola);
            return false;
        }
    }

    public async Task<IEnumerable<TableVersionHistoryModel>> GetTableVersionHistoryAsync(string ter, string cid, string tavola)
    {
        try
        {
            // TODO: Implement version history retrieval
            var history = new List<TableVersionHistoryModel>
            {
                new TableVersionHistoryModel
                {
                    Tavola = tavola,
                    Versione = "1.0",
                    Status = "00",
                    Data = DateTime.UtcNow,
                    PartCount = 0
                }
            };
            
            return history;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving version history for table {Table}", tavola);
            throw;
        }
    }

    public async Task<CIDStatisticsModel> GetCIDStatisticsAsync(string ter, string cid)
    {
        try
        {
            var parts = await GetPartsForCIDAsync(ter, cid);
            var partsList = parts.ToList();
            
            var stats = new CIDStatisticsModel
            {
                TER = ter,
                CID = cid,
                TablesUsed = partsList.Select(p => p.Tavola).Distinct().Count(),
                TotalParts = partsList.Count,
                UniqueParts = partsList.Count, // Simplified - would need complex analysis
                SharedParts = 0, // Simplified - would need cross-CID analysis
                LastModified = DateTime.UtcNow,
                VersionStatusCounts = new Dictionary<string, int> { ["00"] = partsList.Count }
            };
            
            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CID statistics for TER {TER}, CID {CID}", ter, cid);
            throw;
        }
    }

    public async Task<IEnumerable<CIDOverviewModel>> GetCatalogCIDOverviewAsync(string ter)
    {
        try
        {
            var cids = await GetCidsByTerAsync(ter);
            var overview = new List<CIDOverviewModel>();
            
            foreach (var cid in cids)
            {
                var statistics = await GetCIDStatisticsAsync(ter, cid.CID);
                overview.Add(new CIDOverviewModel
                {
                    TER = ter,
                    CID = cid.CID,
                    Titolo = cid.Titolo,
                    Applicabilità = cid.Applicabilità,
                    Data = cid.Data,
                    Statistics = statistics,
                    IsActive = true
                });
            }
            
            return overview;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating catalog CID overview for TER {TER}", ter);
            throw;
        }
    }

    public async Task<IEnumerable<CIDPartModel>> SearchPartsInCIDAsync(string ter, string cid, string searchQuery)
    {
        try
        {
            var allParts = await GetPartsForCIDAsync(ter, cid);
            var searchTerm = searchQuery.ToLower();
            
            var filteredParts = allParts.Where(p => 
                p.PART.ToLower().Contains(searchTerm) ||
                (p.DescrIT ?? "").ToLower().Contains(searchTerm) ||
                (p.DescrEN ?? "").ToLower().Contains(searchTerm))
                .ToList();
            
            return filteredParts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching parts in CID {CID} with query {Query}", cid, searchQuery);
            throw;
        }
    }

    public async Task<IEnumerable<CIDPartModel>> GetUniqueCIDPartsAsync(string ter, string cid)
    {
        try
        {
            // TODO: Implement complex logic to find parts unique to this CID
            var parts = await GetPartsForCIDAsync(ter, cid);
            
            // Mark all as unique for now - would need cross-CID analysis
            foreach (var part in parts)
            {
                part.IsUniqueToCID = true;
            }
            
            return parts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unique parts for CID {CID}", cid);
            throw;
        }
    }

    public async Task<IEnumerable<CIDPartModel>> GetSharedCIDPartsAsync(string ter)
    {
        try
        {
            // TODO: Implement complex logic to find parts shared across CIDs
            return new List<CIDPartModel>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shared CID parts for TER {TER}", ter);
            throw;
        }
    }

    #endregion
}
