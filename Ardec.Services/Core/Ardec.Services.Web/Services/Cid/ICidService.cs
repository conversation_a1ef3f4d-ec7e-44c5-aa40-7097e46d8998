using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Models;

namespace Ardec.Services.Web.Services.CID;

/// <summary>
/// Interface for CID (Change Implementation Document) Service
/// Manages configurations/variants for catalogs with complex versioning logic
/// </summary>
public interface ICIDService
{
    #region Basic CRUD Operations
    
    /// <summary>
    /// Get all CID configurations for a specific catalog
    /// </summary>
    Task<IEnumerable<TB_CID>> GetCIDsForCatalogAsync(string ter);
    
    /// <summary>
    /// Get specific CID by TER and CID
    /// </summary>
    Task<TB_CID?> GetCIDAsync(string ter, string cid);
    
    /// <summary>
    /// Create new CID configuration
    /// </summary>
    Task<TB_CID> CreateCIDAsync(TB_CID cidModel);
    
    /// <summary>
    /// Update existing CID
    /// </summary>
    Task<TB_CID> UpdateCIDAsync(TB_CID cidModel);
    
    /// <summary>
    /// Delete CID and all related data
    /// </summary>
    Task<bool> DeleteCIDAsync(string ter, string cid);
    
    /// <summary>
    /// Check if CID exists
    /// </summary>
    Task<bool> CIDExistsAsync(string ter, string cid);
    
    #endregion

    #region CID-Based Queries
    
    /// <summary>
    /// Get all tables (with versions) for a specific CID configuration
    /// Returns tables ordered by nTavola with their current version status
    /// </summary>
    Task<IEnumerable<CIDTableVersionModel>> GetTablesForCIDAsync(string ter, string cid);
    
    /// <summary>
    /// Get table version matrix for CID - shows all tables with their version states
    /// Essential for the Version Dashboard (Cruscotto Versioni)
    /// </summary>
    Task<CIDVersionMatrixModel> GetCIDVersionMatrixAsync(string ter, string cid);
    
    /// <summary>
    /// Get parts specific to a CID configuration
    /// </summary>
    Task<IEnumerable<CIDPartModel>> GetPartsForCIDAsync(string ter, string cid);
    
    /// <summary>
    /// Compare parts between two CID configurations
    /// Shows differences, identical parts, and CID-specific parts
    /// </summary>
    Task<CIDComparisonModel> CompareCIDConfigurationsAsync(string ter, string cidA, string cidB);
    
    #endregion

    #region Version Management
    
    /// <summary>
    /// Create new table version for specific CID
    /// Copies all parts from previous version and maintains CID relationships
    /// </summary>
    Task<bool> CreateNewTableVersionForCIDAsync(string ter, string cid, string tavola, string newVersion);
    
    /// <summary>
    /// Update table version status (00=Verde, 01=Giallo, 02=Blu, 03=Rosso)
    /// </summary>
    Task<bool> UpdateTableVersionStatusAsync(string tavola, string version, string status);
    
    /// <summary>
    /// Get version history for a table within a CID context
    /// </summary>
    Task<IEnumerable<TableVersionHistoryModel>> GetTableVersionHistoryAsync(string ter, string cid, string tavola);
    
    #endregion

    #region Statistics and Analytics
    
    /// <summary>
    /// Get CID statistics - parts count, tables used, etc.
    /// </summary>
    Task<CIDStatisticsModel> GetCIDStatisticsAsync(string ter, string cid);
    
    /// <summary>
    /// Get catalog-wide CID overview with all configurations
    /// </summary>
    Task<IEnumerable<CIDOverviewModel>> GetCatalogCIDOverviewAsync(string ter);
    
    #endregion

    #region Search and Filtering
    
    /// <summary>
    /// Search parts across all CID configurations for a catalog
    /// </summary>
    Task<IEnumerable<CIDPartModel>> SearchPartsInCIDAsync(string ter, string cid, string searchQuery);
    
    /// <summary>
    /// Get unique parts (parts that exist only in specific CID)
    /// </summary>
    Task<IEnumerable<CIDPartModel>> GetUniqueCIDPartsAsync(string ter, string cid);
    
    /// <summary>
    /// Get shared parts (parts common across multiple CIDs)
    /// </summary>
    Task<IEnumerable<CIDPartModel>> GetSharedCIDPartsAsync(string ter);
    
    #endregion
    
    #region Additional CRUD Methods
    
    /// <summary>
    /// Get all CID entries (Admin view)
    /// </summary>
    Task<IEnumerable<TB_CID>> GetAllCidsAsync();
    
    /// <summary>
    /// Get all CIDs for a specific catalog (TER)
    /// </summary>
    Task<IEnumerable<TB_CID>> GetCidsByTerAsync(string ter);
    
    /// <summary>
    /// Get specific CID by composite key TER + CID
    /// </summary>
    Task<TB_CID?> GetCidAsync(string ter, string cid);
    
    /// <summary>
    /// Check if CID exists for specific TER
    /// </summary>
    Task<bool> ExistsAsync(string ter, string cid);
    
    /// <summary>
    /// Create new CID entry
    /// </summary>
    Task<TB_CID> CreateCidAsync(TB_CID cidEntry);
    
    /// <summary>
    /// Update existing CID entry
    /// </summary>
    Task<TB_CID> UpdateCidAsync(TB_CID cidEntry);
    
    /// <summary>
    /// Delete CID entry
    /// </summary>
    Task<bool> DeleteCidAsync(string ter, string cid);
    
    /// <summary>
    /// Search CIDs by text (title, applicability, etc.)
    /// </summary>
    Task<IEnumerable<TB_CID>> SearchCidsAsync(string searchQuery);
    
    /// <summary>
    /// Get CIDs with multilingual title support
    /// </summary>
    Task<IEnumerable<TB_CID>> GetCidsWithLanguageAsync(string ter, string language);
    
    /// <summary>
    /// Get CID statistics for a catalog
    /// </summary>
    Task<object> GetCidStatisticsAsync(string ter);
    
    /// <summary>
    /// Get CIDs by date range
    /// </summary>
    Task<IEnumerable<TB_CID>> GetCidsByDateRangeAsync(string ter, DateTime? startDate, DateTime? endDate);
    
    /// <summary>
    /// Bulk update CIDs for a catalog
    /// </summary>
    Task<int> BulkUpdateCidsAsync(string ter, IEnumerable<TB_CID> cids);
    
    #endregion
}

#region DTOs and Models for CID Operations

/// <summary>
/// Model for table with CID-specific version information
/// </summary>
public class CIDTableVersionModel
{
    public string Tavola { get; set; } = string.Empty;
    public double? NTavola { get; set; }
    public string TER { get; set; } = string.Empty;
    public string CID { get; set; } = string.Empty;
    public string CurrentVersion { get; set; } = string.Empty;
    public string VersionStatus { get; set; } = string.Empty; // 00, 01, 02, 03
    public string StatusColor => GetStatusColor(VersionStatus);
    public string StatusLabel => GetStatusLabel(VersionStatus);
    
    // Legacy compatibility properties for existing Views
    public string Versione => CurrentVersion;
    public string Stato => VersionStatus;
    public string? GruppoITA { get; set; }
    public string? GruppoENG { get; set; }
    public string? GruppoFRA { get; set; }
    public string? GruppoPOR { get; set; }
    public string? GruppoESP { get; set; }
    public string? GruppoTED { get; set; }
    public string? GruppoUSA { get; set; }
    public double? CEDNPagina { get; set; }
    public string? CEDIGNPagina { get; set; }
    
    // Table details
    public string? Descrizione1IT { get; set; }
    public string? Descrizione2IT { get; set; }
    public string? CodiceTecnico { get; set; }
    public DateTime? Data { get; set; }
    public string? Logo { get; set; }
    public string? Figura { get; set; }
    public int PartCount { get; set; } // Number of parts for this CID
    
    private static string GetStatusColor(string status) => status switch
    {
        "00" => "green",   // Base - Verde
        "01" => "yellow",  // In lavorazione - Giallo  
        "02" => "blue",    // Revisione - Blu
        "03" => "red",     // Problemi - Rosso
        _ => "gray"
    };
    
    private static string GetStatusLabel(string status) => status switch
    {
        "00" => "Base",
        "01" => "In Lavorazione",
        "02" => "Revisione", 
        "03" => "Problemi",
        _ => "Sconosciuto"
    };
}

/// <summary>
/// Model for CID version matrix (Cruscotto Versioni)
/// </summary>
public class CIDVersionMatrixModel
{
    public string TER { get; set; } = string.Empty;
    public string CID { get; set; } = string.Empty;
    public string CIDTitle { get; set; } = string.Empty;
    public List<CIDTableVersionModel> Tables { get; set; } = new();
    public Dictionary<string, int> StatusCounts { get; set; } = new(); // "00" -> count
    public DateTime LastModified { get; set; }
    
    public int TotalTables => Tables.Count;
    public int GreenTables => StatusCounts.GetValueOrDefault("00", 0);
    public int YellowTables => StatusCounts.GetValueOrDefault("01", 0);
    public int BlueTables => StatusCounts.GetValueOrDefault("02", 0);
    public int RedTables => StatusCounts.GetValueOrDefault("03", 0);
}

/// <summary>
/// Model for CID-specific parts
/// </summary>
public class CIDPartModel
{
    public string TER { get; set; } = string.Empty;
    public string CID { get; set; } = string.Empty;
    public string Tavola { get; set; } = string.Empty;
    public string PART { get; set; } = string.Empty;
    public string ITEM { get; set; } = string.Empty;
    public string Versione { get; set; } = string.Empty;
    public int? QTAV { get; set; }
    public string? NotaRVT { get; set; }
    public string? Assieme { get; set; }
    
    // Component description
    public string? DescrIT { get; set; }
    public string? DescrEN { get; set; }
    
    // Classification
    public bool IsUniqueToCID { get; set; } // Only exists in this CID
    public bool IsSharedAcrossCIDs { get; set; } // Common to multiple CIDs
    public List<string> OtherCIDsWithSamePart { get; set; } = new();
}

/// <summary>
/// Model for comparing two CID configurations
/// </summary>
public class CIDComparisonModel
{
    public string TER { get; set; } = string.Empty;
    public string CID_A { get; set; } = string.Empty;
    public string CID_B { get; set; } = string.Empty;
    
    public List<CIDPartModel> IdenticalParts { get; set; } = new(); // Same version in both
    public List<CIDPartModel> DifferentVersions { get; set; } = new(); // Different versions
    public List<CIDPartModel> OnlyInCID_A { get; set; } = new(); // Unique to CID A
    public List<CIDPartModel> OnlyInCID_B { get; set; } = new(); // Unique to CID B
    
    public int IdenticalCount => IdenticalParts.Count;
    public int DifferentCount => DifferentVersions.Count;
    public int UniqueToACount => OnlyInCID_A.Count;
    public int UniqueToBCount => OnlyInCID_B.Count;
    public double SimilarityPercentage => IdenticalCount > 0 ? 
        (double)IdenticalCount / (IdenticalCount + DifferentCount + UniqueToACount + UniqueToBCount) * 100 : 0;
}

/// <summary>
/// Model for table version history
/// </summary>
public class TableVersionHistoryModel
{
    public string Tavola { get; set; } = string.Empty;
    public string Versione { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? Data { get; set; }
    public string? ModifiedBy { get; set; }
    public string? ChangeReason { get; set; }
    public int PartCount { get; set; }
}

/// <summary>
/// Model for CID statistics
/// </summary>
public class CIDStatisticsModel
{
    public string TER { get; set; } = string.Empty;
    public string CID { get; set; } = string.Empty;
    public int TablesUsed { get; set; }
    public int TotalParts { get; set; }
    public int UniqueParts { get; set; }
    public int SharedParts { get; set; }
    public DateTime LastModified { get; set; }
    public Dictionary<string, int> VersionStatusCounts { get; set; } = new();
}

/// <summary>
/// Model for catalog CID overview
/// </summary>
public class CIDOverviewModel
{
    public string TER { get; set; } = string.Empty;
    public string CID { get; set; } = string.Empty;
    public string? Titolo { get; set; }
    public string? Applicabilità { get; set; }
    public DateTime? Data { get; set; }
    public CIDStatisticsModel Statistics { get; set; } = new();
    public bool IsActive { get; set; } = true;
}

#endregion
