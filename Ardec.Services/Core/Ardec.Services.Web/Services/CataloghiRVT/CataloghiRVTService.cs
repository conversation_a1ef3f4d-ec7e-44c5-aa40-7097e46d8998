using Microsoft.EntityFrameworkCore;
using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.DTOs.CataloghiRVT;

namespace Ardec.Services.Web.Services.CataloghiRVT;

/// <summary>
/// Servizio per la gestione delle operazioni CRUD su TB_CataloghiRVT
/// </summary>
public class CataloghiRVTService : ICataloghiRVTService
{
    private readonly ArdecDbContext _context;
    private readonly ILogger<CataloghiRVTService> _logger;

    public CataloghiRVTService(
        ArdecDbContext context,
        ILogger<CataloghiRVTService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<CataloghiRVTPagedResult> GetAllAsync(int skip = 0, int take = 20, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Recupero di tutti i CataloghiRVT con paginazione. Skip: {Skip}, Take: {Take}", skip, take);

        var query = _context.TB_CataloghiRVT.AsNoTracking();

        var totalCount = await query.CountAsync(cancellationToken);
        
        var entities = await query
            .OrderBy(x => x.TER)
            .ThenBy(x => x.RVT)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
        
        var items = entities.Select(MapToResponse).ToList();

        return new CataloghiRVTPagedResult
        {
            Items = items,
            TotalCount = totalCount,
            Skip = skip,
            Take = take
        };
    }

    public async Task<CataloghiRVTResponse?> GetByKeysAsync(string ter, string rvt, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca CatalogoRVT per TER: {TER} e RVT: {RVT}", ter, rvt);

        var entity = await _context.TB_CataloghiRVT
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.TER == ter && x.RVT == rvt, cancellationToken);

        return entity != null ? MapToResponse(entity) : null;
    }

    public async Task<CataloghiRVTPagedResult> SearchAsync(CataloghiRVTSearchRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca CataloghiRVT con filtri: {@Request}", request);

        var query = _context.TB_CataloghiRVT.AsNoTracking();

        // Filtri specifici
        if (!string.IsNullOrWhiteSpace(request.TER))
        {
            query = query.Where(x => EF.Functions.Like(x.TER, $"%{request.TER}%"));
        }

        if (!string.IsNullOrWhiteSpace(request.RVT))
        {
            query = query.Where(x => EF.Functions.Like(x.RVT, $"%{request.RVT}%"));
        }

        if (!string.IsNullOrWhiteSpace(request.Applicabilità))
        {
            query = query.Where(x => x.Applicabilità != null && 
                EF.Functions.Like(x.Applicabilità, $"%{request.Applicabilità}%"));
        }

        // Ricerca generale
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = $"%{request.SearchTerm}%";
            query = query.Where(x => 
                EF.Functions.Like(x.TER, searchTerm) ||
                EF.Functions.Like(x.RVT, searchTerm) ||
                (x.Applicabilità != null && EF.Functions.Like(x.Applicabilità, searchTerm)) ||
                (x.ApplicabilitàEN != null && EF.Functions.Like(x.ApplicabilitàEN, searchTerm)) ||
                (x.ApplicabilitàES != null && EF.Functions.Like(x.ApplicabilitàES, searchTerm)) ||
                (x.ApplicabilitàPT != null && EF.Functions.Like(x.ApplicabilitàPT, searchTerm)) ||
                (x.ApplicabilitàFR != null && EF.Functions.Like(x.ApplicabilitàFR, searchTerm)) ||
                (x.ApplicabilitàUSA != null && EF.Functions.Like(x.ApplicabilitàUSA, searchTerm)));
        }

        // Ordinamento
        query = request.SortBy.ToLower() switch
        {
            "rvt" => request.SortDescending ? query.OrderByDescending(x => x.RVT) : query.OrderBy(x => x.RVT),
            "applicabilità" => request.SortDescending ? query.OrderByDescending(x => x.Applicabilità) : query.OrderBy(x => x.Applicabilità),
            _ => request.SortDescending ? query.OrderByDescending(x => x.TER) : query.OrderBy(x => x.TER)
        };

        var totalCount = await query.CountAsync(cancellationToken);

        var entities = await query
            .Skip(request.Skip)
            .Take(request.Take)
            .ToListAsync(cancellationToken);
        
        var items = entities.Select(MapToResponse).ToList();

        return new CataloghiRVTPagedResult
        {
            Items = items,
            TotalCount = totalCount,
            Skip = request.Skip,
            Take = request.Take
        };
    }

    public async Task<List<CataloghiRVTResponse>> GetByTerAsync(string ter, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca CataloghiRVT per TER: {TER}", ter);

        var entities = await _context.TB_CataloghiRVT
            .AsNoTracking()
            .Where(x => x.TER == ter)
            .OrderBy(x => x.RVT)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<List<CataloghiRVTResponse>> GetByRvtAsync(string rvt, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca CataloghiRVT per RVT: {RVT}", rvt);

        var entities = await _context.TB_CataloghiRVT
            .AsNoTracking()
            .Where(x => x.RVT == rvt)
            .OrderBy(x => x.TER)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<CataloghiRVTResponse> CreateAsync(CreateCataloghiRVTRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creazione nuovo CatalogoRVT: {@Request}", request);

        // Verifica che non esista già
        var exists = await ExistsAsync(request.TER, request.RVT, cancellationToken);
        if (exists)
        {
            throw new InvalidOperationException($"Record con TER '{request.TER}' e RVT '{request.RVT}' già esistente");
        }

        var entity = new TB_CataloghiRVT
        {
            TER = request.TER,
            RVT = request.RVT,
            Applicabilità = request.Applicabilità,
            ApplicabilitàEN = request.ApplicabilitàEN,
            ApplicabilitàES = request.ApplicabilitàES,
            ApplicabilitàPT = request.ApplicabilitàPT,
            ApplicabilitàFR = request.ApplicabilitàFR,
            ApplicabilitàUSA = request.ApplicabilitàUSA
        };

        _context.TB_CataloghiRVT.Add(entity);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("CatalogoRVT creato con successo: TER={TER}, RVT={RVT}", request.TER, request.RVT);

        return MapToResponse(entity);
    }

    public async Task<CataloghiRVTResponse?> UpdateAsync(string ter, string rvt, UpdateCataloghiRVTRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Aggiornamento CatalogoRVT TER: {TER}, RVT: {RVT}", ter, rvt);

        var entity = await _context.TB_CataloghiRVT
            .FirstOrDefaultAsync(x => x.TER == ter && x.RVT == rvt, cancellationToken);

        if (entity == null)
        {
            _logger.LogWarning("CatalogoRVT non trovato per l'aggiornamento: TER={TER}, RVT={RVT}", ter, rvt);
            return null;
        }

        entity.RVT = request.RVT;
        entity.Applicabilità = request.Applicabilità;
        entity.ApplicabilitàEN = request.ApplicabilitàEN;
        entity.ApplicabilitàES = request.ApplicabilitàES;
        entity.ApplicabilitàPT = request.ApplicabilitàPT;
        entity.ApplicabilitàFR = request.ApplicabilitàFR;
        entity.ApplicabilitàUSA = request.ApplicabilitàUSA;

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("CatalogoRVT aggiornato con successo: TER={TER}, RVT={RVT}", ter, rvt);

        return MapToResponse(entity);
    }

    public async Task<bool> DeleteAsync(string ter, string rvt, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Eliminazione CatalogoRVT TER: {TER}, RVT: {RVT}", ter, rvt);

        var entity = await _context.TB_CataloghiRVT
            .FirstOrDefaultAsync(x => x.TER == ter && x.RVT == rvt, cancellationToken);

        if (entity == null)
        {
            _logger.LogWarning("CatalogoRVT non trovato per l'eliminazione: TER={TER}, RVT={RVT}", ter, rvt);
            return false;
        }

        _context.TB_CataloghiRVT.Remove(entity);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("CatalogoRVT eliminato con successo: TER={TER}, RVT={RVT}", ter, rvt);

        return true;
    }

    public async Task<bool> ExistsAsync(string ter, string rvt, CancellationToken cancellationToken = default)
    {
        return await _context.TB_CataloghiRVT
            .AsNoTracking()
            .AnyAsync(x => x.TER == ter && x.RVT == rvt, cancellationToken);
    }

    public async Task<CataloghiRVTStatsResponse> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generazione statistiche per CataloghiRVT");

        var totalRecords = await _context.TB_CataloghiRVT.CountAsync(cancellationToken);
        
        var uniqueTER = await _context.TB_CataloghiRVT
            .Select(x => x.TER)
            .Distinct()
            .CountAsync(cancellationToken);

        var uniqueRVT = await _context.TB_CataloghiRVT
            .Select(x => x.RVT)
            .Distinct()
            .CountAsync(cancellationToken);

        var recordsWithApplicabilità = await _context.TB_CataloghiRVT
            .Where(x => !string.IsNullOrWhiteSpace(x.Applicabilità))
            .CountAsync(cancellationToken);

        var recordsWithMultiLanguage = await _context.TB_CataloghiRVT
            .Where(x => !string.IsNullOrWhiteSpace(x.ApplicabilitàEN) ||
                       !string.IsNullOrWhiteSpace(x.ApplicabilitàES) ||
                       !string.IsNullOrWhiteSpace(x.ApplicabilitàPT) ||
                       !string.IsNullOrWhiteSpace(x.ApplicabilitàFR) ||
                       !string.IsNullOrWhiteSpace(x.ApplicabilitàUSA))
            .CountAsync(cancellationToken);

        var topTER = await _context.TB_CataloghiRVT
            .GroupBy(x => x.TER)
            .OrderByDescending(g => g.Count())
            .Take(10)
            .Select(g => g.Key)
            .ToListAsync(cancellationToken);

        var topRVT = await _context.TB_CataloghiRVT
            .GroupBy(x => x.RVT)
            .OrderByDescending(g => g.Count())
            .Take(10)
            .Select(g => g.Key)
            .ToListAsync(cancellationToken);

        return new CataloghiRVTStatsResponse
        {
            TotalRecords = totalRecords,
            UniqueTER = uniqueTER,
            UniqueRVT = uniqueRVT,
            RecordsWithApplicabilità = recordsWithApplicabilità,
            RecordsWithMultiLanguage = recordsWithMultiLanguage,
            TopTER = topTER,
            TopRVT = topRVT
        };
    }

    private static readonly Func<TB_CataloghiRVT, CataloghiRVTResponse> MapToResponse = entity => new()
    {
        TER = entity.TER,
        RVT = entity.RVT,
        Applicabilità = entity.Applicabilità,
        ApplicabilitàEN = entity.ApplicabilitàEN,
        ApplicabilitàES = entity.ApplicabilitàES,
        ApplicabilitàPT = entity.ApplicabilitàPT,
        ApplicabilitàFR = entity.ApplicabilitàFR,
        ApplicabilitàUSA = entity.ApplicabilitàUSA
    };
}
