using Ardec.Services.Web.DTOs.CataloghiRVT;

namespace Ardec.Services.Web.Services.CataloghiRVT;

/// <summary>
/// Interface per il servizio di gestione TB_CataloghiRVT
/// </summary>
public interface ICataloghiRVTService
{
    /// <summary>
    /// Ottiene tutti i record con paginazione
    /// </summary>
    Task<CataloghiRVTPagedResult> GetAllAsync(int skip = 0, int take = 20, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene un record per TER e RVT
    /// </summary>
    Task<CataloghiRVTResponse?> GetByKeysAsync(string ter, string rvt, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cerca record con filtri
    /// </summary>
    Task<CataloghiRVTPagedResult> SearchAsync(CataloghiRVTSearchRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene tutti i record per un TER specifico
    /// </summary>
    Task<List<CataloghiRVTResponse>> GetByTerAsync(string ter, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene tutti i record per un RVT specifico
    /// </summary>
    Task<List<CataloghiRVTResponse>> GetByRvtAsync(string rvt, CancellationToken cancellationToken = default);

    /// <summary>
    /// Crea un nuovo record
    /// </summary>
    Task<CataloghiRVTResponse> CreateAsync(CreateCataloghiRVTRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Aggiorna un record esistente
    /// </summary>
    Task<CataloghiRVTResponse?> UpdateAsync(string ter, string rvt, UpdateCataloghiRVTRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Elimina un record
    /// </summary>
    Task<bool> DeleteAsync(string ter, string rvt, CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica se esiste un record
    /// </summary>
    Task<bool> ExistsAsync(string ter, string rvt, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene statistiche dei record
    /// </summary>
    Task<CataloghiRVTStatsResponse> GetStatsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// DTO per le statistiche di TB_CataloghiRVT
/// </summary>
public record CataloghiRVTStatsResponse
{
    public int TotalRecords { get; init; }
    public int UniqueTER { get; init; }
    public int UniqueRVT { get; init; }
    public int RecordsWithApplicabilità { get; init; }
    public int RecordsWithMultiLanguage { get; init; }
    public List<string> TopTER { get; init; } = [];
    public List<string> TopRVT { get; init; } = [];
}
