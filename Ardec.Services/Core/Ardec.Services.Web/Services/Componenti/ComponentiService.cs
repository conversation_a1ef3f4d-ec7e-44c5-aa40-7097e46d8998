using Microsoft.EntityFrameworkCore;
using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.DTOs.Componenti;

namespace Ardec.Services.Web.Services.Componenti;

/// <summary>
/// Servizio per la gestione delle operazioni CRUD su TB_Componenti
/// </summary>
public class ComponentiService : IComponentiService
{
    private readonly ArdecDbContext _context;
    private readonly ILogger<ComponentiService> _logger;

    public ComponentiService(
        ArdecDbContext context,
        ILogger<ComponentiService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<ComponentiPagedResult> GetAllAsync(int skip = 0, int take = 20, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Recupero di tutti i componenti con paginazione. Skip: {Skip}, Take: {Take}", skip, take);

        var query = _context.TB_Componenti.AsNoTracking();

        var totalCount = await query.CountAsync(cancellationToken);
        
        var entities = await query
            .OrderBy(x => x.PART)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
        
        var items = entities.Select(MapToResponse).ToList();

        return new ComponentiPagedResult
        {
            Items = items,
            TotalCount = totalCount,
            Skip = skip,
            Take = take
        };
    }

    public async Task<ComponentiResponse?> GetByPartAsync(string part, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca componente per PART: {PART}", part);

        var entity = await _context.TB_Componenti
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.PART == part, cancellationToken);

        return entity != null ? MapToResponse(entity) : null;
    }

    public async Task<ComponentiPagedResult> SearchAsync(ComponentiSearchRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca componenti con filtri: {@Request}", request);

        var query = _context.TB_Componenti.AsNoTracking();

        // Filtri specifici
        if (!string.IsNullOrWhiteSpace(request.PART))
        {
            query = query.Where(x => EF.Functions.Like(x.PART, $"%{request.PART}%"));
        }

        if (!string.IsNullOrWhiteSpace(request.NUC))
        {
            query = query.Where(x => x.NUC != null && EF.Functions.Like(x.NUC, $"%{request.NUC}%"));
        }

        if (!string.IsNullOrWhiteSpace(request.CodF))
        {
            query = query.Where(x => x.CodF != null && EF.Functions.Like(x.CodF, $"%{request.CodF}%"));
        }

        if (!string.IsNullOrWhiteSpace(request.PARTF))
        {
            query = query.Where(x => x.PARTF != null && EF.Functions.Like(x.PARTF, $"%{request.PARTF}%"));
        }

        if (!string.IsNullOrWhiteSpace(request.Tabella))
        {
            query = query.Where(x => x.Tabella != null && EF.Functions.Like(x.Tabella, $"%{request.Tabella}%"));
        }

        if (!string.IsNullOrWhiteSpace(request.Origine))
        {
            query = query.Where(x => x.Origine != null && EF.Functions.Like(x.Origine, $"%{request.Origine}%"));
        }

        if (!string.IsNullOrWhiteSpace(request.TONumber))
        {
            query = query.Where(x => x.TONumber != null && EF.Functions.Like(x.TONumber, $"%{request.TONumber}%"));
        }

        // Ricerca generale con supporto multilingue
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = $"%{request.SearchTerm}%";
            query = query.Where(x => 
                EF.Functions.Like(x.PART, searchTerm) ||
                (x.NUC != null && EF.Functions.Like(x.NUC, searchTerm)) ||
                (x.CodF != null && EF.Functions.Like(x.CodF, searchTerm)) ||
                (x.PARTF != null && EF.Functions.Like(x.PARTF, searchTerm)) ||
                (x.Tabella != null && EF.Functions.Like(x.Tabella, searchTerm)) ||
                (x.Origine != null && EF.Functions.Like(x.Origine, searchTerm)) ||
                (x.TONumber != null && EF.Functions.Like(x.TONumber, searchTerm)) ||
                
                // Ricerca nelle descrizioni
                (x.DescrIT != null && EF.Functions.Like(x.DescrIT, searchTerm)) ||
                (x.DescrEN != null && EF.Functions.Like(x.DescrEN, searchTerm)) ||
                (x.DescrES != null && EF.Functions.Like(x.DescrES, searchTerm)) ||
                (x.DescrPORT != null && EF.Functions.Like(x.DescrPORT, searchTerm)) ||
                (x.DescrFR != null && EF.Functions.Like(x.DescrFR, searchTerm)) ||
                (x.DescrTED != null && EF.Functions.Like(x.DescrTED, searchTerm)) ||
                (x.DescrUSA != null && EF.Functions.Like(x.DescrUSA, searchTerm)) ||
                
                // Ricerca nelle note
                (x.NotaIT != null && EF.Functions.Like(x.NotaIT, searchTerm)) ||
                (x.NotaEN != null && EF.Functions.Like(x.NotaEN, searchTerm)) ||
                (x.NotaES != null && EF.Functions.Like(x.NotaES, searchTerm)) ||
                (x.NotaPORT != null && EF.Functions.Like(x.NotaPORT, searchTerm)) ||
                (x.NotaFR != null && EF.Functions.Like(x.NotaFR, searchTerm)) ||
                (x.NotaTED != null && EF.Functions.Like(x.NotaTED, searchTerm)) ||
                (x.NotaUSA != null && EF.Functions.Like(x.NotaUSA, searchTerm)));
        }

        // Ordinamento
        query = request.SortBy.ToLower() switch
        {
            "nuc" => request.SortDescending ? query.OrderByDescending(x => x.NUC) : query.OrderBy(x => x.NUC),
            "codf" => request.SortDescending ? query.OrderByDescending(x => x.CodF) : query.OrderBy(x => x.CodF),
            "partf" => request.SortDescending ? query.OrderByDescending(x => x.PARTF) : query.OrderBy(x => x.PARTF),
            "tabella" => request.SortDescending ? query.OrderByDescending(x => x.Tabella) : query.OrderBy(x => x.Tabella),
            "origine" => request.SortDescending ? query.OrderByDescending(x => x.Origine) : query.OrderBy(x => x.Origine),
            "tonumber" => request.SortDescending ? query.OrderByDescending(x => x.TONumber) : query.OrderBy(x => x.TONumber),
            _ => request.SortDescending ? query.OrderByDescending(x => x.PART) : query.OrderBy(x => x.PART)
        };

        var totalCount = await query.CountAsync(cancellationToken);

        var entities = await query
            .Skip(request.Skip)
            .Take(request.Take)
            .ToListAsync(cancellationToken);
        
        var items = entities.Select(MapToResponse).ToList();

        return new ComponentiPagedResult
        {
            Items = items,
            TotalCount = totalCount,
            Skip = request.Skip,
            Take = request.Take
        };
    }

    public async Task<List<ComponentiResponse>> GetByCodFAsync(string codF, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca componenti per CodF: {CodF}", codF);

        var entities = await _context.TB_Componenti
            .AsNoTracking()
            .Where(x => x.CodF == codF)
            .OrderBy(x => x.PART)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<List<ComponentiResponse>> GetByOrigineAsync(string origine, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca componenti per Origine: {Origine}", origine);

        var entities = await _context.TB_Componenti
            .AsNoTracking()
            .Where(x => x.Origine == origine)
            .OrderBy(x => x.PART)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<List<ComponentiResponse>> GetByTabellaAsync(string tabella, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca componenti per Tabella: {Tabella}", tabella);

        var entities = await _context.TB_Componenti
            .AsNoTracking()
            .Where(x => x.Tabella == tabella)
            .OrderBy(x => x.PART)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<List<ComponentiResponse>> GetByTONumberAsync(string toNumber, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca componenti per TONumber: {TONumber}", toNumber);

        var entities = await _context.TB_Componenti
            .AsNoTracking()
            .Where(x => x.TONumber == toNumber)
            .OrderBy(x => x.PART)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<List<ComponentiResponse>> SearchByDescriptionAsync(string searchTerm, string language = "IT", CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca componenti per descrizione: {SearchTerm} in lingua: {Language}", searchTerm, language);

        var query = _context.TB_Componenti.AsNoTracking();
        var searchPattern = $"%{searchTerm}%";

        query = language.ToUpper() switch
        {
            "EN" => query.Where(x => x.DescrEN != null && EF.Functions.Like(x.DescrEN, searchPattern)),
            "ES" => query.Where(x => x.DescrES != null && EF.Functions.Like(x.DescrES, searchPattern)),
            "PORT" => query.Where(x => x.DescrPORT != null && EF.Functions.Like(x.DescrPORT, searchPattern)),
            "FR" => query.Where(x => x.DescrFR != null && EF.Functions.Like(x.DescrFR, searchPattern)),
            "TED" => query.Where(x => x.DescrTED != null && EF.Functions.Like(x.DescrTED, searchPattern)),
            "USA" => query.Where(x => x.DescrUSA != null && EF.Functions.Like(x.DescrUSA, searchPattern)),
            _ => query.Where(x => x.DescrIT != null && EF.Functions.Like(x.DescrIT, searchPattern))
        };

        var entities = await query
            .OrderBy(x => x.PART)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<List<ComponentiResponse>> SearchByNoteAsync(string searchTerm, string language = "IT", CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca componenti per note: {SearchTerm} in lingua: {Language}", searchTerm, language);

        var query = _context.TB_Componenti.AsNoTracking();
        var searchPattern = $"%{searchTerm}%";

        query = language.ToUpper() switch
        {
            "EN" => query.Where(x => x.NotaEN != null && EF.Functions.Like(x.NotaEN, searchPattern)),
            "ES" => query.Where(x => x.NotaES != null && EF.Functions.Like(x.NotaES, searchPattern)),
            "PORT" => query.Where(x => x.NotaPORT != null && EF.Functions.Like(x.NotaPORT, searchPattern)),
            "FR" => query.Where(x => x.NotaFR != null && EF.Functions.Like(x.NotaFR, searchPattern)),
            "TED" => query.Where(x => x.NotaTED != null && EF.Functions.Like(x.NotaTED, searchPattern)),
            "USA" => query.Where(x => x.NotaUSA != null && EF.Functions.Like(x.NotaUSA, searchPattern)),
            _ => query.Where(x => x.NotaIT != null && EF.Functions.Like(x.NotaIT, searchPattern))
        };

        var entities = await query
            .OrderBy(x => x.PART)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<ComponentiResponse> CreateAsync(CreateComponentiRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creazione nuovo componente: {@Request}", request);

        // Verifica che non esista già
        var exists = await ExistsAsync(request.PART, cancellationToken);
        if (exists)
        {
            throw new InvalidOperationException($"Componente con PART '{request.PART}' già esistente");
        }

        var entity = new TB_Componenti
        {
            PART = request.PART,
            DescrIT = request.DescrIT,
            DescrES = request.DescrES,
            DescrEN = request.DescrEN,
            DescrPORT = request.DescrPORT,
            DescrFR = request.DescrFR,
            DescrTED = request.DescrTED,
            DescrUSA = request.DescrUSA,
            NotaIT = request.NotaIT,
            NotaES = request.NotaES,
            NotaEN = request.NotaEN,
            NotaPORT = request.NotaPORT,
            NotaFR = request.NotaFR,
            NotaTED = request.NotaTED,
            NotaUSA = request.NotaUSA,
            NUC = request.NUC,
            CodF = request.CodF,
            PARTF = request.PARTF,
            Tabella = request.Tabella,
            Origine = request.Origine,
            TONumber = request.TONumber
        };

        _context.TB_Componenti.Add(entity);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Componente creato con successo: PART={PART}", request.PART);

        return MapToResponse(entity);
    }

    public async Task<ComponentiResponse?> UpdateAsync(string part, UpdateComponentiRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Aggiornamento componente PART: {PART}", part);

        var entity = await _context.TB_Componenti
            .FirstOrDefaultAsync(x => x.PART == part, cancellationToken);

        if (entity == null)
        {
            _logger.LogWarning("Componente non trovato per l'aggiornamento: PART={PART}", part);
            return null;
        }

        entity.DescrIT = request.DescrIT;
        entity.DescrES = request.DescrES;
        entity.DescrEN = request.DescrEN;
        entity.DescrPORT = request.DescrPORT;
        entity.DescrFR = request.DescrFR;
        entity.DescrTED = request.DescrTED;
        entity.DescrUSA = request.DescrUSA;
        entity.NotaIT = request.NotaIT;
        entity.NotaES = request.NotaES;
        entity.NotaEN = request.NotaEN;
        entity.NotaPORT = request.NotaPORT;
        entity.NotaFR = request.NotaFR;
        entity.NotaTED = request.NotaTED;
        entity.NotaUSA = request.NotaUSA;
        entity.NUC = request.NUC;
        entity.CodF = request.CodF;
        entity.PARTF = request.PARTF;
        entity.Tabella = request.Tabella;
        entity.Origine = request.Origine;
        entity.TONumber = request.TONumber;

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Componente aggiornato con successo: PART={PART}", part);

        return MapToResponse(entity);
    }

    public async Task<bool> DeleteAsync(string part, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Eliminazione componente PART: {PART}", part);

        var entity = await _context.TB_Componenti
            .FirstOrDefaultAsync(x => x.PART == part, cancellationToken);

        if (entity == null)
        {
            _logger.LogWarning("Componente non trovato per l'eliminazione: PART={PART}", part);
            return false;
        }

        _context.TB_Componenti.Remove(entity);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Componente eliminato con successo: PART={PART}", part);

        return true;
    }

    public async Task<bool> ExistsAsync(string part, CancellationToken cancellationToken = default)
    {
        return await _context.TB_Componenti
            .AsNoTracking()
            .AnyAsync(x => x.PART == part, cancellationToken);
    }

    public async Task<ComponentiStatsResponse> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generazione statistiche per Componenti");

        var totalComponents = await _context.TB_Componenti.CountAsync(cancellationToken);

        var componentsWithDescriptions = await _context.TB_Componenti
            .Where(x => !string.IsNullOrWhiteSpace(x.DescrIT) ||
                       !string.IsNullOrWhiteSpace(x.DescrEN) ||
                       !string.IsNullOrWhiteSpace(x.DescrES) ||
                       !string.IsNullOrWhiteSpace(x.DescrPORT) ||
                       !string.IsNullOrWhiteSpace(x.DescrFR) ||
                       !string.IsNullOrWhiteSpace(x.DescrTED) ||
                       !string.IsNullOrWhiteSpace(x.DescrUSA))
            .CountAsync(cancellationToken);

        var componentsWithNotes = await _context.TB_Componenti
            .Where(x => !string.IsNullOrWhiteSpace(x.NotaIT) ||
                       !string.IsNullOrWhiteSpace(x.NotaEN) ||
                       !string.IsNullOrWhiteSpace(x.NotaES) ||
                       !string.IsNullOrWhiteSpace(x.NotaPORT) ||
                       !string.IsNullOrWhiteSpace(x.NotaFR) ||
                       !string.IsNullOrWhiteSpace(x.NotaTED) ||
                       !string.IsNullOrWhiteSpace(x.NotaUSA))
            .CountAsync(cancellationToken);

        var componentsWithNUC = await _context.TB_Componenti
            .Where(x => !string.IsNullOrWhiteSpace(x.NUC))
            .CountAsync(cancellationToken);

        var componentsWithCodF = await _context.TB_Componenti
            .Where(x => !string.IsNullOrWhiteSpace(x.CodF))
            .CountAsync(cancellationToken);

        var componentsWithPARTF = await _context.TB_Componenti
            .Where(x => !string.IsNullOrWhiteSpace(x.PARTF))
            .CountAsync(cancellationToken);

        var componentsWithTONumber = await _context.TB_Componenti
            .Where(x => !string.IsNullOrWhiteSpace(x.TONumber))
            .CountAsync(cancellationToken);

        var multiLanguageComponents = await _context.TB_Componenti
            .Where(x => (!string.IsNullOrWhiteSpace(x.DescrIT) && !string.IsNullOrWhiteSpace(x.DescrEN)) ||
                       (!string.IsNullOrWhiteSpace(x.DescrIT) && !string.IsNullOrWhiteSpace(x.DescrES)) ||
                       (!string.IsNullOrWhiteSpace(x.DescrIT) && !string.IsNullOrWhiteSpace(x.DescrPORT)) ||
                       (!string.IsNullOrWhiteSpace(x.DescrIT) && !string.IsNullOrWhiteSpace(x.DescrFR)) ||
                       (!string.IsNullOrWhiteSpace(x.DescrIT) && !string.IsNullOrWhiteSpace(x.DescrTED)) ||
                       (!string.IsNullOrWhiteSpace(x.DescrIT) && !string.IsNullOrWhiteSpace(x.DescrUSA)))
            .CountAsync(cancellationToken);

        var topOrigins = await _context.TB_Componenti
            .Where(x => !string.IsNullOrWhiteSpace(x.Origine))
            .GroupBy(x => x.Origine!)
            .OrderByDescending(g => g.Count())
            .Take(10)
            .Select(g => g.Key)
            .ToListAsync(cancellationToken);

        var topTables = await _context.TB_Componenti
            .Where(x => !string.IsNullOrWhiteSpace(x.Tabella))
            .GroupBy(x => x.Tabella!)
            .OrderByDescending(g => g.Count())
            .Take(10)
            .Select(g => g.Key)
            .ToListAsync(cancellationToken);

        // Statistiche per lingua
        var languageStats = new List<LanguageStats>
        {
            new() { Language = "IT", DescriptionCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.DescrIT)).CountAsync(cancellationToken), NoteCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.NotaIT)).CountAsync(cancellationToken) },
            new() { Language = "EN", DescriptionCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.DescrEN)).CountAsync(cancellationToken), NoteCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.NotaEN)).CountAsync(cancellationToken) },
            new() { Language = "ES", DescriptionCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.DescrES)).CountAsync(cancellationToken), NoteCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.NotaES)).CountAsync(cancellationToken) },
            new() { Language = "PORT", DescriptionCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.DescrPORT)).CountAsync(cancellationToken), NoteCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.NotaPORT)).CountAsync(cancellationToken) },
            new() { Language = "FR", DescriptionCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.DescrFR)).CountAsync(cancellationToken), NoteCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.NotaFR)).CountAsync(cancellationToken) },
            new() { Language = "TED", DescriptionCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.DescrTED)).CountAsync(cancellationToken), NoteCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.NotaTED)).CountAsync(cancellationToken) },
            new() { Language = "USA", DescriptionCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.DescrUSA)).CountAsync(cancellationToken), NoteCount = await _context.TB_Componenti.Where(x => !string.IsNullOrWhiteSpace(x.NotaUSA)).CountAsync(cancellationToken) }
        };

        return new ComponentiStatsResponse
        {
            TotalComponents = totalComponents,
            ComponentsWithDescriptions = componentsWithDescriptions,
            ComponentsWithNotes = componentsWithNotes,
            ComponentsWithNUC = componentsWithNUC,
            ComponentsWithCodF = componentsWithCodF,
            ComponentsWithPARTF = componentsWithPARTF,
            ComponentsWithTONumber = componentsWithTONumber,
            MultiLanguageComponents = multiLanguageComponents,
            TopOrigins = topOrigins,
            TopTables = topTables,
            LanguageDistribution = languageStats
        };
    }

    public async Task<string?> GetLocalizedDescriptionAsync(string part, string language = "IT", CancellationToken cancellationToken = default)
    {
        var entity = await _context.TB_Componenti
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.PART == part, cancellationToken);

        if (entity == null) return null;

        return language.ToUpper() switch
        {
            "EN" => entity.DescrEN ?? entity.DescrIT,
            "ES" => entity.DescrES ?? entity.DescrIT,
            "PORT" => entity.DescrPORT ?? entity.DescrIT,
            "FR" => entity.DescrFR ?? entity.DescrIT,
            "TED" => entity.DescrTED ?? entity.DescrIT,
            "USA" => entity.DescrUSA ?? entity.DescrIT,
            _ => entity.DescrIT
        };
    }

    public async Task<string?> GetLocalizedNoteAsync(string part, string language = "IT", CancellationToken cancellationToken = default)
    {
        var entity = await _context.TB_Componenti
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.PART == part, cancellationToken);

        if (entity == null) return null;

        return language.ToUpper() switch
        {
            "EN" => entity.NotaEN ?? entity.NotaIT,
            "ES" => entity.NotaES ?? entity.NotaIT,
            "PORT" => entity.NotaPORT ?? entity.NotaIT,
            "FR" => entity.NotaFR ?? entity.NotaIT,
            "TED" => entity.NotaTED ?? entity.NotaIT,
            "USA" => entity.NotaUSA ?? entity.NotaIT,
            _ => entity.NotaIT
        };
    }

    public async Task<List<ComponentiResponse>> GetDuplicatesAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca componenti duplicati");

        // Questo metodo cerca componenti con stesso PART (che non dovrebbero esistere per chiave primaria)
        // Ma può essere utile per verificare inconsistenze nei dati
        var entities = await _context.TB_Componenti
            .AsNoTracking()
            .GroupBy(x => x.PART)
            .Where(g => g.Count() > 1)
            .SelectMany(g => g)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<List<ComponentiResponse>> GetWithoutDescriptionAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Ricerca componenti senza descrizione");

        var entities = await _context.TB_Componenti
            .AsNoTracking()
            .Where(x => string.IsNullOrWhiteSpace(x.DescrIT) &&
                       string.IsNullOrWhiteSpace(x.DescrEN) &&
                       string.IsNullOrWhiteSpace(x.DescrES) &&
                       string.IsNullOrWhiteSpace(x.DescrPORT) &&
                       string.IsNullOrWhiteSpace(x.DescrFR) &&
                       string.IsNullOrWhiteSpace(x.DescrTED) &&
                       string.IsNullOrWhiteSpace(x.DescrUSA))
            .OrderBy(x => x.PART)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    private static readonly Func<TB_Componenti, ComponentiResponse> MapToResponse = entity => new()
    {
        PART = entity.PART,
        DescrIT = entity.DescrIT,
        DescrES = entity.DescrES,
        DescrEN = entity.DescrEN,
        DescrPORT = entity.DescrPORT,
        DescrFR = entity.DescrFR,
        DescrTED = entity.DescrTED,
        DescrUSA = entity.DescrUSA,
        NotaIT = entity.NotaIT,
        NotaES = entity.NotaES,
        NotaEN = entity.NotaEN,
        NotaPORT = entity.NotaPORT,
        NotaFR = entity.NotaFR,
        NotaTED = entity.NotaTED,
        NotaUSA = entity.NotaUSA,
        NUC = entity.NUC,
        CodF = entity.CodF,
        PARTF = entity.PARTF,
        Tabella = entity.Tabella,
        Origine = entity.Origine,
        TONumber = entity.TONumber
    };
}
