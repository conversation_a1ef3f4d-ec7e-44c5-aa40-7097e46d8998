using Ardec.Services.Web.DTOs.Componenti;

namespace Ardec.Services.Web.Services.Componenti;

/// <summary>
/// Interface per il servizio di gestione TB_Componenti
/// </summary>
public interface IComponentiService
{
    /// <summary>
    /// Ottiene tutti i componenti con paginazione
    /// </summary>
    Task<ComponentiPagedResult> GetAllAsync(int skip = 0, int take = 20, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene un componente per PART
    /// </summary>
    Task<ComponentiResponse?> GetByPartAsync(string part, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cerca componenti con filtri avanzati
    /// </summary>
    Task<ComponentiPagedResult> SearchAsync(ComponentiSearchRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene componenti per codice fornitore
    /// </summary>
    Task<List<ComponentiResponse>> GetByCodFAsync(string codF, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene componenti per origine
    /// </summary>
    Task<List<ComponentiResponse>> GetByOrigineAsync(string origine, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene componenti per tabella
    /// </summary>
    Task<List<ComponentiResponse>> GetByTabellaAsync(string tabella, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene componenti per TONumber
    /// </summary>
    Task<List<ComponentiResponse>> GetByTONumberAsync(string toNumber, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ricerca componenti per descrizione in una lingua specifica
    /// </summary>
    Task<List<ComponentiResponse>> SearchByDescriptionAsync(string searchTerm, string language = "IT", CancellationToken cancellationToken = default);

    /// <summary>
    /// Ricerca componenti per note in una lingua specifica
    /// </summary>
    Task<List<ComponentiResponse>> SearchByNoteAsync(string searchTerm, string language = "IT", CancellationToken cancellationToken = default);

    /// <summary>
    /// Crea un nuovo componente
    /// </summary>
    Task<ComponentiResponse> CreateAsync(CreateComponentiRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Aggiorna un componente esistente
    /// </summary>
    Task<ComponentiResponse?> UpdateAsync(string part, UpdateComponentiRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Elimina un componente
    /// </summary>
    Task<bool> DeleteAsync(string part, CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica se esiste un componente
    /// </summary>
    Task<bool> ExistsAsync(string part, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene statistiche sui componenti
    /// </summary>
    Task<ComponentiStatsResponse> GetStatsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene la descrizione localizzata per un componente
    /// </summary>
    Task<string?> GetLocalizedDescriptionAsync(string part, string language = "IT", CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene la nota localizzata per un componente
    /// </summary>
    Task<string?> GetLocalizedNoteAsync(string part, string language = "IT", CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene componenti duplicati (stesso PART ma differente nei dettagli)
    /// </summary>
    Task<List<ComponentiResponse>> GetDuplicatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Ottiene componenti senza descrizione
    /// </summary>
    Task<List<ComponentiResponse>> GetWithoutDescriptionAsync(CancellationToken cancellationToken = default);
}
