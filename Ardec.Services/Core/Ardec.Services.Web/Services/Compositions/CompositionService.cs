using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace Ardec.Services.Web.Services.Compositions;

/// <summary>
/// Service for managing TB_Composizione - Catalog-Table composition operations
/// Implements MIL-STD-1388 composition management following Framework patterns
/// </summary>
public class CompositionService : ICompositionService
{
    private readonly ArdecDbContext _context;
    private readonly ILogger<CompositionService> _logger;

    public CompositionService(ArdecDbContext context, ILogger<CompositionService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<TB_Composizione>> GetComposizioneByTerAsync(string ter)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));

        try
        {
            return await _context.TB_Composizione
                .Where(c => c.TER == ter)
                .OrderBy(c => c.nTavola)
                .Include(c => c.TB_Cataloghi)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving composition for TER: {TER}", ter);
            throw;
        }
    }

    public async Task<TB_Composizione?> GetCompositionAsync(string ter, string tavola)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));

        try
        {
            return await _context.TB_Composizione
                .Include(c => c.TB_Cataloghi)
                .FirstOrDefaultAsync(c => c.TER == ter && c.Tavola == tavola);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving composition for TER: {TER}, Tavola: {Tavola}", ter, tavola);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Composizione>> GetAllCompositionsAsync()
    {
        try
        {
            return await _context.TB_Composizione
                .Include(c => c.TB_Cataloghi)
                .OrderBy(c => c.TER)
                .ThenBy(c => c.nTavola)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all compositions");
            throw;
        }
    }

    public async Task<TB_Composizione> CreateCompositionAsync(TB_Composizione composition)
    {
        if (composition == null)
            throw new ArgumentNullException(nameof(composition));

        if (string.IsNullOrWhiteSpace(composition.TER))
            throw new ArgumentException("TER is required", nameof(composition));

        if (string.IsNullOrWhiteSpace(composition.Tavola))
            throw new ArgumentException("Tavola is required", nameof(composition));

        try
        {
            if (await ExistsAsync(composition.TER, composition.Tavola))
                throw new InvalidOperationException($"Composition with TER '{composition.TER}' and Tavola '{composition.Tavola}' already exists");

            // Auto-assign table order if not specified
            if (!composition.nTavola.HasValue)
            {
                var maxOrder = await _context.TB_Composizione
                    .Where(c => c.TER == composition.TER)
                    .MaxAsync(c => (double?)c.nTavola) ?? 0;
                composition.nTavola = maxOrder + 1;
            }

            _context.TB_Composizione.Add(composition);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created composition: TER {TER}, Tavola {Tavola}", composition.TER, composition.Tavola);
            return composition;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating composition for TER: {TER}, Tavola: {Tavola}", composition.TER, composition.Tavola);
            throw;
        }
    }

    public async Task<TB_Composizione> UpdateCompositionAsync(TB_Composizione composition)
    {
        if (composition == null)
            throw new ArgumentNullException(nameof(composition));

        if (string.IsNullOrWhiteSpace(composition.TER))
            throw new ArgumentException("TER is required", nameof(composition));

        if (string.IsNullOrWhiteSpace(composition.Tavola))
            throw new ArgumentException("Tavola is required", nameof(composition));

        try
        {
            var existing = await GetCompositionAsync(composition.TER, composition.Tavola);
            if (existing == null)
                throw new InvalidOperationException($"Composition with TER '{composition.TER}' and Tavola '{composition.Tavola}' not found");

            // Update properties following Framework fields
            existing.nTavola = composition.nTavola;
            existing.CEDNPagina = composition.CEDNPagina;
            existing.CEDIGNPagina = composition.CEDIGNPagina;

            // Update MAGIRUS groups
            existing.GruppoITA = composition.GruppoITA;
            existing.GruppoENG = composition.GruppoENG;
            existing.GruppoFRA = composition.GruppoFRA;
            existing.GruppoPOR = composition.GruppoPOR;
            existing.GruppoESP = composition.GruppoESP;
            existing.GruppoTED = composition.GruppoTED;
            existing.GruppoUSA = composition.GruppoUSA;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated composition: TER {TER}, Tavola {Tavola}", composition.TER, composition.Tavola);
            return existing;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating composition for TER: {TER}, Tavola: {Tavola}", composition.TER, composition.Tavola);
            throw;
        }
    }

    public async Task<bool> DeleteCompositionAsync(string ter, string tavola)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));

        try
        {
            var composition = await GetCompositionAsync(ter, tavola);
            if (composition == null)
                return false;

            _context.TB_Composizione.Remove(composition);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted composition: TER {TER}, Tavola {Tavola}", ter, tavola);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting composition for TER: {TER}, Tavola: {Tavola}", ter, tavola);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(string ter, string tavola)
    {
        if (string.IsNullOrWhiteSpace(ter) || string.IsNullOrWhiteSpace(tavola))
            return false;

        try
        {
            return await _context.TB_Composizione
                .AnyAsync(c => c.TER == ter && c.Tavola == tavola);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if composition exists: TER {TER}, Tavola {Tavola}", ter, tavola);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Composizione>> GetOrderedTablesAsync(string ter)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));

        try
        {
            return await _context.TB_Composizione
                .Where(c => c.TER == ter)
                .OrderBy(c => c.nTavola)
                .ThenBy(c => c.Tavola)
                .Include(c => c.TB_Cataloghi)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving ordered tables for TER: {TER}", ter);
            throw;
        }
    }

    public async Task<IEnumerable<string>> GetGruppiByLanguageAsync(string ter, string language)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));

        if (string.IsNullOrWhiteSpace(language))
            throw new ArgumentException("Language cannot be null or empty", nameof(language));

        try
        {
            var compositions = await _context.TB_Composizione
                .Where(c => c.TER == ter)
                .ToListAsync();

            var groups = language.ToUpper() switch
            {
                "ITA" or "IT" => compositions.Select(c => c.GruppoITA),
                "ENG" or "EN" => compositions.Select(c => c.GruppoENG),
                "FRA" or "FR" => compositions.Select(c => c.GruppoFRA),
                "POR" or "PT" => compositions.Select(c => c.GruppoPOR),
                "ESP" or "ES" => compositions.Select(c => c.GruppoESP),
                "TED" or "DE" => compositions.Select(c => c.GruppoTED),
                "USA" or "US" => compositions.Select(c => c.GruppoUSA),
                _ => throw new ArgumentException($"Unsupported language: {language}")
            };

            return groups.Where(g => !string.IsNullOrEmpty(g))
                        .Distinct()
                        .OrderBy(g => g)
                        .ToList()!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving groups for TER: {TER}, Language: {Language}", ter, language);
            throw;
        }
    }

    public async Task<TB_Composizione> UpdateTableOrderAsync(string ter, string tavola, double newOrder)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));

        try
        {
            var composition = await GetCompositionAsync(ter, tavola);
            if (composition == null)
                throw new InvalidOperationException($"Composition with TER '{ter}' and Tavola '{tavola}' not found");

            composition.nTavola = newOrder;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated table order: TER {TER}, Tavola {Tavola}, New Order: {Order}", ter, tavola, newOrder);
            return composition;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating table order for TER: {TER}, Tavola: {Tavola}", ter, tavola);
            throw;
        }
    }

    public async Task<object> GetCompositionStatisticsAsync(string ter)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));

        try
        {
            var compositions = await GetComposizioneByTerAsync(ter);
            var compositionList = compositions.ToList();

            var stats = new
            {
                ter = ter,
                totalTables = compositionList.Count,
                tablesWithGroups = compositionList.Count(c => 
                    !string.IsNullOrEmpty(c.GruppoITA) || 
                    !string.IsNullOrEmpty(c.GruppoENG) ||
                    !string.IsNullOrEmpty(c.GruppoFRA)),
                tablesWithPageNumbers = compositionList.Count(c => c.CEDNPagina.HasValue),
                languageGroups = new
                {
                    italian = compositionList.Where(c => !string.IsNullOrEmpty(c.GruppoITA)).Select(c => c.GruppoITA).Distinct().Count(),
                    english = compositionList.Where(c => !string.IsNullOrEmpty(c.GruppoENG)).Select(c => c.GruppoENG).Distinct().Count(),
                    french = compositionList.Where(c => !string.IsNullOrEmpty(c.GruppoFRA)).Select(c => c.GruppoFRA).Distinct().Count(),
                    spanish = compositionList.Where(c => !string.IsNullOrEmpty(c.GruppoESP)).Select(c => c.GruppoESP).Distinct().Count(),
                    portuguese = compositionList.Where(c => !string.IsNullOrEmpty(c.GruppoPOR)).Select(c => c.GruppoPOR).Distinct().Count(),
                    german = compositionList.Where(c => !string.IsNullOrEmpty(c.GruppoTED)).Select(c => c.GruppoTED).Distinct().Count(),
                    usa = compositionList.Where(c => !string.IsNullOrEmpty(c.GruppoUSA)).Select(c => c.GruppoUSA).Distinct().Count()
                },
                orderRange = compositionList.Any() ? new
                {
                    min = compositionList.Min(c => c.nTavola ?? 0),
                    max = compositionList.Max(c => c.nTavola ?? 0)
                } : null
            };

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating composition statistics for TER: {TER}", ter);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Composizione>> SearchCompositionsAsync(string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return await GetAllCompositionsAsync();

        try
        {
            var term = searchTerm.ToLower().Trim();

            return await _context.TB_Composizione
                .Include(c => c.TB_Cataloghi)
                .Where(c =>
                    c.TER.ToLower().Contains(term) ||
                    c.Tavola.ToLower().Contains(term) ||
                    (c.CEDIGNPagina != null && c.CEDIGNPagina.ToLower().Contains(term)) ||
                    (c.GruppoITA != null && c.GruppoITA.ToLower().Contains(term)) ||
                    (c.GruppoENG != null && c.GruppoENG.ToLower().Contains(term)))
                .OrderBy(c => c.TER)
                .ThenBy(c => c.nTavola)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching compositions with term: {SearchTerm}", searchTerm);
            throw;
        }
    }
}
