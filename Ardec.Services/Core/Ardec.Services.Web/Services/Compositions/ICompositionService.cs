using Ardec.Services.Web.Data.Models;

namespace Ardec.Services.Web.Services.Compositions;

/// <summary>
/// Service interface for managing TB_Composizione - Catalog-Table composition
/// </summary>
public interface ICompositionService
{
    /// <summary>
    /// Get all composition entries for a catalog (all tables in a catalog)
    /// </summary>
    Task<IEnumerable<TB_Composizione>> GetComposizioneByTerAsync(string ter);

    /// <summary>
    /// Get specific composition entry by TER + Tavola
    /// </summary>
    Task<TB_Composizione?> GetCompositionAsync(string ter, string tavola);

    /// <summary>
    /// Get all compositions (admin view)
    /// </summary>
    Task<IEnumerable<TB_Composizione>> GetAllCompositionsAsync();

    /// <summary>
    /// Add table to catalog composition
    /// </summary>
    Task<TB_Composizione> CreateCompositionAsync(TB_Composizione composition);

    /// <summary>
    /// Update composition entry (order, groups, etc.)
    /// </summary>
    Task<TB_Composizione> UpdateCompositionAsync(TB_Composizione composition);

    /// <summary>
    /// Remove table from catalog composition
    /// </summary>
    Task<bool> DeleteCompositionAsync(string ter, string tavola);

    /// <summary>
    /// Check if composition entry exists
    /// </summary>
    Task<bool> ExistsAsync(string ter, string tavola);

    /// <summary>
    /// Get tables in a catalog ordered by nTavola
    /// </summary>
    Task<IEnumerable<TB_Composizione>> GetOrderedTablesAsync(string ter);

    /// <summary>
    /// Get groups for specific language (MAGIRUS functionality)
    /// </summary>
    Task<IEnumerable<string>> GetGruppiByLanguageAsync(string ter, string language);

    /// <summary>
    /// Update table order in composition
    /// </summary>
    Task<TB_Composizione> UpdateTableOrderAsync(string ter, string tavola, double newOrder);

    /// <summary>
    /// Get composition statistics for a catalog
    /// </summary>
    Task<object> GetCompositionStatisticsAsync(string ter);

    /// <summary>
    /// Search compositions by catalog or table name
    /// </summary>
    Task<IEnumerable<TB_Composizione>> SearchCompositionsAsync(string searchTerm);
}
