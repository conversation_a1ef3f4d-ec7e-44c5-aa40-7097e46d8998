using Ardec.Services.Web.DTOs.Reports;

namespace Ardec.Services.Web.Services
{
    /// <summary>
    /// Servizio per la gestione dei report e degli export
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// Esporta un catalogo completo in formato Excel
        /// </summary>
        /// <param name="request">Parametri per l'export</param>
        /// <returns>File Excel del catalogo</returns>
        Task<ExportResult> ExportCatalogToExcelAsync(ExportCatalogRequest request);

        /// <summary>
        /// Esporta Data Modules in formato XML S1000D
        /// </summary>
        /// <param name="request">Parametri per l'export</param>
        /// <returns>File XML o ZIP con i Data Modules</returns>
        Task<ExportResult> ExportDataModulesToXmlAsync(ExportDMRequest request);

        /// <summary>
        /// Esporta dati in formato CSV
        /// </summary>
        /// <param name="request">Parametri per l'export</param>
        /// <returns>File CSV</returns>
        Task<ExportResult> ExportToCsvAsync(ExportCsvRequest request);

        /// <summary>
        /// Esporta archivio completo tramite stored procedure
        /// </summary>
        /// <param name="format">Formato output (excel/csv)</param>
        /// <returns>File archivio</returns>
        Task<ExportResult> ExportCompleteArchiveAsync(string format);

        /// <summary>
        /// Ottiene la lista dei cataloghi disponibili
        /// </summary>
        /// <returns>Lista dei cataloghi</returns>
        Task<List<CatalogInfo>> GetAvailableCatalogsAsync();

        /// <summary>
        /// Ottiene la lista delle tavole per un catalogo
        /// </summary>
        /// <param name="ter">TER del catalogo</param>
        /// <returns>Lista delle tavole</returns>
        Task<List<TableInfo>> GetCatalogTablesAsync(string ter);
    }
}
