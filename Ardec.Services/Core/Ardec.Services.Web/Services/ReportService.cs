using Ardec.Services.Web.DTOs.Reports;
using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Microsoft.EntityFrameworkCore;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Text;
using System.Xml.Linq;
using System.IO.Compression;

namespace Ardec.Services.Web.Services
{
    /// <summary>
    /// Implementazione del servizio per i report e gli export
    /// </summary>
    public class ReportService : IReportService
    {
        private readonly ArdecDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ReportService> _logger;

        public ReportService(
            ArdecDbContext context, 
            IConfiguration configuration,
            ILogger<ReportService> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<ExportResult> ExportCatalogToExcelAsync(ExportCatalogRequest request)
        {
            _logger.LogInformation("Inizio export catalogo Excel per TER: {TER}", request.TER);

            // Verificare che il catalogo esista
            var catalog = await _context.TB_Cataloghi
                .FirstOrDefaultAsync(c => c.TER == request.TER);

            if (catalog == null)
            {
                throw new ArgumentException($"Catalogo con TER '{request.TER}' non trovato");
            }

            // Creare il dataset con i dati del catalogo
            var dataset = await BuildCatalogDataSetAsync(catalog, request.IsS1000D);

            // Generare il file Excel
            var fileContent = await CreateExcelFromDataSetAsync(dataset, request.IsS1000D);

            // Gestire immagini mancanti se richiesto
            ExportResult? missingImagesFile = null;
            if (request.CheckMissingImages)
            {
                missingImagesFile = await CheckMissingImagesAsync(request.TER);
            }

            var fileName = $"CATALOGO_{request.TER}_{DateTime.Now:yyyyMMdd_HHmm}.xlsx";

            return new ExportResult
            {
                FileContent = fileContent,
                FileName = fileName,
                ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                AdditionalFile = missingImagesFile
            };
        }

        public async Task<ExportResult> ExportDataModulesToXmlAsync(ExportDMRequest request)
        {
            _logger.LogInformation("Inizio export DM XML per {Count} tavole", request.Tavole.Count);

            // Validare la lingua
            if (!SupportedLanguages.All.Contains(request.Language))
            {
                throw new ArgumentException($"Lingua '{request.Language}' non supportata");
            }

            // Ottenere i dati del catalogo
            var catalog = await _context.TB_Cataloghi
                .FirstOrDefaultAsync(c => c.TER == request.TER);

            if (catalog == null)
            {
                throw new ArgumentException($"Catalogo con TER '{request.TER}' non trovato");
            }

            // Ottenere le tavole richieste
            var tables = await _context.TB_Tavole
                .Where(t => request.Tavole.Contains(t.Tavola) && t.TER == request.TER)
                .ToListAsync();

            if (tables.Count != request.Tavole.Count)
            {
                var found = tables.Select(t => t.Tavola).ToList();
                var missing = request.Tavole.Except(found).ToList();
                throw new ArgumentException($"Tavole non trovate: {string.Join(", ", missing)}");
            }

            if (request.Tavole.Count == 1)
            {
                // Export singolo
                var xmlContent = await GenerateDataModuleXmlAsync(catalog, tables.First(), request);
                var fileName = $"{tables.First().DMC ?? tables.First().Tavola}.xml";

                return new ExportResult
                {
                    FileContent = Encoding.UTF8.GetBytes(xmlContent),
                    FileName = fileName,
                    ContentType = "application/xml"
                };
            }
            else
            {
                // Export multiplo - creare ZIP
                var zipContent = await CreateDataModulesZipAsync(catalog, tables.Cast<dynamic>().ToList(), request);
                var fileName = $"DataModules_{request.TER}_{DateTime.Now:yyyyMMdd_HHmm}.zip";

                return new ExportResult
                {
                    FileContent = zipContent,
                    FileName = fileName,
                    ContentType = "application/zip"
                };
            }
        }

        public async Task<ExportResult> ExportToCsvAsync(ExportCsvRequest request)
        {
            _logger.LogInformation("Inizio export CSV tipo: {Type} per TER: {TER}", request.Type, request.TER);

            // Validare il tipo di export
            if (!CsvExportTypes.All.Contains(request.Type))
            {
                throw new ArgumentException($"Tipo export '{request.Type}' non supportato");
            }

            var csvContent = request.Type switch
            {
                CsvExportTypes.Tavole => await ExportTavoleToCsvAsync(request.TER, request.IsS1000D),
                CsvExportTypes.SingolaTavola => await ExportSingolaTavolaToCsvAsync(request.TER, request.Tavola!, request.IsS1000D),
                _ => throw new ArgumentException($"Tipo export '{request.Type}' non implementato")
            };

            var fileName = request.Type switch
            {
                CsvExportTypes.Tavole => $"Tavole_{request.TER}_{DateTime.Now:yyyyMMdd_HHmm}.csv",
                CsvExportTypes.SingolaTavola => $"Tavola_{request.Tavola}_{DateTime.Now:yyyyMMdd_HHmm}.csv",
                _ => $"Export_{DateTime.Now:yyyyMMdd_HHmm}.csv"
            };

            return new ExportResult
            {
                FileContent = Encoding.UTF8.GetBytes(csvContent),
                FileName = fileName,
                ContentType = "text/csv"
            };
        }

        public async Task<ExportResult> ExportCompleteArchiveAsync(string format)
        {
            _logger.LogInformation("Inizio export archivio completo formato: {Format}", format);

            if (format.ToLower() != "excel" && format.ToLower() != "csv")
            {
                throw new ArgumentException("Formato supportati: 'excel' o 'csv'");
            }

            // Utilizzare la stored procedure per ottenere l'archivio completo
            var dataTable = await ExecuteStoredProcedureAsync("PRC_COMPONENTI_IN_TER_PIVOT");

            byte[] fileContent;
            string fileName;
            string contentType;

            if (format.ToLower() == "excel")
            {
                fileContent = await CreateExcelFromDataTableAsync(dataTable);
                fileName = $"Archivio_Completo_{DateTime.Now:yyyyMMdd_HHmm}.xlsx";
                contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            }
            else
            {
                var csvContent = ConvertDataTableToCsv(dataTable);
                fileContent = Encoding.UTF8.GetBytes(csvContent);
                fileName = $"Archivio_Completo_{DateTime.Now:yyyyMMdd_HHmm}.csv";
                contentType = "text/csv";
            }

            return new ExportResult
            {
                FileContent = fileContent,
                FileName = fileName,
                ContentType = contentType
            };
        }

        public async Task<List<CatalogInfo>> GetAvailableCatalogsAsync()
        {
            return await _context.TB_Cataloghi
                .Select(c => new CatalogInfo
                {
                    TER = c.TER,
                    Title = c.Titolo ?? "",
                    ShortTitle = c.TitoloBreve ?? "",
                    IsS1000D = c.S1000D ?? false,
                    TablesCount = _context.TB_Tavole.Count(t => t.TER == c.TER),
                    LastModified = null
                })
                .OrderBy(c => c.TER)
                .ToListAsync();
        }

        public async Task<List<TableInfo>> GetCatalogTablesAsync(string ter)
        {
            var catalog = await _context.TB_Cataloghi
                .FirstOrDefaultAsync(c => c.TER == ter);

            if (catalog == null)
            {
                throw new ArgumentException($"Catalogo con TER '{ter}' non trovato");
            }

            return await _context.TB_Tavole
                .Where(t => t.TER == ter)
                .Select(t => new TableInfo
                {
                    Tavola = t.Tavola,
                    CodiceTecnico = t.CodiceTecnico ?? "",
                    Descrizione = GetTavolaDescrizione(t, catalog.CEDLanguage),
                    DMC = t.DMC,
                    TechName = t.TECHNAME,
                    PartsCount = _context.TB_Parti.Count(p => p.Tavola == t.Tavola)
                })
                .OrderBy(t => t.Tavola)
                .ToListAsync();
        }

        // Metodi privati di supporto...
        
        private async Task<DataSet> BuildCatalogDataSetAsync(TB_Cataloghi catalog, bool isS1000D)
        {
            var dataset = new DataSet("CatalogoCompleto");

            // 1. Ottenere tutti i dati del catalogo
            var components = await _context.TB_Componenti
                .Where(c => _context.TB_Parti.Any(p => p.PART == c.PART && 
                    _context.TB_Tavole.Any(t => t.Tavola == p.Tavola && t.TER == catalog.TER)))
                .ToListAsync();

            var parts = await _context.TB_Parti
                .Where(p => _context.TB_Tavole.Any(t => t.Tavola == p.Tavola && t.TER == catalog.TER))
                .OrderBy(p => p.Tavola).ThenBy(p => int.Parse(p.ITEM))
                .ToListAsync();

            var tables = await _context.TB_DettagliTavole
                .Where(t => _context.TB_Composizione.Any(c => c.Tavola == t.Tavola && c.TER == catalog.TER))
                .ToListAsync();

            var tableDetails = await _context.TB_Tavole
                .Where(t => t.TER == catalog.TER)
                .ToListAsync();

            var compositions = await _context.TB_Composizione
                .Where(c => c.TER == catalog.TER)
                .ToListAsync();

            var rvts = await _context.TB_RVT
                .Where(r => r.TER == catalog.TER)
                .ToListAsync();

            var catalogRvts = await _context.TB_CataloghiRVT
                .Where(cr => cr.TER == catalog.TER)
                .ToListAsync();

            // 2. Creare le tabelle del DataSet
            var partsTable = CreatePartsDataTable(parts, components, catalog, isS1000D);
            var tablesTable = CreateTablesDataTable(tables, tableDetails, catalog, isS1000D);
            var rvtTable = CreateRvtDataTable(rvts, catalogRvts, catalog);

            dataset.Tables.Add(partsTable);
            dataset.Tables.Add(tablesTable);
            dataset.Tables.Add(rvtTable);

            return dataset;
        }

        private async Task<byte[]> CreateExcelFromDataSetAsync(DataSet dataset, bool isS1000D)
        {
            using var stream = new MemoryStream();
            using var workbook = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            
            var workbookPart = workbook.AddWorkbookPart();
            workbook.WorkbookPart.Workbook = new Workbook();
            workbook.WorkbookPart.Workbook.Sheets = new Sheets();

            uint sheetId = 1;
            var sheets = workbook.WorkbookPart.Workbook.GetFirstChild<Sheets>();

            foreach (DataTable table in dataset.Tables)
            {
                // Filtrare le tabelle S1000D vs Standard
                if (ShouldIncludeTable(table.TableName, isS1000D))
                {
                    var sheetPart = workbook.WorkbookPart.AddNewPart<WorksheetPart>();
                    var sheetData = new SheetData();
                    sheetPart.Worksheet = new Worksheet(sheetData);

                    var relationshipId = workbook.WorkbookPart.GetIdOfPart(sheetPart);
                    var sheet = new Sheet() { Id = relationshipId, SheetId = sheetId++, Name = CleanSheetName(table.TableName) };
                    sheets!.Append(sheet);

                    // Header
                    var headerRow = new Row();
                    foreach (DataColumn column in table.Columns)
                    {
                        var cell = new Cell
                        {
                            DataType = CellValues.String,
                            CellValue = new CellValue(column.ColumnName)
                        };
                        headerRow.AppendChild(cell);
                    }
                    sheetData.AppendChild(headerRow);

                    // Dati
                    foreach (DataRow dataRow in table.Rows)
                    {
                        var newRow = new Row();
                        foreach (var field in dataRow.ItemArray)
                        {
                            var cell = new Cell
                            {
                                DataType = CellValues.String,
                                CellValue = new CellValue(field?.ToString() ?? "")
                            };
                            newRow.AppendChild(cell);
                        }
                        sheetData.AppendChild(newRow);
                    }
                }
            }

            workbook.Save();
            return stream.ToArray();
        }

        private async Task<ExportResult?> CheckMissingImagesAsync(string ter)
        {
            var imagesBasePath = _configuration["Storage:ImagesBasePath"] ?? "";
            var missingImages = new List<string>();

            // Ottenere tutte le immagini del catalogo
            var tableImages = await _context.TB_DettagliTavole
                .Where(t => _context.TB_Composizione.Any(c => c.Tavola == t.Tavola && c.TER == ter))
                .Select(t => new { t.Tavola, t.Logo, t.Figura })
                .ToListAsync();

            foreach (var img in tableImages)
            {
                // Controllo Logo
                if (!string.IsNullOrEmpty(img.Logo))
                {
                    var logoPath = Path.Combine(imagesBasePath, img.Logo.Replace(".cgm", ".png").Replace(".PCX", ".png"));
                    if (!File.Exists(logoPath))
                    {
                        missingImages.Add($"Tavola {img.Tavola} - Logo: {img.Logo}");
                    }
                }

                // Controllo Figura
                if (!string.IsNullOrEmpty(img.Figura))
                {
                    var figuraPath = Path.Combine(imagesBasePath, img.Figura.Replace(".cgm", ".png").Replace(".PCX", ".png"));
                    if (!File.Exists(figuraPath))
                    {
                        missingImages.Add($"Tavola {img.Tavola} - Figura: {img.Figura}");
                    }
                }
            }

            if (missingImages.Count == 0) return null;

            var content = string.Join(Environment.NewLine, missingImages);
            var fileName = $"Immagini_Mancanti_{ter}_{DateTime.Now:yyyyMMdd_HHmm}.txt";

            return new ExportResult
            {
                FileContent = Encoding.UTF8.GetBytes(content),
                FileName = fileName,
                ContentType = "text/plain"
            };
        }

        private async Task<string> GenerateDataModuleXmlAsync(dynamic catalog, dynamic table, ExportDMRequest request)
        {
            // Implementare la generazione XML S1000D
            // Basandosi sulla logica di DMExporter del Framework
            // TODO: Implementare la generazione XML completa
            return "<?xml version=\"1.0\" encoding=\"UTF-8\"?><dmodule></dmodule>";
        }

        private async Task<byte[]> CreateDataModulesZipAsync(dynamic catalog, List<dynamic> tables, ExportDMRequest request)
        {
            using var zipStream = new MemoryStream();
            using var archive = new ZipArchive(zipStream, ZipArchiveMode.Create, true);

            foreach (var table in tables)
            {
                var xmlContent = await GenerateDataModuleXmlAsync(catalog, table, request);
                var fileName = $"{table.DMC ?? table.Tavola}.xml";
                
                var entry = archive.CreateEntry(fileName);
                using var entryStream = entry.Open();
                var xmlBytes = Encoding.UTF8.GetBytes(xmlContent);
                await entryStream.WriteAsync(xmlBytes);
            }

            return zipStream.ToArray();
        }

        private async Task<string> ExportTavoleToCsvAsync(string ter, bool isS1000D)
        {
            var catalog = await _context.TB_Cataloghi.FirstOrDefaultAsync(c => c.TER == ter);
            if (catalog == null) throw new ArgumentException($"Catalogo {ter} non trovato");

            var tables = await _context.TB_DettagliTavole
                .Where(t => _context.TB_Composizione.Any(c => c.Tavola == t.Tavola && c.TER == ter))
                .OrderBy(t => t.Tavola)
                .ToListAsync();

            var tableDetails = await _context.TB_Tavole
                .Where(t => t.TER == ter)
                .ToDictionaryAsync(t => t.Tavola, t => t);

            var csv = new StringBuilder();

            // Header
            if (isS1000D)
            {
                csv.AppendLine("CodiceTecnico;Tavola;TECHNAME;DMC;SBC ALC_00;SBC ALC_01;SBC ALC_02;SBC ALC_03;SBC ALC_05;SBC ALC_06;SBC ALC_08;SBC ALC_09;SNS;ICN_TITLE;ICN_Tavola;ICN_Logo;Logo;Figura");
            }
            else
            {
                csv.AppendLine("CodiceTecnico;Tavola;Descrizione1;Descrizione2;Descrizione3;Logo;Figura");
            }

            // Dati
            foreach (var table in tables)
            {
                if (isS1000D)
                {
                    var detail = tableDetails.GetValueOrDefault(table.Tavola);
                    var sbcParts = (table.SBC ?? "").Split(',').Select(s => s.Trim()).ToArray();
                    
                    csv.AppendLine(string.Join(";", new string[]
                    {
                        EscapeCsvField(table.CodiceTecnico ?? ""),
                        EscapeCsvField(table.Tavola),
                        EscapeCsvField(detail?.TECHNAME ?? ""),
                        EscapeCsvField(detail?.DMC ?? ""),
                        EscapeCsvField(sbcParts.Length > 0 ? sbcParts[0] : ""),
                        EscapeCsvField(sbcParts.Length > 1 ? sbcParts[1] : ""),
                        EscapeCsvField(sbcParts.Length > 2 ? sbcParts[2] : ""),
                        EscapeCsvField(sbcParts.Length > 3 ? sbcParts[3] : ""),
                        EscapeCsvField(sbcParts.Length > 4 ? sbcParts[4] : ""),
                        EscapeCsvField(sbcParts.Length > 5 ? sbcParts[5] : ""),
                        EscapeCsvField(sbcParts.Length > 6 ? sbcParts[6] : ""),
                        EscapeCsvField(sbcParts.Length > 7 ? sbcParts[7] : ""),
                        EscapeCsvField(table.SNS ?? ""),
                        EscapeCsvField(detail?.ICN_TITLE ?? ""),
                        EscapeCsvField(detail?.ICN_Tavola ?? ""),
                        EscapeCsvField(detail?.ICN_Logo ?? ""),
                        EscapeCsvField(table.Logo ?? ""),
                        EscapeCsvField(table.Figura ?? "")
                    }));
                }
                else
                {
                    csv.AppendLine(string.Join(";", new string[]
                    {
                        EscapeCsvField(table.CodiceTecnico ?? ""),
                        EscapeCsvField(table.Tavola),
                        EscapeCsvField(GetTableDescription1(table, catalog.CEDLanguage)),
                        EscapeCsvField(GetTableDescription2(table, catalog.CEDLanguage)),
                        EscapeCsvField(GetTableDescription3(table, catalog.CEDLanguage)),
                        EscapeCsvField(table.Logo ?? ""),
                        EscapeCsvField(table.Figura ?? "")
                    }));
                }
            }

            return csv.ToString();
        }

        private async Task<string> ExportSingolaTavolaToCsvAsync(string ter, string tavola, bool isS1000D)
        {
            var catalog = await _context.TB_Cataloghi.FirstOrDefaultAsync(c => c.TER == ter);
            if (catalog == null) throw new ArgumentException($"Catalogo {ter} non trovato");

            var tableDetail = await _context.TB_DettagliTavole.FirstOrDefaultAsync(t => t.Tavola == tavola);
            if (tableDetail == null) throw new ArgumentException($"Tavola {tavola} non trovata");

            var tableInfo = await _context.TB_Tavole.FirstOrDefaultAsync(t => t.Tavola == tavola && t.TER == ter);
            
            var parts = await _context.TB_Parti
                .Where(p => p.Tavola == tavola)
                .OrderBy(p => int.Parse(p.ITEM))
                .ToListAsync();

            var components = await _context.TB_Componenti
                .Where(c => parts.Select(p => p.PART).Contains(c.PART))
                .ToDictionaryAsync(c => c.PART, c => c);

            var csv = new StringBuilder();

            // Header tavola
            csv.AppendLine($"Catalogo;Tavola;CodTecnico;{(isS1000D ? "SnS;sbc;TechName;IcnTitle;Dmc;IcnTavola;IcnLogo;" : "")}Figura;Logo");
            
            // Info tavola
            if (isS1000D)
            {
                csv.AppendLine(string.Join(";", new string[]
                {
                    EscapeCsvField(ter),
                    EscapeCsvField(tavola),
                    EscapeCsvField(tableDetail.CodiceTecnico ?? ""),
                    EscapeCsvField(tableDetail.SNS ?? ""),
                    EscapeCsvField(tableDetail.SBC ?? ""),
                    EscapeCsvField(tableInfo?.TECHNAME ?? ""),
                    EscapeCsvField(tableInfo?.ICN_TITLE ?? ""),
                    EscapeCsvField(tableInfo?.DMC ?? ""),
                    EscapeCsvField(tableInfo?.ICN_Tavola ?? ""),
                    EscapeCsvField(tableInfo?.ICN_Logo ?? ""),
                    EscapeCsvField(tableDetail.Figura ?? ""),
                    EscapeCsvField(tableDetail.Logo ?? "")
                }));
            }
            else
            {
                csv.AppendLine(string.Join(";", new string[]
                {
                    EscapeCsvField(ter),
                    EscapeCsvField(tavola),
                    EscapeCsvField(tableDetail.CodiceTecnico ?? ""),
                    EscapeCsvField(tableDetail.Figura ?? ""),
                    EscapeCsvField(tableDetail.Logo ?? "")
                }));
            }

            csv.AppendLine();

            // Header parti
            csv.AppendLine("ITEM;PART;QTAV;Descrizione;CodF;NUC" + (isS1000D ? ";CSNREF;SMRCode;ILS" : ";Assieme;CodModifica"));

            // Dati parti
            foreach (var part in parts)
            {
                var component = components.GetValueOrDefault(part.PART);
                var description = component != null ? GetComponentDescription(component, catalog.CEDLanguage) : "";
                
                var row = new List<string>
                {
                    EscapeCsvField(part.ITEM),
                    EscapeCsvField(part.PART),
                    EscapeCsvField(part.QTAV),
                    EscapeCsvField(description),
                    EscapeCsvField(component?.CodF ?? ""),
                    EscapeCsvField(component?.NUC ?? "")
                };

                if (isS1000D)
                {
                    row.AddRange(new string[]
                    {
                        EscapeCsvField(part.CSNREF ?? ""),
                        EscapeCsvField(part.SMRCode ?? ""),
                        EscapeCsvField(part.ILS?.ToString() ?? "false")
                    });
                }
                else
                {
                    row.AddRange(new string[]
                    {
                        EscapeCsvField(part.Assieme ?? ""),
                        EscapeCsvField(part.CodModifica ?? "")
                    });
                }

                csv.AppendLine(string.Join(";", row));
            }

            return csv.ToString();
        }

        private async Task<DataTable> ExecuteStoredProcedureAsync(string procedureName)
        {
            var dataTable = new DataTable();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using var connection = new SqlConnection(connectionString);
            using var command = new SqlCommand(procedureName, connection);
            command.CommandType = CommandType.StoredProcedure;

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();
            dataTable.Load(reader);

            return dataTable;
        }

        private async Task<byte[]> CreateExcelFromDataTableAsync(DataTable dataTable)
        {
            using var stream = new MemoryStream();
            using var workbook = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            
            // Implementare la creazione Excel da DataTable
            // Basandosi su ExportDataTableToExcel del Framework
            
            var workbookPart = workbook.AddWorkbookPart();
            workbook.WorkbookPart.Workbook = new Workbook();
            workbook.WorkbookPart.Workbook.Sheets = new Sheets();

            var sheetPart = workbook.WorkbookPart.AddNewPart<WorksheetPart>();
            var sheetData = new SheetData();
            sheetPart.Worksheet = new Worksheet(sheetData);

            var sheets = workbook.WorkbookPart.Workbook.GetFirstChild<Sheets>();
            var relationshipId = workbook.WorkbookPart.GetIdOfPart(sheetPart);
            var sheet = new Sheet() { Id = relationshipId, SheetId = 1, Name = dataTable.TableName };
            sheets.Append(sheet);

            // Aggiungere header
            var headerRow = new Row();
            foreach (DataColumn column in dataTable.Columns)
            {
                var cell = new Cell
                {
                    DataType = CellValues.String,
                    CellValue = new CellValue(column.ColumnName)
                };
                headerRow.AppendChild(cell);
            }
            sheetData.AppendChild(headerRow);

            // Aggiungere righe dati
            foreach (DataRow dataRow in dataTable.Rows)
            {
                var newRow = new Row();
                foreach (var field in dataRow.ItemArray)
                {
                    var cell = new Cell
                    {
                        DataType = CellValues.String,
                        CellValue = new CellValue(field?.ToString() ?? "")
                    };
                    newRow.AppendChild(cell);
                }
                sheetData.AppendChild(newRow);
            }

            workbook.Save();
            return stream.ToArray();
        }

        private string ConvertDataTableToCsv(DataTable dataTable)
        {
            var csv = new StringBuilder();
            
            // Header
            var columnNames = dataTable.Columns.Cast<DataColumn>()
                .Select(column => EscapeCsvField(column.ColumnName));
            csv.AppendLine(string.Join(";", columnNames));

            // Righe dati
            foreach (DataRow row in dataTable.Rows)
            {
                var fields = row.ItemArray.Select(field => EscapeCsvField(field?.ToString() ?? ""));
                csv.AppendLine(string.Join(";", fields));
            }

            return csv.ToString();
        }

        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field)) return "";
            
            if (field.Contains(";") || field.Contains("\"") || field.Contains("\n"))
            {
                field = field.Replace("\"", "\"\"");
                return $"\"{field}\"";
            }
            
            return field;
        }

        private string GetTavolaDescrizione(dynamic tavola, string? language)
        {
            // Implementare la logica per ottenere la descrizione nella lingua corretta
            return language?.ToLower() switch
            {
                "en" or "english" => tavola.Descrizione1EN ?? tavola.Descrizione1IT ?? "",
                "fr" or "french" => tavola.Descrizione1FR ?? tavola.Descrizione1IT ?? "",
                "de" or "german" => tavola.Descrizione1TED ?? tavola.Descrizione1IT ?? "",
                "es" or "spanish" => tavola.Descrizione1ES ?? tavola.Descrizione1IT ?? "",
                "pt" or "portuguese" => tavola.Descrizione1PT ?? tavola.Descrizione1IT ?? "",
                "usa" or "usa english" => tavola.Descrizione1USA ?? tavola.Descrizione1EN ?? tavola.Descrizione1IT ?? "",
                _ => tavola.Descrizione1IT ?? ""
            };
        }

        private DataTable CreatePartsDataTable(List<TB_Parti> parts, List<TB_Componenti> components, TB_Cataloghi catalog, bool isS1000D)
        {
            var table = new DataTable(isS1000D ? "Parti S1000D" : "Parti");
            var componentDict = components.ToDictionary(c => c.PART, c => c);

            // Colonne base
            table.Columns.Add("Tavola", typeof(string));
            table.Columns.Add("ITEM", typeof(string));
            table.Columns.Add("PART", typeof(string));
            table.Columns.Add("QTAV", typeof(string));
            table.Columns.Add("Descrizione", typeof(string));
            table.Columns.Add("CodF", typeof(string));
            table.Columns.Add("NUC", typeof(string));
            
            if (isS1000D)
            {
                table.Columns.Add("CSNREF", typeof(string));
                table.Columns.Add("SMRCode", typeof(string));
                table.Columns.Add("ILS", typeof(string));
            }
            else
            {
                table.Columns.Add("Assieme", typeof(string));
                table.Columns.Add("CodModifica", typeof(string));
            }

            foreach (var part in parts)
            {
                var row = table.NewRow();
                row["Tavola"] = part.Tavola;
                row["ITEM"] = part.ITEM;
                row["PART"] = part.PART;
                row["QTAV"] = part.QTAV;
                
                if (componentDict.TryGetValue(part.PART, out var component))
                {
                    row["Descrizione"] = GetComponentDescription(component, catalog.CEDLanguage);
                    row["CodF"] = component.CodF ?? "";
                    row["NUC"] = component.NUC ?? "";
                }

                if (isS1000D)
                {
                    row["CSNREF"] = part.CSNREF ?? "";
                    row["SMRCode"] = part.SMRCode ?? "";
                    row["ILS"] = part.ILS?.ToString() ?? "false";
                }
                else
                {
                    row["Assieme"] = part.Assieme ?? "";
                    row["CodModifica"] = part.CodModifica ?? "";
                }

                table.Rows.Add(row);
            }

            return table;
        }

        private DataTable CreateTablesDataTable(List<TB_DettagliTavole> tables, List<TB_Tavole> tableDetails, TB_Cataloghi catalog, bool isS1000D)
        {
            var table = new DataTable(isS1000D ? "Tavole S1000D" : "Tavole");
            var detailsDict = tableDetails.ToDictionary(t => t.Tavola, t => t);

            // Colonne base
            table.Columns.Add("Tavola", typeof(string));
            table.Columns.Add("CodiceTecnico", typeof(string));
            table.Columns.Add("Descrizione1", typeof(string));
            table.Columns.Add("Descrizione2", typeof(string));
            table.Columns.Add("Logo", typeof(string));
            table.Columns.Add("Figura", typeof(string));

            if (isS1000D)
            {
                table.Columns.Add("DMC", typeof(string));
                table.Columns.Add("TECHNAME", typeof(string));
                table.Columns.Add("SNS", typeof(string));
                table.Columns.Add("SBC", typeof(string));
                table.Columns.Add("ICN_TITLE", typeof(string));
                table.Columns.Add("ICN_Tavola", typeof(string));
            }

            foreach (var tbl in tables)
            {
                var row = table.NewRow();
                row["Tavola"] = tbl.Tavola;
                row["CodiceTecnico"] = tbl.CodiceTecnico ?? "";
                row["Descrizione1"] = GetTableDescription1(tbl, catalog.CEDLanguage);
                row["Descrizione2"] = GetTableDescription2(tbl, catalog.CEDLanguage);
                row["Logo"] = tbl.Logo ?? "";
                row["Figura"] = tbl.Figura ?? "";

                if (isS1000D && detailsDict.TryGetValue(tbl.Tavola, out var detail))
                {
                    row["DMC"] = detail.DMC ?? "";
                    row["TECHNAME"] = detail.TECHNAME ?? "";
                    row["SNS"] = tbl.SNS ?? "";
                    row["SBC"] = tbl.SBC ?? "";
                    row["ICN_TITLE"] = detail.ICN_TITLE ?? "";
                    row["ICN_Tavola"] = detail.ICN_Tavola ?? "";
                }

                table.Rows.Add(row);
            }

            return table;
        }

        private DataTable CreateRvtDataTable(List<TB_RVT> rvts, List<TB_CataloghiRVT> catalogRvts, TB_Cataloghi catalog)
        {
            var table = new DataTable("RVT");
            var rvtDict = catalogRvts.ToDictionary(cr => cr.RVT, cr => cr);

            table.Columns.Add("RVT", typeof(string));
            table.Columns.Add("Data", typeof(string));
            table.Columns.Add("Titolo", typeof(string));
            table.Columns.Add("Applicabilità", typeof(string));

            foreach (var rvt in rvts)
            {
                var row = table.NewRow();
                row["RVT"] = rvt.RVT;
                row["Data"] = rvt.Data?.ToShortDateString() ?? "";
                row["Titolo"] = GetRvtTitle(rvt, catalog.CEDLanguage);
                
                if (rvtDict.TryGetValue(rvt.RVT, out var catalogRvt))
                {
                    row["Applicabilità"] = GetRvtApplicability(catalogRvt, catalog.CEDLanguage);
                }

                table.Rows.Add(row);
            }

            return table;
        }

        private string GetComponentDescription(TB_Componenti component, string? language)
        {
            return language?.ToLower() switch
            {
                "en" or "english" => component.DescrEN ?? component.DescrIT ?? "",
                "fr" or "french" => component.DescrFR ?? component.DescrIT ?? "",
                "de" or "german" => component.DescrTED ?? component.DescrIT ?? "",
                "es" or "spanish" => component.DescrES ?? component.DescrIT ?? "",
                "pt" or "portuguese" => component.DescrPORT ?? component.DescrIT ?? "",
                "usa" or "usa english" => component.DescrUSA ?? component.DescrEN ?? component.DescrIT ?? "",
                _ => component.DescrIT ?? ""
            };
        }

        private string GetTableDescription1(TB_DettagliTavole table, string? language)
        {
            return language?.ToLower() switch
            {
                "en" or "english" => table.Descrizione1EN ?? table.Descrizione1IT ?? "",
                "fr" or "french" => table.Descrizione1FR ?? table.Descrizione1IT ?? "",
                "de" or "german" => table.Descrizione1TED ?? table.Descrizione1IT ?? "",
                "es" or "spanish" => table.Descrizione1ES ?? table.Descrizione1IT ?? "",
                "pt" or "portuguese" => table.Descrizione1PT ?? table.Descrizione1IT ?? "",
                "usa" or "usa english" => table.Descrizione1USA ?? table.Descrizione1EN ?? table.Descrizione1IT ?? "",
                _ => table.Descrizione1IT ?? ""
            };
        }

        private string GetTableDescription2(TB_DettagliTavole table, string? language)
        {
            return language?.ToLower() switch
            {
                "en" or "english" => table.Descrizione2EN ?? table.Descrizione2IT ?? "",
                "fr" or "french" => table.Descrizione2FR ?? table.Descrizione2IT ?? "",
                "de" or "german" => table.Descrizione2TED ?? table.Descrizione2IT ?? "",
                "es" or "spanish" => table.Descrizione2ES ?? table.Descrizione2IT ?? "",
                "pt" or "portuguese" => table.Descrizione2PT ?? table.Descrizione2IT ?? "",
                "usa" or "usa english" => table.Descrizione2USA ?? table.Descrizione2EN ?? table.Descrizione2IT ?? "",
                _ => table.Descrizione2IT ?? ""
            };
        }

        private string GetTableDescription3(TB_DettagliTavole table, string? language)
        {
            return language?.ToLower() switch
            {
                "en" or "english" => table.Descrizione3EN ?? table.Descrizione3IT ?? "",
                "fr" or "french" => table.Descrizione3FR ?? table.Descrizione3IT ?? "",
                "de" or "german" => table.Descrizione3TED ?? table.Descrizione3IT ?? "",
                "es" or "spanish" => table.Descrizione3ES ?? table.Descrizione3IT ?? "",
                "pt" or "portuguese" => table.Descrizione3PT ?? table.Descrizione3IT ?? "",
                "usa" or "usa english" => table.Descrizione3USA ?? table.Descrizione3EN ?? table.Descrizione3IT ?? "",
                _ => table.Descrizione3IT ?? ""
            };
        }

        private string GetRvtTitle(TB_RVT rvt, string? language)
        {
            return language?.ToLower() switch
            {
                "en" or "english" => rvt.TitoloEN ?? rvt.Titolo ?? "",
                "fr" or "french" => rvt.TitoloFR ?? rvt.Titolo ?? "",
                "de" or "german" => rvt.TitoloTED ?? rvt.Titolo ?? "",
                "es" or "spanish" => rvt.TitoloES ?? rvt.Titolo ?? "",
                "pt" or "portuguese" => rvt.TitoloPT ?? rvt.Titolo ?? "",
                "usa" or "usa english" => rvt.TitoloUSA ?? rvt.TitoloEN ?? rvt.Titolo ?? "",
                _ => rvt.Titolo ?? ""
            };
        }

        private string GetRvtApplicability(TB_CataloghiRVT catalogRvt, string? language)
        {
            return language?.ToLower() switch
            {
                "en" or "english" => catalogRvt.ApplicabilitàEN ?? catalogRvt.Applicabilità ?? "",
                "fr" or "french" => catalogRvt.ApplicabilitàFR ?? catalogRvt.Applicabilità ?? "",
                "de" or "german" => catalogRvt.Applicabilità ?? "", // ApplicabilitàTED non esiste nel modello
                "es" or "spanish" => catalogRvt.ApplicabilitàES ?? catalogRvt.Applicabilità ?? "",
                "pt" or "portuguese" => catalogRvt.ApplicabilitàPT ?? catalogRvt.Applicabilità ?? "",
                "usa" or "usa english" => catalogRvt.ApplicabilitàUSA ?? catalogRvt.ApplicabilitàEN ?? catalogRvt.Applicabilità ?? "",
                _ => catalogRvt.Applicabilità ?? ""
            };
        }

        private bool ShouldIncludeTable(string tableName, bool isS1000D)
        {
            if (tableName.Contains("S1000D"))
                return isS1000D;
            if (isS1000D)
                return !tableName.Contains("Standard");
            return true;
        }

        private string CleanSheetName(string tableName)
        {
            // Excel sheet names limitations
            return tableName.Replace("S1000D", "").Replace("Standard", "").Trim().Substring(0, Math.Min(31, tableName.Length));
        }
    }
}
