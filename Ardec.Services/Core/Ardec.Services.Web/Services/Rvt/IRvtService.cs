using Ardec.Services.Web.Data.Models;

namespace Ardec.Services.Web.Services.Rvt;

public interface IRvtService
{
    Task<IEnumerable<TB_RVT>> GetAllAsync();
    Task<IEnumerable<TB_RVT>> GetByTerAsync(string ter);
    Task<TB_RVT?> GetByCompositeKeyAsync(string ter, string rvt);
    Task<TB_RVT> CreateAsync(TB_RVT rvt);
    Task<TB_RVT> UpdateAsync(TB_RVT rvt);
    Task<bool> DeleteAsync(string ter, string rvt);
    Task<bool> ExistsAsync(string ter, string rvt);
    Task<IEnumerable<TB_RVT>> SearchAsync(string searchTerm);
    Task<int> GetTotalCountAsync();
}
