using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace Ardec.Services.Web.Services.Rvt;

public class RvtService : IRvtService
{
    private readonly ArdecDbContext _context;
    private readonly ILogger<RvtService> _logger;

    public RvtService(ArdecDbContext context, ILogger<RvtService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<TB_RVT>> GetAllAsync()
    {
        try
        {
            return await _context.TB_RVT
                .OrderBy(r => r.TER)
                .ThenBy(r => r.RVT)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all RVT");
            throw;
        }
    }

    public async Task<IEnumerable<TB_RVT>> GetByTerAsync(string ter)
    {
        if (string.IsNullOrWhiteSpace(ter))
            throw new ArgumentException("TER cannot be null or empty", nameof(ter));

        try
        {
            return await _context.TB_RVT
                .Where(r => r.TER == ter)
                .OrderBy(r => r.RVT)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving RVT for TER: {TER}", ter);
            throw;
        }
    }

    public async Task<TB_RVT?> GetByCompositeKeyAsync(string ter, string rvt)
    {
        if (string.IsNullOrWhiteSpace(ter) || string.IsNullOrWhiteSpace(rvt))
            return null;

        try
        {
            return await _context.TB_RVT
                .FirstOrDefaultAsync(r => r.TER == ter && r.RVT == rvt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving RVT with composite key: TER={TER}, RVT={RVT}", ter, rvt);
            throw;
        }
    }

    public async Task<TB_RVT> CreateAsync(TB_RVT rvt)
    {
        if (rvt == null)
            throw new ArgumentNullException(nameof(rvt));

        if (string.IsNullOrWhiteSpace(rvt.TER) || string.IsNullOrWhiteSpace(rvt.RVT))
            throw new ArgumentException("TER and RVT are required");

        try
        {
            if (await ExistsAsync(rvt.TER, rvt.RVT))
                throw new InvalidOperationException($"RVT already exists: {rvt.TER}/{rvt.RVT}");

            _context.TB_RVT.Add(rvt);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created RVT: TER={TER}, RVT={RVT}", rvt.TER, rvt.RVT);
            return rvt;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating RVT: {TER}/{RVT}", rvt.TER, rvt.RVT);
            throw;
        }
    }

    public async Task<TB_RVT> UpdateAsync(TB_RVT rvt)
    {
        if (rvt == null)
            throw new ArgumentNullException(nameof(rvt));

        try
        {
            var existing = await GetByCompositeKeyAsync(rvt.TER, rvt.RVT);
            if (existing == null)
                throw new InvalidOperationException("RVT not found");

            // Update Framework model properties
            existing.Data = rvt.Data;
            existing.Titolo = rvt.Titolo;
            existing.TitoloES = rvt.TitoloES;
            existing.TitoloEN = rvt.TitoloEN;
            existing.TitoloPT = rvt.TitoloPT;
            existing.TitoloFR = rvt.TitoloFR;
            existing.TitoloUSA = rvt.TitoloUSA;
            existing.TitoloTED = rvt.TitoloTED;
            existing.Applicabilità = rvt.Applicabilità;
            existing.ApplicabilitàEN = rvt.ApplicabilitàEN;
            existing.ApplicabilitàES = rvt.ApplicabilitàES;
            existing.ApplicabilitàPT = rvt.ApplicabilitàPT;
            existing.ApplicabilitàFR = rvt.ApplicabilitàFR;
            existing.ApplicabilitàUSA = rvt.ApplicabilitàUSA;
            existing.ApplicabilitàTED = rvt.ApplicabilitàTED;
            existing.CEDNPagina = rvt.CEDNPagina;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated RVT: {TER}/{RVT}", rvt.TER, rvt.RVT);
            return existing;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating RVT: {TER}/{RVT}", rvt.TER, rvt.RVT);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string ter, string rvt)
    {
        if (string.IsNullOrWhiteSpace(ter) || string.IsNullOrWhiteSpace(rvt))
            return false;

        try
        {
            var existing = await GetByCompositeKeyAsync(ter, rvt);
            if (existing == null)
                return false;

            _context.TB_RVT.Remove(existing);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted RVT: {TER}/{RVT}", ter, rvt);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting RVT: {TER}/{RVT}", ter, rvt);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(string ter, string rvt)
    {
        if (string.IsNullOrWhiteSpace(ter) || string.IsNullOrWhiteSpace(rvt))
            return false;

        try
        {
            return await _context.TB_RVT
                .AnyAsync(r => r.TER == ter && r.RVT == rvt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if RVT exists: {TER}/{RVT}", ter, rvt);
            throw;
        }
    }

    public async Task<IEnumerable<TB_RVT>> SearchAsync(string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return await GetAllAsync();

        try
        {
            var term = searchTerm.ToLower().Trim();

            return await _context.TB_RVT
                .Where(r =>
                    r.TER.ToLower().Contains(term) ||
                    r.RVT.ToLower().Contains(term) ||
                    r.Titolo.ToLower().Contains(term) ||
                    r.TitoloEN.ToLower().Contains(term) ||
                    r.Applicabilità.ToLower().Contains(term))
                .OrderBy(r => r.TER)
                .ThenBy(r => r.RVT)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching RVT with term: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<int> GetTotalCountAsync()
    {
        try
        {
            return await _context.TB_RVT.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total RVT count");
            throw;
        }
    }
}
