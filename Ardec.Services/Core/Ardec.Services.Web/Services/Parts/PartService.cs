using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace Ardec.Services.Web.Services.Parts;

/// <summary>
/// Complete Part Service Implementation - Framework-compatible
/// Main class with basic CRUD operations aligned to real TB_Parti model
/// </summary>
public partial class PartService : IPartService
{
    private readonly ArdecDbContext _context;
    private readonly ILogger<PartService> _logger;

    public PartService(ArdecDbContext context, ILogger<PartService> logger)
    {
        _context = context;
        _logger = logger;
    }

    #region Basic CRUD Operations

    public async Task<IEnumerable<TB_Parti>> GetAllAsync()
    {
        try
        {
            return await _context.TB_Parti
                .OrderBy(p => p.Tavola)
                .ThenBy(p => p.ITEM)
                .ThenBy(p => p.PART)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all parts");
            throw;
        }
    }

    public async Task<IEnumerable<TB_Parti>> GetAllLazyAsync()
    {
        try
        {
            return await _context.TB_Parti
                .AsNoTracking()
                .OrderBy(p => p.Tavola)
                .ThenBy(p => p.ITEM)
                .ThenBy(p => p.PART)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all parts (lazy)");
            throw;
        }
    }

    public async Task<IEnumerable<TB_Parti>> GetByTavolaAsync(string tavola)
    {
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));

        try
        {
            return await _context.TB_Parti
                .Where(p => p.Tavola == tavola)
                .OrderBy(p => p.ITEM)
                .ThenBy(p => p.PART)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving parts by tavola: {Tavola}", tavola);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Parti>> GetByTavolaAndVersionAsync(string tavola, string versione)
    {
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));
        if (string.IsNullOrWhiteSpace(versione))
            throw new ArgumentException("Versione cannot be null or empty", nameof(versione));

        try
        {
            return await _context.TB_Parti
                .Where(p => p.Tavola == tavola && p.Versione == versione)
                .OrderBy(p => p.ITEM)
                .ThenBy(p => p.PART)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving parts by tavola and version: {Tavola}, {Versione}", tavola, versione);
            throw;
        }
    }

    public async Task<TB_Parti?> GetByCompositeKeyAsync(string tavola, string part, string item, string versione)
    {
        if (string.IsNullOrWhiteSpace(tavola) || string.IsNullOrWhiteSpace(part) || 
            string.IsNullOrWhiteSpace(item) || string.IsNullOrWhiteSpace(versione))
            throw new ArgumentException("All composite key components are required");

        try
        {
            return await _context.TB_Parti
                .FirstOrDefaultAsync(p => p.Tavola == tavola && p.PART == part && p.ITEM == item && p.Versione == versione);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving part by composite key: {Tavola}-{Part}-{Item}-{Versione}", 
                tavola, part, item, versione);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Parti>> GetByItemAsync(string item)
    {
        if (string.IsNullOrWhiteSpace(item))
            throw new ArgumentException("Item cannot be null or empty", nameof(item));

        try
        {
            return await _context.TB_Parti
                .Where(p => p.ITEM == item)
                .OrderBy(p => p.Tavola)
                .ThenBy(p => p.PART)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving parts by item: {Item}", item);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Parti>> GetByPartNumberAsync(string partNumber)
    {
        if (string.IsNullOrWhiteSpace(partNumber))
            throw new ArgumentException("Part number cannot be null or empty", nameof(partNumber));

        try
        {
            return await _context.TB_Parti
                .Where(p => p.PART == partNumber)
                .OrderBy(p => p.Tavola)
                .ThenBy(p => p.ITEM)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving parts by part number: {PartNumber}", partNumber);
            throw;
        }
    }

    public async Task<TB_Parti> CreateAsync(TB_Parti part)
    {
        if (part == null)
            throw new ArgumentNullException(nameof(part));

        // Validate composite key
        if (string.IsNullOrWhiteSpace(part.Tavola) || string.IsNullOrWhiteSpace(part.PART) || 
            string.IsNullOrWhiteSpace(part.ITEM) || string.IsNullOrWhiteSpace(part.Versione))
            throw new ArgumentException("All composite key components are required");

        try
        {
            if (await ExistsAsync(part.Tavola, part.PART, part.ITEM, part.Versione))
                throw new InvalidOperationException($"Part already exists: {part.Tavola}-{part.PART}-{part.ITEM}-{part.Versione}");

            _context.TB_Parti.Add(part);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Created part: {Tavola}-{Part}-{Item}-{Versione}", 
                part.Tavola, part.PART, part.ITEM, part.Versione);
            return part;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating part: {Tavola}-{Part}-{Item}-{Versione}", 
                part.Tavola, part.PART, part.ITEM, part.Versione);
            throw;
        }
    }

    public async Task<TB_Parti> UpdateAsync(TB_Parti part)
    {
        if (part == null)
            throw new ArgumentNullException(nameof(part));

        try
        {
            var existingPart = await GetByCompositeKeyAsync(part.Tavola, part.PART, part.ITEM, part.Versione);
            if (existingPart == null)
                throw new InvalidOperationException($"Part not found: {part.Tavola}-{part.PART}-{part.ITEM}-{part.Versione}");

            // Update all TB_Parti real properties
            existingPart.CID = part.CID;
            existingPart.QTAV = part.QTAV;
            existingPart.CodModifica = part.CodModifica;
            existingPart.NotaRVT = part.NotaRVT;
            existingPart.NotaRVTEN = part.NotaRVTEN;
            existingPart.NotaRVTES = part.NotaRVTES;
            existingPart.NotaRVTPT = part.NotaRVTPT;
            existingPart.NotaRVTFR = part.NotaRVTFR;
            existingPart.NotaRVTUSA = part.NotaRVTUSA;
            existingPart.Nota2 = part.Nota2;
            existingPart.Assieme = part.Assieme;
            existingPart.FasciaManutentiva = part.FasciaManutentiva;
            existingPart.CEDNPagina = part.CEDNPagina;
            existingPart.CEDNColonna = part.CEDNColonna;
            existingPart.CSN = part.CSN;
            existingPart.CSNREF = part.CSNREF;
            existingPart.Item1 = part.Item1;
            existingPart.Variante_Item = part.Variante_Item;
            existingPart.ILS = part.ILS;
            existingPart.SMRCode = part.SMRCode;
            existingPart.UM = part.UM;
            existingPart.DOT = part.DOT;
            existingPart.LCN = part.LCN;
            existingPart.ALC = part.ALC;
            existingPart.MODIDX = part.MODIDX;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Updated part: {Tavola}-{Part}-{Item}-{Versione}", 
                part.Tavola, part.PART, part.ITEM, part.Versione);
            return existingPart;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating part: {Tavola}-{Part}-{Item}-{Versione}", 
                part.Tavola, part.PART, part.ITEM, part.Versione);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string tavola, string part, string item, string versione)
    {
        try
        {
            var existingPart = await GetByCompositeKeyAsync(tavola, part, item, versione);
            if (existingPart == null)
                return false;

            _context.TB_Parti.Remove(existingPart);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Deleted part: {Tavola}-{Part}-{Item}-{Versione}", tavola, part, item, versione);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting part: {Tavola}-{Part}-{Item}-{Versione}", tavola, part, item, versione);
            throw;
        }
    }

    public async Task<bool> DeleteAllByTavolaAsync(string tavola)
    {
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));

        try
        {
            var parts = await _context.TB_Parti
                .Where(p => p.Tavola == tavola)
                .ToListAsync();

            if (!parts.Any())
                return false;

            _context.TB_Parti.RemoveRange(parts);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Deleted {Count} parts for tavola: {Tavola}", parts.Count, tavola);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting all parts for tavola: {Tavola}", tavola);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(string tavola, string part, string item, string versione)
    {
        try
        {
            return await _context.TB_Parti
                .AnyAsync(p => p.Tavola == tavola && p.PART == part && p.ITEM == item && p.Versione == versione);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if part exists: {Tavola}-{Part}-{Item}-{Versione}", 
                tavola, part, item, versione);
            throw;
        }
    }

    #endregion
}
