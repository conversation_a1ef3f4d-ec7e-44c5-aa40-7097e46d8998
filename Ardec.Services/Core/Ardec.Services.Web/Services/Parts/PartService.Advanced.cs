using Ardec.Services.Web.Data.Models;
using Microsoft.EntityFrameworkCore;
using Ardec.Services.Web.DTOs.Parts;

namespace Ardec.Services.Web.Services.Parts;

/// <summary>
/// PartService partial class - Advanced Operations
/// Handles advanced search, CID management, and versioning operations
/// </summary>
public partial class PartService
{
    #region Advanced Search Operations

    /// <summary>
    /// Advanced search with multiple criteria
    /// </summary>
    public async Task<IEnumerable<TB_Parti>> AdvancedSearchAsync(PartSearchCriteria criteria)
    {
        try
        {
            var query = _context.TB_Parti.AsQueryable();

            if (!string.IsNullOrWhiteSpace(criteria.Tavola))
                query = query.Where(p => EF.Functions.Like(p.Tavola, $"%{criteria.Tavola}%"));

            if (!string.IsNullOrWhiteSpace(criteria.PartNumber))
                query = query.Where(p => EF.Functions.Like(p.PART, $"%{criteria.PartNumber}%"));

            if (!string.IsNullOrWhiteSpace(criteria.Item))
                query = query.Where(p => EF.Functions.Like(p.ITEM, $"%{criteria.Item}%"));

            if (!string.IsNullOrWhiteSpace(criteria.Csnref))
                query = query.Where(p => EF.Functions.Like(p.CSNREF ?? "", $"%{criteria.Csnref}%"));

            if (!string.IsNullOrWhiteSpace(criteria.Lcn))
                query = query.Where(p => EF.Functions.Like(p.LCN ?? "", $"%{criteria.Lcn}%"));

            if (!string.IsNullOrWhiteSpace(criteria.Assieme))
                query = query.Where(p => EF.Functions.Like(p.Assieme ?? "", $"%{criteria.Assieme}%"));

            if (criteria.ILS.HasValue)
                query = query.Where(p => p.ILS == criteria.ILS.Value);

            if (!string.IsNullOrWhiteSpace(criteria.Versione))
                query = query.Where(p => p.Versione == criteria.Versione);

            return await query
                .OrderBy(p => p.Tavola)
                .ThenBy(p => p.PART)
                .ThenBy(p => p.ITEM)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing advanced search");
            throw;
        }
    }

    /// <summary>
    /// Search parts by Assieme
    /// </summary>
    public async Task<IEnumerable<TB_Parti>> SearchByAssiemeAsync(string assieme)
    {
        if (string.IsNullOrWhiteSpace(assieme))
            throw new ArgumentException("Assieme cannot be null or empty", nameof(assieme));

        try
        {
            return await _context.TB_Parti
                .Where(p => !string.IsNullOrEmpty(p.Assieme) && EF.Functions.Like(p.Assieme, $"%{assieme}%"))
                .OrderBy(p => p.Tavola)
                .ThenBy(p => p.PART)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching parts by Assieme: {Assieme}", assieme);
            throw;
        }
    }

    /// <summary>
    /// Search parts by ILS status
    /// </summary>
    public async Task<IEnumerable<TB_Parti>> SearchByILSAsync(bool ilsStatus)
    {
        try
        {
            return await _context.TB_Parti
                .Where(p => p.ILS == ilsStatus)
                .OrderBy(p => p.Tavola)
                .ThenBy(p => p.PART)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching parts by ILS status: {ILSStatus}", ilsStatus);
            throw;
        }
    }

    #endregion

    #region CID and Versioning Operations

    /// <summary>
    /// Get parts by CID
    /// </summary>
    public async Task<IEnumerable<TB_Parti>> GetByCidAsync(string cid)
    {
        if (string.IsNullOrWhiteSpace(cid))
            throw new ArgumentException("CID cannot be null or empty", nameof(cid));

        try
        {
            return await _context.TB_Parti
                .Where(p => p.CID == cid)
                .OrderBy(p => p.Tavola)
                .ThenBy(p => p.PART)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving parts by CID: {CID}", cid);
            throw;
        }
    }

    /// <summary>
    /// Get all versions of a specific part (Tavola + PART + ITEM)
    /// </summary>
    public async Task<IEnumerable<TB_Parti>> GetVersionsAsync(string tavola, string part, string item)
    {
        if (string.IsNullOrWhiteSpace(tavola) || string.IsNullOrWhiteSpace(part) || string.IsNullOrWhiteSpace(item))
            throw new ArgumentException("Tavola, Part, and Item are required");

        try
        {
            return await _context.TB_Parti
                .Where(p => p.Tavola == tavola && p.PART == part && p.ITEM == item)
                .OrderBy(p => p.Versione)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving versions for part: {Tavola}-{Part}-{Item}", tavola, part, item);
            throw;
        }
    }

    /// <summary>
    /// Get latest version of a specific part
    /// </summary>
    public async Task<TB_Parti?> GetLatestVersionAsync(string tavola, string part, string item)
    {
        try
        {
            return await _context.TB_Parti
                .Where(p => p.Tavola == tavola && p.PART == part && p.ITEM == item)
                .OrderByDescending(p => p.Versione)
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving latest version for part: {Tavola}-{Part}-{Item}", tavola, part, item);
            throw;
        }
    }

    /// <summary>
    /// Validate CID uniqueness and conflicts
    /// </summary>
    public async Task<bool> ValidateCidAsync(string cid, string tavola, string part, string item)
    {
        if (string.IsNullOrWhiteSpace(cid))
            return false;

        try
        {
            // Check if CID already exists for other parts
            var existingParts = await GetByCidAsync(cid);
            
            // CID is valid if it doesn't exist or exists only for this specific part combination
            return !existingParts.Any() || existingParts.All(p => 
                p.Tavola == tavola && p.PART == part && p.ITEM == item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating CID: {CID}", cid);
            throw;
        }
    }

    /// <summary>
    /// Create new version of an existing part
    /// </summary>
    public async Task<TB_Parti> CreateNewVersionAsync(string tavola, string part, string item, string fromVersion, string toVersion, string? newCid = null)
    {
        try
        {
            var sourcePart = await GetByCompositeKeyAsync(tavola, part, item, fromVersion);
            if (sourcePart == null)
                throw new InvalidOperationException($"Source part not found: {tavola}-{part}-{item}-{fromVersion}");

            // Check if target version already exists
            if (await ExistsAsync(tavola, part, item, toVersion))
                throw new InvalidOperationException($"Target version already exists: {tavola}-{part}-{item}-{toVersion}");

            // Create new part with new version
            var newPart = new TB_Parti
            {
                Tavola = sourcePart.Tavola,
                PART = sourcePart.PART,
                ITEM = sourcePart.ITEM,
                Versione = toVersion,
                CID = newCid ?? sourcePart.CID, // Use provided CID or keep the same
                QTAV = sourcePart.QTAV,
                CodModifica = sourcePart.CodModifica,
                CSNREF = sourcePart.CSNREF,
                LCN = sourcePart.LCN,
                Assieme = sourcePart.Assieme,
                ILS = sourcePart.ILS
            };

            return await CreateAsync(newPart);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating new version: {Tavola}-{Part}-{Item} from v{FromVer} to v{ToVer}", 
                tavola, part, item, fromVersion, toVersion);
            throw;
        }
    }

    #endregion

    // Note: SearchByAssiemiAsync is already implemented in PartService.Search.cs
}
