using Ardec.Services.Web.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace Ardec.Services.Web.Services.Parts;

/// <summary>
/// PartService partial class - Basic Search Operations
/// Framework-compatible search functionality aligned to real TB_Parti fields
/// </summary>
public partial class PartService
{
    #region Basic Search Operations

    public async Task<IEnumerable<TB_Parti>> SearchAsync(string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return await GetAllLazyAsync();

        try
        {
            var term = searchTerm.ToLower().Trim();
            
            return await _context.TB_Parti
                .Where(p => EF.Functions.Like(p.PART.ToLower(), $"%{term}%") ||
                           EF.Functions.Like(p.ITEM.ToLower(), $"%{term}%") ||
                           EF.Functions.Like(p.Tavola.ToLower(), $"%{term}%") ||
                           EF.Functions.Like(p.NotaRVT ?? "", $"%{term}%") ||
                           EF.Functions.Like(p.CSNREF ?? "", $"%{term}%") ||
                           EF.Functions.Like(p.LCN ?? "", $"%{term}%"))
                .OrderBy(p => p.Tavola)
                .ThenBy(p => p.ITEM)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching parts with term: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Parti>> SearchByPartAsync(string partNumber)
    {
        if (string.IsNullOrWhiteSpace(partNumber))
            return Enumerable.Empty<TB_Parti>();

        try
        {
            return await _context.TB_Parti
                .Where(p => EF.Functions.Like(p.PART, $"%{partNumber}%"))
                .OrderBy(p => p.PART)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching parts by part number: {PartNumber}", partNumber);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Parti>> SearchByCsnrefAsync(string csnref)
    {
        if (string.IsNullOrWhiteSpace(csnref))
            return Enumerable.Empty<TB_Parti>();

        try
        {
            return await _context.TB_Parti
                .Where(p => !string.IsNullOrEmpty(p.CSNREF) && EF.Functions.Like(p.CSNREF, $"%{csnref}%"))
                .OrderBy(p => p.CSNREF)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching parts by CSNREF: {CSNREF}", csnref);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Parti>> SearchByLcnAsync(string lcn)
    {
        if (string.IsNullOrWhiteSpace(lcn))
            return Enumerable.Empty<TB_Parti>();

        try
        {
            return await _context.TB_Parti
                .Where(p => !string.IsNullOrEmpty(p.LCN) && EF.Functions.Like(p.LCN, $"%{lcn}%"))
                .OrderBy(p => p.LCN)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching parts by LCN: {LCN}", lcn);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Parti>> SearchByAssiemiAsync(string assieme)
    {
        if (string.IsNullOrWhiteSpace(assieme))
            return Enumerable.Empty<TB_Parti>();

        try
        {
            return await _context.TB_Parti
                .Where(p => !string.IsNullOrEmpty(p.Assieme) && EF.Functions.Like(p.Assieme, $"%{assieme}%"))
                .OrderBy(p => p.Assieme)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching parts by Assieme: {Assieme}", assieme);
            throw;
        }
    }

    #endregion
}
