using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.DTOs.Parts;

namespace Ardec.Services.Web.Services.Parts;

/// <summary>
/// Part Service Interface - Base implementation aligned to real TB_Parti model
/// Manages TB_Parti with composite keys (Tavola+PART+ITEM+Versione)
/// </summary>
public interface IPartService
{
    #region Basic CRUD Operations
    
    // Core retrieval methods
    Task<IEnumerable<TB_Parti>> GetAllAsync();
    Task<IEnumerable<TB_Parti>> GetAllLazyAsync();
    Task<IEnumerable<TB_Parti>> GetByTavolaAsync(string tavola);
    Task<IEnumerable<TB_Parti>> GetByTavolaAndVersionAsync(string tavola, string versione);
    Task<TB_Parti?> GetByCompositeKeyAsync(string tavola, string part, string item, string versione);
    Task<IEnumerable<TB_Parti>> GetByItemAsync(string item);
    Task<IEnumerable<TB_Parti>> GetByPartNumberAsync(string partNumber);
    
    // CRUD operations
    Task<TB_Parti> CreateAsync(TB_Parti part);
    Task<TB_Parti> UpdateAsync(TB_Parti part);
    Task<bool> DeleteAsync(string tavola, string part, string item, string versione);
    Task<bool> DeleteAllByTavolaAsync(string tavola);
    Task<bool> ExistsAsync(string tavola, string part, string item, string versione);
    
    #endregion
    
    #region Basic Search Operations
    
    // General search
    Task<IEnumerable<TB_Parti>> SearchAsync(string searchTerm);
    Task<IEnumerable<TB_Parti>> SearchByPartAsync(string partNumber);
    
    // Framework-specific searches based on real TB_Parti fields
    Task<IEnumerable<TB_Parti>> SearchByCsnrefAsync(string csnref);
    Task<IEnumerable<TB_Parti>> SearchByLcnAsync(string lcn);
    Task<IEnumerable<TB_Parti>> SearchByAssiemiAsync(string assieme);
    Task<IEnumerable<TB_Parti>> SearchByAssiemeAsync(string assieme);
    Task<IEnumerable<TB_Parti>> SearchByILSAsync(bool ilsStatus);
    Task<IEnumerable<TB_Parti>> AdvancedSearchAsync(PartSearchCriteria criteria);
    
    #endregion
    
    #region CID and Versioning Operations
    
    // CID operations
    Task<IEnumerable<TB_Parti>> GetByCidAsync(string cid);
    Task<bool> ValidateCidAsync(string cid, string tavola, string part, string item);
    
    // Versioning operations
    Task<IEnumerable<TB_Parti>> GetVersionsAsync(string tavola, string part, string item);
    Task<TB_Parti?> GetLatestVersionAsync(string tavola, string part, string item);
    Task<TB_Parti> CreateNewVersionAsync(string tavola, string part, string item, string fromVersion, string toVersion, string? newCid = null);
    
    #endregion
}
