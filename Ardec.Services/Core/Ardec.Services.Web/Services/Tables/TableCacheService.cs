using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Models.Tables;
using System.Text.Json;

namespace Ardec.Services.Web.Services.Tables
{
    public interface ITableCacheService
    {
        Task<TableCompositionDetail> GetTableAsync(string tavola, string versione, string ter, bool forceRefresh = false);
        Task<List<TableCompositionDetail>> GetTableVersionsAsync(string tavola, string ter, bool forceRefresh = false);
        Task<TableDetailViewModel> GetTableDetailAsync(string tavola, string versione, string ter, bool forceRefresh = false);
        Task<TablesDashboardData> GetTablesDashboardAsync(string? ter, string? search, int page, int pageSize, bool forceRefresh = false);
        Task<List<TableDashboardItemModel>> GetDashboardTablesAsync(string ter, bool forceRefresh = false);
        void InvalidateTable(string tavola, string ter);
        void InvalidateCatalog(string ter);
        void InvalidateAll();
        Task PreloadCatalogAsync(string ter);
        CacheStatistics GetCacheStatistics();
    }

    public class TableCacheService : ITableCacheService
    {
        private readonly IMemoryCache _cache;
        private readonly ArdecDbContext _context;
        private readonly ILogger<TableCacheService> _logger;
        private readonly SemaphoreSlim _semaphore = new(1, 1);
        private readonly Dictionary<string, DateTime> _lastAccess = new();
        private readonly Dictionary<string, int> _hitCount = new();
        
        // Cache key prefixes
        private const string TABLE_KEY_PREFIX = "table:";
        private const string VERSIONS_KEY_PREFIX = "versions:";
        private const string DETAIL_KEY_PREFIX = "detail:";
        private const string DASHBOARD_KEY_PREFIX = "dashboard:";
        private const string CATALOG_KEY_PREFIX = "catalog:";
        
        // Cache durations
        private readonly TimeSpan _shortCacheDuration = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _mediumCacheDuration = TimeSpan.FromMinutes(15);
        private readonly TimeSpan _longCacheDuration = TimeSpan.FromHours(1);
        
        public TableCacheService(
            IMemoryCache cache,
            ArdecDbContext context,
            ILogger<TableCacheService> logger)
        {
            _cache = cache;
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Get single table from cache or database
        /// </summary>
        public async Task<TableCompositionDetail> GetTableAsync(string tavola, string versione, string ter, bool forceRefresh = false)
        {
            var cacheKey = $"{TABLE_KEY_PREFIX}{ter}:{tavola}:{versione}";
            
            if (!forceRefresh && _cache.TryGetValue<TableCompositionDetail>(cacheKey, out var cachedTable))
            {
                UpdateStatistics(cacheKey, true);
                _logger.LogDebug("Table {Tavola} v{Version} retrieved from cache", tavola, versione);
                return cachedTable;
            }
            
            await _semaphore.WaitAsync();
            try
            {
                // Double-check after acquiring lock
                if (!forceRefresh && _cache.TryGetValue<TableCompositionDetail>(cacheKey, out cachedTable))
                {
                    UpdateStatistics(cacheKey, true);
                    return cachedTable;
                }
                
                // Load from database with join
                var table = await (from comp in _context.TB_Composizione
                                  join det in _context.TB_DettagliTavole 
                                      on comp.Tavola equals det.Tavola
                                  where comp.Tavola == tavola && det.Versione == versione && comp.TER == ter
                                  select new TableCompositionDetail
                                  {
                                      TER = comp.TER,
                                      Tavola = comp.Tavola,
                                      nTavola = comp.nTavola,
                                      Versione = det.Versione,
                                      CodiceTecnico = det.CodiceTecnico,
                                      Stato = det.Stato,
                                      Data = det.Data,
                                      Descrizione1IT = det.Descrizione1IT,
                                      Descrizione2IT = det.Descrizione2IT,
                                      Descrizione3IT = det.Descrizione3IT,
                                      DMC = det.DMC,
                                      SBC = det.SBC,
                                      SNS = det.SNS,
                                      Figura = det.Figura,
                                      Note = det.Note
                                  }).FirstOrDefaultAsync();
                
                if (table != null)
                {
                    var cacheOptions = new MemoryCacheEntryOptions
                    {
                        SlidingExpiration = _mediumCacheDuration,
                        Priority = CacheItemPriority.Normal
                    };
                    
                    _cache.Set(cacheKey, table, cacheOptions);
                    UpdateStatistics(cacheKey, false);
                    _logger.LogDebug("Table {Tavola} v{Version} loaded and cached", tavola, versione);
                }
                
                return table;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Get all versions of a table
        /// </summary>
        public async Task<List<TableCompositionDetail>> GetTableVersionsAsync(string tavola, string ter, bool forceRefresh = false)
        {
            var cacheKey = $"{VERSIONS_KEY_PREFIX}{ter}:{tavola}";
            
            if (!forceRefresh && _cache.TryGetValue<List<TableCompositionDetail>>(cacheKey, out var cachedVersions))
            {
                UpdateStatistics(cacheKey, true);
                return cachedVersions;
            }
            
            await _semaphore.WaitAsync();
            try
            {
                if (!forceRefresh && _cache.TryGetValue<List<TableCompositionDetail>>(cacheKey, out cachedVersions))
                {
                    UpdateStatistics(cacheKey, true);
                    return cachedVersions;
                }
                
                var versions = await (from comp in _context.TB_Composizione
                                     join det in _context.TB_DettagliTavole
                                         on comp.Tavola equals det.Tavola
                                     where comp.Tavola == tavola && comp.TER == ter
                                     orderby det.Versione descending
                                     select new TableCompositionDetail
                                     {
                                         TER = comp.TER,
                                         Tavola = comp.Tavola,
                                         nTavola = comp.nTavola,
                                         Versione = det.Versione,
                                         CodiceTecnico = det.CodiceTecnico,
                                         Stato = det.Stato,
                                         Data = det.Data,
                                         Descrizione1IT = det.Descrizione1IT,
                                         DMC = det.DMC,
                                         SBC = det.SBC,
                                         SNS = det.SNS
                                     }).ToListAsync();
                
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    SlidingExpiration = _mediumCacheDuration,
                    Priority = CacheItemPriority.Normal
                };
                
                _cache.Set(cacheKey, versions, cacheOptions);
                UpdateStatistics(cacheKey, false);
                
                return versions;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Get table detail view model with all related data
        /// </summary>
        public async Task<TableDetailViewModel> GetTableDetailAsync(string tavola, string versione, string ter, bool forceRefresh = false)
        {
            var cacheKey = $"{DETAIL_KEY_PREFIX}{ter}:{tavola}:{versione}";
            
            if (!forceRefresh && _cache.TryGetValue<TableDetailViewModel>(cacheKey, out var cachedDetail))
            {
                UpdateStatistics(cacheKey, true);
                return cachedDetail;
            }
            
            await _semaphore.WaitAsync();
            try
            {
                if (!forceRefresh && _cache.TryGetValue<TableDetailViewModel>(cacheKey, out cachedDetail))
                {
                    UpdateStatistics(cacheKey, true);
                    return cachedDetail;
                }
                
                // Load table data
                var table = await GetTableAsync(tavola, versione, ter, forceRefresh);
                if (table == null) return null;
                
                // Load catalog
                var catalog = await _context.TB_Cataloghi
                    .Where(c => c.TER == ter)
                    .FirstOrDefaultAsync();
                
                // Load all versions for dropdown
                var versions = await GetTableVersionsAsync(tavola, ter, forceRefresh);
                
                // Load related data (parts, etc.)
                var parts = await _context.TB_Parti
                    .Where(p => p.Tavola == tavola)
                    .ToListAsync();
                
                // Build view model
                var viewModel = new TableDetailViewModel
                {
                    Tavola = table.Tavola,
                    TER = table.TER,
                    CodiceTecnico = table.CodiceTecnico,
                    Versione = table.Versione,
                    Stato = table.Stato,
                    Descrizione1IT = table.Descrizione1IT,
                    Data = table.Data,
                    Parts = parts,
                    PartsCount = parts.Count
                };
                
                // Cache with appropriate duration based on status
                var cacheDuration = table.Stato == "00" ? _longCacheDuration : _shortCacheDuration;
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    SlidingExpiration = cacheDuration,
                    Priority = table.Stato == "00" ? CacheItemPriority.High : CacheItemPriority.Normal
                };
                
                _cache.Set(cacheKey, viewModel, cacheOptions);
                UpdateStatistics(cacheKey, false);
                
                return viewModel;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Get tables dashboard with pagination and statistics
        /// </summary>
        public async Task<TablesDashboardData> GetTablesDashboardAsync(string? ter, string? search, int page, int pageSize, bool forceRefresh = false)
        {
            var tables = await GetDashboardTablesAsync(ter ?? "", forceRefresh);
            
            // Apply search filter if provided
            if (!string.IsNullOrEmpty(search))
            {
                var searchLower = search.ToLowerInvariant();
                tables = tables.Where(t => 
                    t.Tavola.ToLowerInvariant().Contains(searchLower) ||
                    (t.CodiceTecnico ?? "").ToLowerInvariant().Contains(searchLower) ||
                    (t.Descrizione1IT ?? "").ToLowerInvariant().Contains(searchLower))
                    .ToList();
            }
            
            var totalCount = tables.Count;
            
            // Apply pagination
            var paginatedTables = tables
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
            
            // Calculate statistics
            var statistics = new TablesDashboardStatistics
            {
                TotalTables = totalCount,
                TablesWithMultipleVersions = tables.Count(t => t.HasMultipleVersions),
                TablesWithParts = tables.Count(t => t.HasParts),
                StatusDistribution = tables.GroupBy(t => t.CurrentVersionStatus)
                    .ToDictionary(g => g.Key, g => g.Count()),
                LastUpdate = tables.Max(t => t.LastUpdate)
            };
            
            return new TablesDashboardData
            {
                Tables = paginatedTables,
                TotalCount = totalCount,
                Statistics = statistics
            };
        }
        
        /// <summary>
        /// Get dashboard table list
        /// </summary>
        public async Task<List<TableDashboardItemModel>> GetDashboardTablesAsync(string ter, bool forceRefresh = false)
        {
            var cacheKey = $"{DASHBOARD_KEY_PREFIX}{ter}";
            
            if (!forceRefresh && _cache.TryGetValue<List<TableDashboardItemModel>>(cacheKey, out var cachedTables))
            {
                UpdateStatistics(cacheKey, true);
                return cachedTables;
            }
            
            await _semaphore.WaitAsync();
            try
            {
                if (!forceRefresh && _cache.TryGetValue<List<TableDashboardItemModel>>(cacheKey, out cachedTables))
                {
                    UpdateStatistics(cacheKey, true);
                    return cachedTables;
                }
                
                // Load latest version of each table with join
                var tables = await (from comp in _context.TB_Composizione
                                   join det in _context.TB_DettagliTavole
                                       on comp.Tavola equals det.Tavola
                                   where string.IsNullOrEmpty(ter) || comp.TER == ter
                                   group new { comp, det } by new { comp.Tavola, comp.TER } into g
                                   select g.OrderByDescending(x => x.det.Versione).First())
                    .Select(x => new TableDashboardItemModel
                    {
                        Tavola = x.comp.Tavola,
                        CatalogTER = x.comp.TER,
                        CatalogTitle = x.comp.TER, // Will be populated later
                        CodiceTecnico = x.det.CodiceTecnico,
                        Descrizione1IT = x.det.Descrizione1IT,
                        nTavola = x.comp.nTavola,
                        CurrentVersion = x.det.Versione,
                        CurrentVersionStatus = x.det.Stato,
                        LastUpdate = x.det.Data,
                        HasParts = false, // Will be populated later
                        PartsCount = 0 // Will be populated later
                    })
                    .ToListAsync();
                
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    SlidingExpiration = _shortCacheDuration,
                    Priority = CacheItemPriority.Normal
                };
                
                _cache.Set(cacheKey, tables, cacheOptions);
                UpdateStatistics(cacheKey, false);
                
                return tables;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Invalidate specific table cache
        /// </summary>
        public void InvalidateTable(string tavola, string ter)
        {
            _logger.LogInformation("Invalidating cache for table {Tavola} in catalog {TER}", tavola, ter);
            
            // Remove all versions of the table
            var pattern = $"{TABLE_KEY_PREFIX}{ter}:{tavola}:";
            RemoveByPattern(pattern);
            
            // Remove versions list
            _cache.Remove($"{VERSIONS_KEY_PREFIX}{ter}:{tavola}");
            
            // Remove detail views
            pattern = $"{DETAIL_KEY_PREFIX}{ter}:{tavola}:";
            RemoveByPattern(pattern);
            
            // Invalidate dashboard
            _cache.Remove($"{DASHBOARD_KEY_PREFIX}{ter}");
            _cache.Remove($"{DASHBOARD_KEY_PREFIX}"); // Also invalidate "all catalogs" view
        }

        /// <summary>
        /// Invalidate entire catalog cache
        /// </summary>
        public void InvalidateCatalog(string ter)
        {
            _logger.LogInformation("Invalidating cache for catalog {TER}", ter);
            
            var patterns = new[]
            {
                $"{TABLE_KEY_PREFIX}{ter}:",
                $"{VERSIONS_KEY_PREFIX}{ter}:",
                $"{DETAIL_KEY_PREFIX}{ter}:",
                $"{DASHBOARD_KEY_PREFIX}{ter}",
                $"{CATALOG_KEY_PREFIX}{ter}"
            };
            
            foreach (var pattern in patterns)
            {
                RemoveByPattern(pattern);
            }
            
            // Also invalidate "all catalogs" view
            _cache.Remove($"{DASHBOARD_KEY_PREFIX}");
        }

        /// <summary>
        /// Clear entire cache
        /// </summary>
        public void InvalidateAll()
        {
            _logger.LogWarning("Clearing entire cache");
            
            // Since IMemoryCache doesn't provide a Clear method,
            // we need to track keys or use a different approach
            // For now, we'll invalidate known patterns
            
            var allPatterns = new[]
            {
                TABLE_KEY_PREFIX,
                VERSIONS_KEY_PREFIX,
                DETAIL_KEY_PREFIX,
                DASHBOARD_KEY_PREFIX,
                CATALOG_KEY_PREFIX
            };
            
            foreach (var pattern in allPatterns)
            {
                RemoveByPattern(pattern);
            }
            
            _lastAccess.Clear();
            _hitCount.Clear();
        }

        /// <summary>
        /// Preload catalog data for better performance
        /// </summary>
        public async Task PreloadCatalogAsync(string ter)
        {
            _logger.LogInformation("Preloading cache for catalog {TER}", ter);
            
            try
            {
                // Load dashboard tables
                await GetDashboardTablesAsync(ter, true);
                
                // Load most recent tables (top 20) with join
                var recentTables = await (from comp in _context.TB_Composizione
                                         join det in _context.TB_DettagliTavole
                                             on comp.Tavola equals det.Tavola
                                         where comp.TER == ter
                                         orderby det.Data descending
                                         select new TableCompositionDetail
                                         {
                                             TER = comp.TER,
                                             Tavola = comp.Tavola,
                                             nTavola = comp.nTavola,
                                             Versione = det.Versione,
                                             CodiceTecnico = det.CodiceTecnico,
                                             Stato = det.Stato,
                                             Data = det.Data,
                                             Descrizione1IT = det.Descrizione1IT
                                         })
                    .Take(20)
                    .ToListAsync();
                
                foreach (var table in recentTables)
                {
                    var cacheKey = $"{TABLE_KEY_PREFIX}{ter}:{table.Tavola}:{table.Versione}";
                    var cacheOptions = new MemoryCacheEntryOptions
                    {
                        SlidingExpiration = _longCacheDuration,
                        Priority = CacheItemPriority.Low
                    };
                    _cache.Set(cacheKey, table, cacheOptions);
                }
                
                _logger.LogInformation("Preloaded {Count} tables for catalog {TER}", recentTables.Count, ter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error preloading catalog {TER}", ter);
            }
        }

        /// <summary>
        /// Get cache statistics for monitoring
        /// </summary>
        public CacheStatistics GetCacheStatistics()
        {
            var totalHits = _hitCount.Values.Where(v => v > 0).Sum();
            var totalMisses = _hitCount.Values.Where(v => v == 0).Count();
            var hitRate = totalHits + totalMisses > 0 
                ? (double)totalHits / (totalHits + totalMisses) * 100 
                : 0;
            
            return new CacheStatistics
            {
                TotalHits = totalHits,
                TotalMisses = totalMisses,
                HitRate = hitRate,
                CachedItemsCount = _lastAccess.Count,
                MostAccessedKeys = _hitCount
                    .OrderByDescending(kvp => kvp.Value)
                    .Take(10)
                    .Select(kvp => new CacheItemInfo
                    {
                        Key = kvp.Key,
                        HitCount = kvp.Value,
                        LastAccess = _lastAccess.ContainsKey(kvp.Key) ? _lastAccess[kvp.Key] : DateTime.MinValue
                    })
                    .ToList()
            };
        }

        #region Helper Methods

        private string GetStatusText(string stato)
        {
            return stato switch
            {
                "00" => "Approvata",
                "01" => "In Revisione",
                "02" => "Bozza",
                "03" => "Problemi",
                _ => "N/A"
            };
        }

        private void UpdateStatistics(string key, bool isHit)
        {
            _lastAccess[key] = DateTime.Now;
            
            if (!_hitCount.ContainsKey(key))
                _hitCount[key] = 0;
            
            if (isHit)
                _hitCount[key]++;
        }

        private void RemoveByPattern(string pattern)
        {
            // Since IMemoryCache doesn't support pattern removal directly,
            // we need to track keys or use reflection. For now, we'll
            // remove known keys based on our naming convention
            
            var keysToRemove = _lastAccess.Keys
                .Where(k => k.StartsWith(pattern))
                .ToList();
            
            foreach (var key in keysToRemove)
            {
                _cache.Remove(key);
                _lastAccess.Remove(key);
                _hitCount.Remove(key);
            }
        }

        #endregion
    }

    public class CacheStatistics
    {
        public int TotalHits { get; set; }
        public int TotalMisses { get; set; }
        public double HitRate { get; set; }
        public int CachedItemsCount { get; set; }
        public List<CacheItemInfo> MostAccessedKeys { get; set; }
    }

    public class CacheItemInfo
    {
        public string Key { get; set; }
        public int HitCount { get; set; }
        public DateTime LastAccess { get; set; }
    }

    // View model for table detail
    public class TableDetailViewModel
    {
        public string Tavola { get; set; }
        public string TER { get; set; }
        public string CodiceTecnico { get; set; }
        public string Versione { get; set; }
        public string Stato { get; set; }
        public string Descrizione1IT { get; set; }
        public DateTime? Data { get; set; }
        public List<TB_Parti> Parts { get; set; }
        public int PartsCount { get; set; }
    }
    
    // Combined model for TB_Composizione and TB_DettagliTavole
    public class TableCompositionDetail
    {
        // From TB_Composizione
        public string TER { get; set; }
        public string Tavola { get; set; }
        public double? nTavola { get; set; }
        
        // From TB_DettagliTavole
        public string Versione { get; set; }
        public string CodiceTecnico { get; set; }
        public string Stato { get; set; }
        public DateTime? Data { get; set; }
        public string Descrizione1IT { get; set; }
        public string Descrizione2IT { get; set; }
        public string Descrizione3IT { get; set; }
        public string DMC { get; set; }
        public string SBC { get; set; }
        public string SNS { get; set; }
        public string Figura { get; set; }
        public string Note { get; set; }
    }
    
    // Dashboard data with statistics
    public class TablesDashboardData
    {
        public List<TableDashboardItemModel> Tables { get; set; } = new();
        public int TotalCount { get; set; }
        public TablesDashboardStatistics Statistics { get; set; } = new();
    }
}
