using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Models.Tables;

namespace Ardec.Services.Web.Services.Tables;

/// <summary>
/// Table Service Interface - Complete implementation based on Framework TableService
/// Manages TB_Tavole and TB_DettagliTavole with versioning support
/// Handles table-catalog relationships through TB_Composizione
/// </summary>
public interface ITableService
{
    // TB_Tavole CRUD operations
    Task<IEnumerable<TB_Tavole>> GetAllTablesAsync();
    Task<IEnumerable<TB_Tavole>> GetTablesLazyAsync();
    Task<TB_Tavole?> GetTableByTavolaAsync(string tavola);
    Task<IEnumerable<TB_Tavole>> GetTablesByTavolaAsync(string tavola);
    Task<TB_Tavole> CreateTableAsync(TB_Tavole table);
    Task<TB_Tavole> UpdateTableAsync(TB_Tavole table);
    Task<bool> DeleteTableAsync(string tavola);
    Task<bool> TableExistsAsync(string tavola);
    Task<int> GetTablesCountAsync();
    
    // TB_DettagliTavole CRUD operations
    Task<IEnumerable<TB_DettagliTavole>> GetAllTableDetailsAsync();
    Task<IEnumerable<TB_DettagliTavole>> GetTableDetailsLazyAsync();
    Task<IEnumerable<TB_DettagliTavole>> GetTableDetailsByTavolaAsync(string tavola);
    Task<IEnumerable<TB_DettagliTavole>> GetTableDetailsByTavolaAndVersionAsync(string tavola, string versione);
    Task<IEnumerable<TB_DettagliTavole>> GetLastTableDetailsByTavolaAsync(string tavola);
    Task<TB_DettagliTavole?> GetTableDetailsAsync(string tavola, string versione);
    Task<TB_DettagliTavole> CreateTableDetailsAsync(TB_DettagliTavole details);
    Task<TB_DettagliTavole> UpdateTableDetailsAsync(TB_DettagliTavole details);
    Task<bool> DeleteTableDetailsAsync(string tavola, string versione);
    Task<bool> TableDetailsExistsAsync(string tavola, string versione);
    
    // Tables by TER (through TB_Composizione) - Core Framework functionality
    Task<IEnumerable<TB_Tavole>> GetTablesByTerAsync(string ter, bool orderByTavola = true);
    Task<IEnumerable<TB_DettagliTavole>> GetAllTableDetailsByTerAsync(string ter, bool orderByTavola = true);
    Task<IEnumerable<TB_DettagliTavole>> GetLastTableDetailsByTerAsync(string ter, bool orderByTavola = true);
    Task<int> GetTablesCountByTerAsync(string ter);
    
    // Status management (00=Verde, 01=Giallo, 02=Blu, 03=Rosso)
    Task SetTableStatusAsync(string tavola, string versione, string stato);
    
    // Versioning - Core Framework feature
    Task CreateNewTableVersionAsync(string tavola, string oldVersione, string newVersione, DateTime newData, string newStatus);
    
    // Multilingual descriptions support
    Task<IEnumerable<string>> GetDistinctTavoleAsync();
    Task<IEnumerable<string>> GetAllItalianDescriptions1Async();
    Task<IEnumerable<string>> GetAllItalianDescriptions2Async();
    Task<IEnumerable<string>> GetAllItalianDescriptions3Async();
    Task<IEnumerable<string>> GetAllEnglishDescriptions1Async();
    Task<IEnumerable<string>> GetAllEnglishDescriptions2Async();
    Task<IEnumerable<string>> GetAllEnglishDescriptions3Async();
    Task<IEnumerable<string>> GetAllSpanishDescriptions1Async();
    Task<IEnumerable<string>> GetAllSpanishDescriptions2Async();
    Task<IEnumerable<string>> GetAllSpanishDescriptions3Async();
    Task<IEnumerable<string>> GetAllPortugueseDescriptions1Async();
    Task<IEnumerable<string>> GetAllPortugueseDescriptions2Async();
    Task<IEnumerable<string>> GetAllPortugueseDescriptions3Async();
    Task<IEnumerable<string>> GetAllFrenchDescriptions1Async();
    Task<IEnumerable<string>> GetAllFrenchDescriptions2Async();
    Task<IEnumerable<string>> GetAllFrenchDescriptions3Async();
    Task<IEnumerable<string>> GetAllGermanDescriptions1Async();
    Task<IEnumerable<string>> GetAllGermanDescriptions2Async();
    Task<IEnumerable<string>> GetAllGermanDescriptions3Async();
    Task<IEnumerable<string>> GetAllUSADescriptions1Async();
    Task<IEnumerable<string>> GetAllUSADescriptions2Async();
    Task<IEnumerable<string>> GetAllUSADescriptions3Async();
    
    // Search and filters
    Task<IEnumerable<TB_Tavole>> SearchTablesAsync(string searchTerm);
    Task<IEnumerable<TB_DettagliTavole>> SearchTableDetailsAsync(string searchTerm);
    
    // Advanced operations
    Task<bool> CheckImagePathAsync(string imagePath);
    Task<IEnumerable<TB_DettagliTavole>> GetTableDetailsByDateRangeAsync(DateTime? startDate, DateTime? endDate);
    Task<object> GetTableStatisticsAsync();
    Task<object> GetTableStatisticsByTerAsync(string ter);
    
    // Tables Dashboard (Desktop-style)
    Task<TablesDashboardResult> GetTablesDashboardAsync(string? ter = null, string? search = null, int page = 1, int pageSize = 50);
}
