using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Models.Tables;
using Microsoft.EntityFrameworkCore;

namespace Ardec.Services.Web.Services.Tables;

/// <summary>
/// TableService - Dashboard functionality (Desktop-style tables cruscotto)
/// Handles tables dashboard with version matrix and status information
/// </summary>
public partial class TableService
{
    #region Desktop-style Dashboard

    /// <summary>
    /// Get tables dashboard data - Desktop app style
    /// Shows all tables with version matrix, status, and CID information
    /// </summary>
    /// <param name="ter">Optional catalog filter</param>
    /// <param name="search">Optional search term</param>
    /// <param name="page">Page number (1-based)</param>
    /// <param name="pageSize">Items per page</param>
    /// <returns>Dashboard data with statistics</returns>
    public async Task<TablesDashboardResult> GetTablesDashboardAsync(string? ter = null, string? search = null, int page = 1, int pageSize = 50)
    {
        try
        {
            _logger.LogInformation("Loading tables dashboard: ter={TER}, search={Search}, page={Page}", ter, search, page);

            // Base query - all tables through composition
            var baseQuery = from dt in _context.TB_DettagliTavole
                            join c in _context.TB_Composizione on dt.Tavola equals c.Tavola
                            join cat in _context.TB_Cataloghi on c.TER equals cat.TER
                            select new { dt, c, cat };

            // Apply catalog filter
            if (!string.IsNullOrEmpty(ter))
            {
                baseQuery = baseQuery.Where(x => x.c.TER == ter);
            }

            // Apply search filter (case-insensitive, SQL compatible)
            if (!string.IsNullOrEmpty(search))
            {
                var searchPattern = $"%{search}%";
                baseQuery = baseQuery.Where(x =>
                    EF.Functions.Like(x.dt.Tavola, searchPattern) ||
                    (x.dt.CodiceTecnico != null && EF.Functions.Like(x.dt.CodiceTecnico, searchPattern)) ||
                    (x.dt.Descrizione1IT != null && EF.Functions.Like(x.dt.Descrizione1IT, searchPattern)));
            }

            // Materialize rows first (avoid non-translatable GroupBy + ToList in EF Core)
            var rows = await baseQuery
                .Select(x => new
                {
                    x.dt.Tavola,
                    x.dt.Versione,
                    x.dt.Stato,
                    x.dt.Data,
                    x.dt.DataModificaTavola,
                    TER = x.c.TER,
                    TitoloBreve = x.cat.TitoloBreve,
                    nTavola = x.c.nTavola,
                    CodiceTecnico = x.dt.CodiceTecnico,
                    Descrizione1IT = x.dt.Descrizione1IT
                })
                .ToListAsync();

            // Group in-memory by table
            var grouped = rows
                .GroupBy(r => new { r.Tavola, r.TER, r.TitoloBreve, r.nTavola })
                .Select(g => new
                {
                    g.Key.Tavola,
                    g.Key.TER,
                    CatalogTitle = g.Key.TitoloBreve ?? g.Key.TER,
                    g.Key.nTavola,
                    Latest = g.OrderByDescending(r => r.Versione).First(),
                    Versions = g.OrderByDescending(r => r.Versione).ToList()
                })
                .OrderBy(x => x.nTavola)
                .ThenBy(x => x.Tavola)
                .ToList();

            // Get total count before pagination
            var totalCount = grouped.Count;

            // Apply pagination
            var pagedTables = grouped
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

     
            // Build dashboard items
            var dashboardItems = new List<TableDashboardItemModel>();

            foreach (var tableGroup in pagedTables)
            {
                // Fetch minimal parts data and compute aggregates in-memory to avoid complex SQL translations
                var partsFlat = await _context.TB_Parti
                    .Where(p => p.Tavola == tableGroup.Tavola)
                    .Select(p => new { p.Tavola, p.CID })
                    .ToListAsync();

                var partsLookup = partsFlat
                    .GroupBy(x => x.Tavola)
                    .ToDictionary(
                        g => g.Key,
                        g => new
                        {
                            PartsCount = g.Count(),
                            AssociatedCIDs = g.Select(x => x.CID)
                                              .Where(cid => !string.IsNullOrEmpty(cid))
                                              .Distinct()
                                              .ToList()
                        }
                    );

                // Get parts data from lookup (no additional queries)
                partsLookup.TryGetValue(tableGroup.Tavola, out var partData);
                var partsCount = partData?.PartsCount ?? 0;
                var associatedCIDs = partData?.AssociatedCIDs ?? new List<string>();

                // Build version info list
                var versionInfos = tableGroup.Versions
                    .Select(v => new TableVersionInfo
                    {
                        Versione = v.Versione ?? string.Empty,
                        Stato = v.Stato ?? "00",
                        Data = v.Data,
                        LastModified = TryParseNullableDate(v.DataModificaTavola),
                        PartsCount = 0,
                        IsCurrentVersion = v.Versione == tableGroup.Latest.Versione
                    })
                    .OrderByDescending(v => v.Versione)
                    .ToList();

                // Create dashboard item
                var dashboardItem = new TableDashboardItemModel
                {
                    Tavola = tableGroup.Tavola,
                    CatalogTER = tableGroup.TER,
                    CatalogTitle = tableGroup.CatalogTitle,
                    CodiceTecnico = tableGroup.Latest.CodiceTecnico,
                    Descrizione1IT = tableGroup.Latest.Descrizione1IT,
                    nTavola = tableGroup.nTavola,
                    CurrentVersion = tableGroup.Latest.Versione ?? string.Empty,
                    CurrentVersionStatus = tableGroup.Latest.Stato ?? "00",
                    LastUpdate = TryParseNullableDate(tableGroup.Latest.DataModificaTavola) ?? tableGroup.Latest.Data,
                    HasParts = partsCount > 0,
                    PartsCount = partsCount,
                    AvailableVersions = versionInfos,
                    AssociatedCIDs = associatedCIDs.Where(cid => !string.IsNullOrEmpty(cid)).ToList()
                };

                dashboardItems.Add(dashboardItem);
            }

            // Calculate statistics
            var statistics = await CalculateDashboardStatisticsAsync(ter);

            return new TablesDashboardResult
            {
                Tables = dashboardItems,
                TotalCount = totalCount,
                Statistics = statistics
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading tables dashboard: ter={TER}, search={Search}", ter, search);
            throw;
        }
    }

    /// <summary>
    /// Calculate dashboard statistics
    /// </summary>
    private async Task<TablesDashboardStatistics> CalculateDashboardStatisticsAsync(string? ter = null)
    {
        try
        {
            var query = _context.TB_DettagliTavole.AsQueryable();

            // Apply catalog filter if provided
            if (!string.IsNullOrEmpty(ter))
            {
                query = from dt in query
                        join c in _context.TB_Composizione on dt.Tavola equals c.Tavola
                        where c.TER == ter
                        select dt;
            }

            // Get latest versions only (per table)
            var latestVersions = await query
                .GroupBy(dt => dt.Tavola)
                .Select(g => g.OrderByDescending(dt => dt.Versione).First())
                .ToListAsync();

            var totalTables = latestVersions.Count;

            // Status distribution
            var statusDistribution = latestVersions
                .GroupBy(dt => dt.Stato ?? "00")
                .ToDictionary(g => g.Key, g => g.Count());

            // Tables with multiple versions
            var tablesWithMultipleVersions = await query
                .GroupBy(dt => dt.Tavola)
                .Where(g => g.Count() > 1)
                .CountAsync();

            // Tables with parts
            var tablesWithParts = await _context.TB_Parti
                .Select(p => p.Tavola)
                .Distinct()
                .CountAsync();

            // Get catalog count
            var catalogCount = string.IsNullOrEmpty(ter) ?
                await _context.TB_Cataloghi.CountAsync() : 1;

            // Last update
            var lastUpdate = latestVersions
                .Select(dt => (dt.DataModificaTavola != null ? ParseStringToDateTime(dt.DataModificaTavola.ToString()) : null) ??
                             (dt.Data != null ? ParseStringToDateTime(dt.Data.ToString()) : null))
                .Where(date => date.HasValue)
                .DefaultIfEmpty()
                .Max();

            return new TablesDashboardStatistics
            {
                TotalTables = totalTables,
                TotalCatalogs = catalogCount,
                StatusDistribution = statusDistribution,
                TablesWithMultipleVersions = tablesWithMultipleVersions,
                TablesWithParts = tablesWithParts,
                LastUpdate = lastUpdate
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating dashboard statistics");
            throw;
        }
    }

    /// <summary>
    /// Parse string date to DateTime (handles various formats from database)
    /// </summary>
    private static DateTime? ParseStringToDateTime(string? dateString)
    {
        if (string.IsNullOrWhiteSpace(dateString))
            return null;

        // Try various date formats that might be in the database
        var formats = new[]
        {
            "yyyy-MM-dd",
            "yyyy-MM-dd HH:mm:ss",
            "dd/MM/yyyy",
            "dd/MM/yyyy HH:mm:ss",
            "MM/dd/yyyy",
            "MM/dd/yyyy HH:mm:ss"
        };

        foreach (var format in formats)
        {
            if (DateTime.TryParseExact(dateString, format, null, System.Globalization.DateTimeStyles.None, out var result))
            {
                return result;
            }
        }

        // Fallback to general parsing
        if (DateTime.TryParse(dateString, out var fallbackResult))
        {
            return fallbackResult;
        }

        return null;
    }

    /// <summary>
    /// Try parse nullable string date commonly stored in string columns
    /// </summary>
    private static DateTime? TryParseNullableDate(string? s)
    {
        if (string.IsNullOrWhiteSpace(s)) return null;
        if (DateTime.TryParse(s, out var dt)) return dt;
        return null;
    }

    #endregion
}
