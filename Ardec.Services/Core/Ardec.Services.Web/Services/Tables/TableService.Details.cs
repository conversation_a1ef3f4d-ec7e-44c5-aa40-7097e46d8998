using Ardec.Services.Web.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace Ardec.Services.Web.Services.Tables;

/// <summary>
/// TableService partial class - TB_DettagliTavole Operations
/// Handles all table details operations including versioning and multilingual descriptions
/// </summary>
public partial class TableService
{
    #region TB_DettagliTavole Basic Operations

    public async Task<IEnumerable<TB_DettagliTavole>> GetAllTableDetailsAsync()
    {
        try
        {
            return await _context.TB_DettagliTavole
                .OrderBy(d => d.Tavola)
                .ThenBy(d => d.Versione)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all table details");
            throw;
        }
    }

    public async Task<IEnumerable<TB_DettagliTavole>> GetTableDetailsLazyAsync()
    {
        try
        {
            return await _context.TB_DettagliTavole
                .AsNoTracking()
                .OrderBy(d => d.Tavola)
                .ThenBy(d => d.Versione)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving table details (lazy)");
            throw;
        }
    }

    public async Task<IEnumerable<TB_DettagliTavole>> GetTableDetailsByTavolaAsync(string tavola)
    {
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));

        try
        {
            return await _context.TB_DettagliTavole
                .Where(d => d.Tavola == tavola)
                .OrderBy(d => d.Versione)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving table details by tavola: {Tavola}", tavola);
            throw;
        }
    }

    public async Task<IEnumerable<TB_DettagliTavole>> GetTableDetailsByTavolaAndVersionAsync(string tavola, string versione)
    {
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));
        if (string.IsNullOrWhiteSpace(versione))
            throw new ArgumentException("Versione cannot be null or empty", nameof(versione));

        try
        {
            return await _context.TB_DettagliTavole
                .Where(d => d.Tavola == tavola && d.Versione == versione)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving table details by tavola and version: {Tavola}, {Versione}", tavola, versione);
            throw;
        }
    }

    public async Task<IEnumerable<TB_DettagliTavole>> GetLastTableDetailsByTavolaAsync(string tavola)
    {
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));

        try
        {
            var maxVersion = await _context.TB_DettagliTavole
                .Where(d => d.Tavola == tavola)
                .MaxAsync(d => d.Versione);

            return await _context.TB_DettagliTavole
                .Where(d => d.Tavola == tavola && d.Versione == maxVersion)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving last table details by tavola: {Tavola}", tavola);
            throw;
        }
    }

    public async Task<TB_DettagliTavole?> GetTableDetailsAsync(string tavola, string versione)
    {
        return await _context.TB_DettagliTavole
            .FirstOrDefaultAsync(d => d.Tavola == tavola && d.Versione == versione);
    }

    public async Task<TB_DettagliTavole> CreateTableDetailsAsync(TB_DettagliTavole details)
    {
        _context.TB_DettagliTavole.Add(details);
        await _context.SaveChangesAsync();
        return details;
    }

    public async Task<TB_DettagliTavole> UpdateTableDetailsAsync(TB_DettagliTavole details)
    {
        _context.TB_DettagliTavole.Update(details);
        await _context.SaveChangesAsync();
        return details;
    }

    public async Task<bool> DeleteTableDetailsAsync(string tavola, string versione)
    {
        var details = await GetTableDetailsAsync(tavola, versione);
        if (details != null)
        {
            _context.TB_DettagliTavole.Remove(details);
            await _context.SaveChangesAsync();
            return true;
        }
        return false;
    }

    public async Task<bool> TableDetailsExistsAsync(string tavola, string versione)
    {
        return await _context.TB_DettagliTavole
            .AnyAsync(d => d.Tavola == tavola && d.Versione == versione);
    }

    #endregion

    #region TB_DettagliTavole TER Operations

    public async Task<IEnumerable<TB_DettagliTavole>> GetLastTableDetailsByTerAsync(string ter, bool orderByTavola = true)
    {
        if (string.IsNullOrWhiteSpace(ter))
            return await GetTableDetailsLazyAsync();

        try
        {
            var tables = await GetTablesByTerAsync(ter);
            var tableNames = tables.Select(t => t.Tavola).ToList();
            
            var tableDetails = await _context.TB_DettagliTavole
                .Where(d => tableNames.Contains(d.Tavola))
                .ToListAsync();

            // Get last version for each table
            var lastVersions = tableDetails
                .GroupBy(d => d.Tavola)
                .Select(g => g.OrderByDescending(d => d.Versione).First())
                .ToList();

            return orderByTavola 
                ? lastVersions.OrderBy(d => d.Tavola)
                : lastVersions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving last table details by TER: {TER}", ter);
            throw;
        }
    }

    public async Task<IEnumerable<TB_DettagliTavole>> GetAllTableDetailsByTerAsync(string ter, bool orderByTavola = true)
    {
        var tables = await GetTablesByTerAsync(ter);
        var tableNames = tables.Select(t => t.Tavola);
        
        var query = _context.TB_DettagliTavole.Where(d => tableNames.Contains(d.Tavola));
        return orderByTavola 
            ? await query.OrderBy(d => d.Tavola).ToListAsync()
            : await query.ToListAsync();
    }

    #endregion

    #region Versioning and Status Management

    public async Task SetTableStatusAsync(string tavola, string versione, string stato)
    {
        var detail = await GetTableDetailsAsync(tavola, versione);
        if (detail != null)
        {
            detail.Stato = stato;
            await _context.SaveChangesAsync();
            _logger.LogInformation("Updated table status: {Tavola} v{Versione} -> {Stato}", tavola, versione, stato);
        }
    }

    public async Task CreateNewTableVersionAsync(string tavola, string oldVersione, string newVersione, DateTime newData, string newStatus)
    {
        try
        {
            var oldDetails = await GetTableDetailsAsync(tavola, oldVersione);
            if (oldDetails == null)
                throw new InvalidOperationException($"Old version not found: {tavola} v{oldVersione}");

            // Create new TB_DettagliTavole
            var newDetails = new TB_DettagliTavole
            {
                Tavola = oldDetails.Tavola,
                Versione = newVersione,
                Data = newData,
                Stato = newStatus,
                NonForniti = oldDetails.NonForniti,
                Descrizione1IT = oldDetails.Descrizione1IT,
                Descrizione2IT = oldDetails.Descrizione2IT,
                Descrizione3IT = oldDetails.Descrizione3IT,
                Logo = oldDetails.Logo,
                Figura = oldDetails.Figura,
                Note = oldDetails.Note
            };

            await CreateTableDetailsAsync(newDetails);

            // Copy TB_Parti with new version (if exists)
            var oldParts = await _context.TB_Parti
                .Where(p => p.Tavola == tavola && p.Versione == oldVersione)
                .ToListAsync();

            foreach (var part in oldParts)
            {
                var newPart = new TB_Parti
                {
                    Tavola = part.Tavola,
                    PART = part.PART,
                    ITEM = part.ITEM,
                    Versione = newVersione,
                    CID = part.CID, // Maintain original CID
                    QTAV = part.QTAV,
                    CodModifica = part.CodModifica
                };
                
                _context.TB_Parti.Add(newPart);
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Created new table version: {Tavola} from v{OldVer} to v{NewVer}", tavola, oldVersione, newVersione);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating new table version");
            throw;
        }
    }

    #endregion

    #region Search and Statistics

    public async Task<IEnumerable<TB_DettagliTavole>> SearchTableDetailsAsync(string searchTerm)
    {
        return await _context.TB_DettagliTavole
            .Where(d => EF.Functions.Like(d.Tavola, $"%{searchTerm}%") ||
                       EF.Functions.Like(d.Descrizione1IT ?? "", $"%{searchTerm}%"))
            .ToListAsync();
    }

    public async Task<IEnumerable<TB_DettagliTavole>> GetTableDetailsByDateRangeAsync(DateTime? startDate, DateTime? endDate)
    {
        var query = _context.TB_DettagliTavole.AsQueryable();
        
        if (startDate.HasValue)
            query = query.Where(d => d.Data >= startDate.Value);
            
        if (endDate.HasValue)
            query = query.Where(d => d.Data <= endDate.Value);
            
        return await query.ToListAsync();
    }

    public async Task<IEnumerable<string>> GetDistinctTavoleAsync()
    {
        return await _context.TB_DettagliTavole
            .Select(d => d.Tavola)
            .Distinct()
            .ToListAsync();
    }

    public async Task<bool> CheckImagePathAsync(string imagePath)
    {
        // Framework implementation for image path checking
        // Simplified version - would need actual file system integration
        return await Task.FromResult(!string.IsNullOrEmpty(imagePath));
    }

    public async Task<object> GetTableStatisticsAsync()
    {
        var totalTables = await GetTablesCountAsync();
        var totalDetails = await _context.TB_DettagliTavole.CountAsync();
        
        return new
        {
            TotalTables = totalTables,
            TotalVersions = totalDetails,
            AverageVersionsPerTable = totalTables > 0 ? (double)totalDetails / totalTables : 0
        };
    }

    public async Task<object> GetTableStatisticsByTerAsync(string ter)
    {
        var tablesCount = await GetTablesCountByTerAsync(ter);
        var tables = await GetTablesByTerAsync(ter);
        var tableNames = tables.Select(t => t.Tavola);
        var detailsCount = await _context.TB_DettagliTavole
            .CountAsync(d => tableNames.Contains(d.Tavola));
            
        return new
        {
            TER = ter,
            TablesCount = tablesCount,
            TotalVersions = detailsCount,
            AverageVersionsPerTable = tablesCount > 0 ? (double)detailsCount / tablesCount : 0
        };
    }

    #endregion
}
