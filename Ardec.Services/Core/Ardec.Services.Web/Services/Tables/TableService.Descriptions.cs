using Ardec.Services.Web.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace Ardec.Services.Web.Services.Tables;

/// <summary>
/// TableService partial class - Multilingual Descriptions Operations  
/// Handles all multilingual description queries for TB_DettagliTavole
/// </summary>
public partial class TableService
{
    #region Italian Descriptions

    public async Task<IEnumerable<string>> GetAllItalianDescriptions1Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione1IT))
            .Select(d => d.Descrizione1IT!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllItalianDescriptions2Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione2IT))
            .Select(d => d.Descrizione2IT!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllItalianDescriptions3Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione3IT))
            .Select(d => d.Descrizione3IT!)
            .Distinct()
            .ToListAsync();
    }

    #endregion

    #region English Descriptions

    public async Task<IEnumerable<string>> GetAllEnglishDescriptions1Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione1EN))
            .Select(d => d.Descrizione1EN!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllEnglishDescriptions2Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione2EN))
            .Select(d => d.Descrizione2EN!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllEnglishDescriptions3Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione3EN))
            .Select(d => d.Descrizione3EN!)
            .Distinct()
            .ToListAsync();
    }

    #endregion

    #region Spanish Descriptions

    public async Task<IEnumerable<string>> GetAllSpanishDescriptions1Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione1ES))
            .Select(d => d.Descrizione1ES!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllSpanishDescriptions2Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione2ES))
            .Select(d => d.Descrizione2ES!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllSpanishDescriptions3Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione3ES))
            .Select(d => d.Descrizione3ES!)
            .Distinct()
            .ToListAsync();
    }

    #endregion

    #region Portuguese Descriptions

    public async Task<IEnumerable<string>> GetAllPortugueseDescriptions1Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione1PT))
            .Select(d => d.Descrizione1PT!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllPortugueseDescriptions2Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione2PT))
            .Select(d => d.Descrizione2PT!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllPortugueseDescriptions3Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione3PT))
            .Select(d => d.Descrizione3PT!)
            .Distinct()
            .ToListAsync();
    }

    #endregion

    #region French Descriptions

    public async Task<IEnumerable<string>> GetAllFrenchDescriptions1Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione1FR))
            .Select(d => d.Descrizione1FR!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllFrenchDescriptions2Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione2FR))
            .Select(d => d.Descrizione2FR!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllFrenchDescriptions3Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione3FR))
            .Select(d => d.Descrizione3FR!)
            .Distinct()
            .ToListAsync();
    }

    #endregion

    #region German Descriptions

    public async Task<IEnumerable<string>> GetAllGermanDescriptions1Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione1TED))
            .Select(d => d.Descrizione1TED!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllGermanDescriptions2Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione2TED))
            .Select(d => d.Descrizione2TED!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllGermanDescriptions3Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione3TED))
            .Select(d => d.Descrizione3TED!)
            .Distinct()
            .ToListAsync();
    }

    #endregion

    #region USA Descriptions

    public async Task<IEnumerable<string>> GetAllUSADescriptions1Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione1USA))
            .Select(d => d.Descrizione1USA!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllUSADescriptions2Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione2USA))
            .Select(d => d.Descrizione2USA!)
            .Distinct()
            .ToListAsync();
    }

    public async Task<IEnumerable<string>> GetAllUSADescriptions3Async()
    {
        return await _context.TB_DettagliTavole
            .Where(d => !string.IsNullOrEmpty(d.Descrizione3USA))
            .Select(d => d.Descrizione3USA!)
            .Distinct()
            .ToListAsync();
    }

    #endregion

    #region Helper Methods

    public async Task<IEnumerable<TB_Tavole>> SearchTablesAsync(string searchTerm)
    {
        return await _context.TB_Tavole
            .Where(t => EF.Functions.Like(t.Tavola, $"%{searchTerm}%") ||
                       EF.Functions.Like(t.TECHNAME ?? "", $"%{searchTerm}%"))
            .ToListAsync();
    }

    public async Task<int> GetTablesCountByTerAsync(string ter)
    {
        var tables = await GetTablesByTerAsync(ter);
        return tables.Count();
    }

    #endregion
}
