using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Models.Tables;
using Microsoft.EntityFrameworkCore;
using ClosedXML.Excel;

namespace Ardec.Services.Web.Services.Tables
{
    public interface ITableExportService
    {
        Task<byte[]> ExportToDataModuleXmlAsync(string tavola, string versione, string ter);
        Task<byte[]> ExportToExcelAsync(string tavola, string versione, string ter);
        Task<byte[]> ExportMultipleTablesToExcelAsync(List<string> tavole, string ter);
        Task<byte[]> ExportCatalogToExcelAsync(string ter);
    }

    public class TableExportService : ITableExportService
    {
        private readonly ArdecDbContext _context;
        private readonly ILogger<TableExportService> _logger;

        public TableExportService(ArdecDbContext context, ILogger<TableExportService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Export single table to S1000D Data Module XML format
        /// </summary>
        public async Task<byte[]> ExportToDataModuleXmlAsync(string tavola, string versione, string ter)
        {
            try
            {
                // Get table data with join
                var tableData = await (from comp in _context.TB_Composizione
                                      join det in _context.TB_DettagliTavole
                                          on comp.Tavola equals det.Tavola
                                      where comp.Tavola == tavola && comp.TER == ter && det.Versione == versione
                                      select new { comp, det })
                                      .FirstOrDefaultAsync();

                if (tableData == null)
                {
                    throw new ArgumentException($"Table {tavola} version {versione} not found");
                }

                // Create S1000D XML structure
                var ns = "http://www.s1000d.org/S1000D_5-0/xml_schema_flat/descript.xsd";
                var xDoc = new XDocument(
                    new XDeclaration("1.0", "UTF-8", "yes"),
                    new XElement(XName.Get("dmodule", ns),
                        new XAttribute("xmlns", ns),
                        new XAttribute(XNamespace.Xmlns + "xsi", "http://www.w3.org/2001/XMLSchema-instance"),
                        
                        // Identification and status section
                        new XElement("identAndStatusSection",
                            new XElement("dmAddress",
                                new XElement("dmIdent",
                                    new XElement("dmCode", 
                                        new XAttribute("modelIdentCode", ter ?? ""),
                                        new XAttribute("systemDiffCode", tableData?.det.SBC ?? "A"),
                                        new XAttribute("systemCode", tableData?.det.SNS ?? "00"),
                                        new XAttribute("subSystemCode", "0"),
                                        new XAttribute("subSubSystemCode", "0"),
                                        new XAttribute("assyCode", "00"),
                                        new XAttribute("disassyCode", "00"),
                                        new XAttribute("disassyCodeVariant", "A"),
                                        new XAttribute("infoCode", tableData?.comp.Tavola ?? "000"),
                                        new XAttribute("infoCodeVariant", "A"),
                                        new XAttribute("itemLocationCode", "A")
                                    ),
                                    new XElement("language", 
                                        new XAttribute("languageIsoCode", "it"),
                                        new XAttribute("countryIsoCode", "IT")
                                    ),
                                    new XElement("issueInfo",
                                        new XAttribute("issueNumber", versione ?? "001"),
                                        new XAttribute("inWork", "00")
                                    )
                                ),
                                new XElement("dmAddressItems",
                                    new XElement("issueDate", 
                                        new XAttribute("year", tableData?.det.Data?.Year ?? DateTime.Now.Year),
                                        new XAttribute("month", tableData?.det.Data?.Month ?? DateTime.Now.Month),
                                        new XAttribute("day", tableData?.det.Data?.Day ?? DateTime.Now.Day)
                                    ),
                                    new XElement("dmTitle",
                                        new XElement("techName", $"TAVOLA {tavola}"),
                                        new XElement("infoName", "ILLUSTRATED PARTS DATA")
                                    )
                                )
                            ),
                            new XElement("dmStatus",
                                new XElement("security", 
                                    new XAttribute("securityClassification", "01")
                                ),
                                new XElement("responsiblePartnerCompany",
                                    new XElement("enterpriseName", "ARDEC")
                                ),
                                new XElement("originator",
                                    new XElement("enterpriseName", "ARDEC")
                                ),
                                new XElement("applic",
                                    new XElement("displayText",
                                        new XElement("simplePara", "All")
                                    )
                                ),
                                new XElement("brexDmRef",
                                    new XElement("dmRef",
                                        new XElement("dmRefIdent",
                                            new XElement("dmCode", 
                                                new XAttribute("modelIdentCode", "S1000D"),
                                                new XAttribute("systemDiffCode", "F"),
                                                new XAttribute("systemCode", "04"),
                                                new XAttribute("subSystemCode", "1"),
                                                new XAttribute("subSubSystemCode", "0"),
                                                new XAttribute("assyCode", "0301"),
                                                new XAttribute("disassyCode", "00"),
                                                new XAttribute("disassyCodeVariant", "A"),
                                                new XAttribute("infoCode", "022"),
                                                new XAttribute("infoCodeVariant", "A"),
                                                new XAttribute("itemLocationCode", "D")
                                            )
                                        )
                                    )
                                ),
                                new XElement("qualityAssurance",
                                    new XElement("unverified")
                                )
                            )
                        ),
                        
                        // Content section
                        new XElement("content",
                            new XElement("illustratedPartsCatalog",
                                new XElement("figure",
                                    new XAttribute("id", $"fig-{tavola}"),
                                    new XElement("title", tableData?.det.Descrizione1IT ?? $"Tavola {tavola}"),
                                    new XElement("graphic", 
                                        new XAttribute("infoEntityIdent", tableData?.det.Figura ?? $"ICN-{tavola}-001")
                                    )
                                ),
                                
                                // Parts list
                                new XElement("catalogSeqNumber",
                                    new XAttribute("catalogSeqNumberValue", "001"),
                                    new XElement("itemSeqNumber",
                                        new XAttribute("itemSeqNumberValue", "001"),
                                        new XElement("quantityPerNextHigherAssy", "1"),
                                        new XElement("partRef",
                                            new XAttribute("partNumberValue", tableData?.det.CodiceTecnico ?? tavola),
                                            new XElement("partIdent",
                                                new XElement("manufacturerCode", "ARDEC"),
                                                new XElement("partNumber", tableData?.det.CodiceTecnico ?? tavola)
                                            )
                                        ),
                                        new XElement("itemIdentData",
                                            new XElement("descrForPart", tableData?.det.Descrizione1IT ?? "")
                                        )
                                    )
                                )
                            )
                        )
                    )
                );

                // Convert to byte array
                using (var ms = new MemoryStream())
                {
                    using (var writer = XmlWriter.Create(ms, new XmlWriterSettings 
                    { 
                        Indent = true, 
                        Encoding = Encoding.UTF8 
                    }))
                    {
                        xDoc.Save(writer);
                    }
                    return ms.ToArray();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting table {Tavola} to XML", tavola);
                throw;
            }
        }

        /// <summary>
        /// Export single table to Excel format
        /// </summary>
        public async Task<byte[]> ExportToExcelAsync(string tavola, string versione, string ter)
        {
            try
            {
                var tableData = await (from comp in _context.TB_Composizione
                                      join det in _context.TB_DettagliTavole
                                          on comp.Tavola equals det.Tavola
                                      where comp.Tavola == tavola && comp.TER == ter && det.Versione == versione
                                      select new { comp, det })
                                      .FirstOrDefaultAsync();

                if (tableData == null)
                {
                    throw new ArgumentException($"Table {tavola} version {versione} not found");
                }

                using (var workbook = new XLWorkbook())
                {
                    var worksheet = workbook.Worksheets.Add($"Tavola_{tavola}");
                    
                    // Apply ARDEC styling
                    worksheet.Row(1).Style.Fill.BackgroundColor = XLColor.FromHtml("#044687");
                    worksheet.Row(1).Style.Font.FontColor = XLColor.White;
                    worksheet.Row(1).Style.Font.Bold = true;
                    worksheet.Row(1).Height = 30;
                    
                    // Header section
                    int row = 1;
                    worksheet.Cell(row, 1).Value = "TAVOLA";
                    worksheet.Cell(row, 2).Value = tavola;
                    worksheet.Range(row, 2, row, 6).Merge();
                    
                    row++;
                    worksheet.Cell(row, 1).Value = "VERSIONE";
                    worksheet.Cell(row, 2).Value = versione;
                    worksheet.Cell(row, 4).Value = "STATO";
                    worksheet.Cell(row, 5).Value = GetStatusText(tableData?.det.Stato);
                    
                    row++;
                    worksheet.Cell(row, 1).Value = "CATALOGO TER";
                    worksheet.Cell(row, 2).Value = ter;
                    worksheet.Cell(row, 4).Value = "DATA";
                    worksheet.Cell(row, 5).Value = tableData?.det.Data?.ToString("dd/MM/yyyy") ?? "";
                    
                    // Add separator
                    row += 2;
                    
                    // Technical data section
                    worksheet.Cell(row, 1).Value = "DATI TECNICI";
                    worksheet.Cell(row, 1).Style.Fill.BackgroundColor = XLColor.FromHtml("#e8f2ff");
                    worksheet.Cell(row, 1).Style.Font.Bold = true;
                    worksheet.Range(row, 1, row, 6).Merge();
                    
                    row++;
                    AddDataRow(worksheet, ref row, "Codice Tecnico", tableData?.det.CodiceTecnico);
                    AddDataRow(worksheet, ref row, "DMC", tableData?.det.DMC);
                    AddDataRow(worksheet, ref row, "SBC", tableData?.det.SBC);
                    AddDataRow(worksheet, ref row, "SNS", tableData?.det.SNS);
                    
                    // Descriptions section
                    row++;
                    worksheet.Cell(row, 1).Value = "DESCRIZIONI";
                    worksheet.Cell(row, 1).Style.Fill.BackgroundColor = XLColor.FromHtml("#e8f2ff");
                    worksheet.Cell(row, 1).Style.Font.Bold = true;
                    worksheet.Range(row, 1, row, 6).Merge();
                    
                    row++;
                    AddDescriptionRow(worksheet, ref row, "Descrizione IT 1", tableData?.det.Descrizione1IT);
                    AddDescriptionRow(worksheet, ref row, "Descrizione IT 2", tableData?.det.Descrizione2IT);
                    AddDescriptionRow(worksheet, ref row, "Descrizione IT 3", tableData?.det.Descrizione3IT);
                    
                    // Language groups
                    row++;
                    worksheet.Cell(row, 1).Value = "GRUPPI LINGUA";
                    worksheet.Cell(row, 1).Style.Fill.BackgroundColor = XLColor.FromHtml("#e8f2ff");
                    worksheet.Cell(row, 1).Style.Font.Bold = true;
                    worksheet.Range(row, 1, row, 6).Merge();
                    
                    row++;
                    AddDataRow(worksheet, ref row, "🇮🇹 ITA", tableData?.comp.GruppoITA);
                    AddDataRow(worksheet, ref row, "🇬🇧 ENG", tableData?.comp.GruppoENG);
                    AddDataRow(worksheet, ref row, "🇫🇷 FRA", tableData?.comp.GruppoFRA);
                    AddDataRow(worksheet, ref row, "🇵🇹 POR", tableData?.comp.GruppoPOR);
                    AddDataRow(worksheet, ref row, "🇪🇸 ESP", tableData?.comp.GruppoESP);
                    AddDataRow(worksheet, ref row, "🇩🇪 TED", tableData?.comp.GruppoTED);
                    AddDataRow(worksheet, ref row, "🇺🇸 USA", tableData?.comp.GruppoUSA);
                    
                    // Auto-fit columns
                    worksheet.Columns().AdjustToContents();
                    worksheet.Column(1).Width = 25;
                    worksheet.Column(2).Width = 50;
                    
                    // Save to memory stream
                    using (var stream = new MemoryStream())
                    {
                        workbook.SaveAs(stream);
                        return stream.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting table {Tavola} to Excel", tavola);
                throw;
            }
        }

        /// <summary>
        /// Export multiple tables to Excel with multiple sheets
        /// </summary>
        public async Task<byte[]> ExportMultipleTablesToExcelAsync(List<string> tavole, string ter)
        {
            try
            {
                using (var workbook = new XLWorkbook())
                {
                    // Summary sheet
                    var summarySheet = workbook.Worksheets.Add("Riepilogo");
                    SetupSummarySheet(summarySheet, tavole, ter);
                    
                    // Individual sheets for each table
                    foreach (var tavola in tavole.Take(10)) // Limit to 10 tables for performance
                    {
                        var tableData = await (from comp in _context.TB_Composizione
                                              join det in _context.TB_DettagliTavole
                                                  on comp.Tavola equals det.Tavola
                                              where comp.Tavola == tavola && comp.TER == ter
                                              orderby det.Versione descending
                                              select new { comp, det })
                                              .FirstOrDefaultAsync();
                            
                        if (tableData != null)
                        {
                            if (tableData != null)
                                AddTableSheet(workbook, tableData.comp, tableData.det);
                        }
                    }
                    
                    using (var stream = new MemoryStream())
                    {
                        workbook.SaveAs(stream);
                        return stream.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting multiple tables to Excel");
                throw;
            }
        }

        /// <summary>
        /// Export entire catalog to Excel
        /// </summary>
        public async Task<byte[]> ExportCatalogToExcelAsync(string ter)
        {
            try
            {
                var catalog = await _context.TB_Cataloghi
                    .Where(c => c.TER == ter)
                    .FirstOrDefaultAsync();
                    
                if (catalog == null)
                {
                    throw new ArgumentException($"Catalog {ter} not found");
                }
                
                var tables = await (from comp in _context.TB_Composizione
                                   join det in _context.TB_DettagliTavole
                                       on comp.Tavola equals det.Tavola
                                   where comp.TER == ter
                                   group new { comp, det } by comp.Tavola into g
                                   select new
                                   {
                                       Tavola = g.Key,
                                       LatestVersion = g.OrderByDescending(x => x.det.Versione).FirstOrDefault()
                                   })
                                   .ToListAsync();
                
                using (var workbook = new XLWorkbook())
                {
                    // Catalog info sheet
                    var catalogSheet = workbook.Worksheets.Add("Catalogo");
                    SetupCatalogSheet(catalogSheet, catalog, tables.Count);
                    
                    // Tables list sheet
                    var tablesSheet = workbook.Worksheets.Add("Elenco_Tavole");
                    // Convert to List<dynamic> for SetupTablesListSheet
                    var tablesList = tables
                        .Where(t => t.LatestVersion != null)
                        .Select(t => (dynamic)new { comp = t.LatestVersion.comp, det = t.LatestVersion.det })
                        .ToList();
                    SetupTablesListSheet(tablesSheet, tablesList);
                    
                    using (var stream = new MemoryStream())
                    {
                        workbook.SaveAs(stream);
                        return stream.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting catalog {TER} to Excel", ter);
                throw;
            }
        }

        #region Helper Methods


        private string GetStatusText(string stato)
        {
            return stato switch
            {
                "00" => "Approvata",
                "01" => "In Revisione",
                "02" => "Bozza",
                "03" => "Problemi",
                _ => "N/A"
            };
        }

        private void AddDataRow(IXLWorksheet worksheet, ref int row, string label, string value)
        {
            if (!string.IsNullOrEmpty(value))
            {
                worksheet.Cell(row, 1).Value = label;
                worksheet.Cell(row, 1).Style.Font.Bold = true;
                worksheet.Cell(row, 2).Value = value;
                worksheet.Range(row, 2, row, 6).Merge();
                row++;
            }
        }

        private void AddDescriptionRow(IXLWorksheet worksheet, ref int row, string label, string value)
        {
            if (!string.IsNullOrEmpty(value))
            {
                worksheet.Cell(row, 1).Value = label;
                worksheet.Cell(row, 1).Style.Font.Bold = true;
                worksheet.Cell(row, 2).Value = value;
                worksheet.Cell(row, 2).Style.Alignment.WrapText = true;
                worksheet.Range(row, 2, row, 6).Merge();
                worksheet.Row(row).Height = Math.Min(100, 15 * (value.Length / 100 + 1));
                row++;
            }
        }

        private void SetupSummarySheet(IXLWorksheet sheet, List<string> tavole, string ter)
        {
            sheet.Cell(1, 1).Value = "RIEPILOGO ESPORTAZIONE";
            sheet.Cell(1, 1).Style.Font.Bold = true;
            sheet.Cell(1, 1).Style.Font.FontSize = 16;
            sheet.Range(1, 1, 1, 4).Merge();
            
            sheet.Cell(3, 1).Value = "Catalogo TER:";
            sheet.Cell(3, 2).Value = ter;
            
            sheet.Cell(4, 1).Value = "Numero Tavole:";
            sheet.Cell(4, 2).Value = tavole.Count;
            
            sheet.Cell(5, 1).Value = "Data Export:";
            sheet.Cell(5, 2).Value = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
            
            sheet.Cell(7, 1).Value = "Elenco Tavole";
            sheet.Cell(7, 1).Style.Font.Bold = true;
            
            int row = 8;
            foreach (var tavola in tavole)
            {
                sheet.Cell(row, 1).Value = tavola;
                row++;
            }
            
            sheet.Columns().AdjustToContents();
        }

        private void SetupCatalogSheet(IXLWorksheet sheet, TB_Cataloghi catalog, int tableCount)
        {
            sheet.Row(1).Style.Fill.BackgroundColor = XLColor.FromHtml("#044687");
            sheet.Row(1).Style.Font.FontColor = XLColor.White;
            sheet.Row(1).Style.Font.Bold = true;
            
            sheet.Cell(1, 1).Value = "CATALOGO TECNICO";
            sheet.Range(1, 1, 1, 4).Merge();
            
            int row = 3;
            AddDataRow(sheet, ref row, "TER", catalog.TER);
            AddDataRow(sheet, ref row, "Titolo Breve", catalog.TitoloBreve);
            AddDataRow(sheet, ref row, "Titolo Completo", catalog.Titolo);
            AddDataRow(sheet, ref row, "Revisione", catalog.Revi);
            AddDataRow(sheet, ref row, "Base", catalog.Base);
            AddDataRow(sheet, ref row, "Numero Tavole", tableCount.ToString());
            
            sheet.Columns().AdjustToContents();
        }

        private void SetupTablesListSheet(IXLWorksheet sheet, List<dynamic> tables)
        {
            // Header
            sheet.Row(1).Style.Fill.BackgroundColor = XLColor.FromHtml("#044687");
            sheet.Row(1).Style.Font.FontColor = XLColor.White;
            sheet.Row(1).Style.Font.Bold = true;
            
            sheet.Cell(1, 1).Value = "Tavola";
            sheet.Cell(1, 2).Value = "Cod. Tecnico";
            sheet.Cell(1, 3).Value = "Versione";
            sheet.Cell(1, 4).Value = "Stato";
            sheet.Cell(1, 5).Value = "Descrizione";
            sheet.Cell(1, 6).Value = "DMC";
            sheet.Cell(1, 7).Value = "Data";
            
            // Data
            int row = 2;
            foreach (var table in tables)
            {
                sheet.Cell(row, 1).Value = table.comp.Tavola;
                sheet.Cell(row, 2).Value = table.det.CodiceTecnico;
                sheet.Cell(row, 3).Value = table.det.Versione;
                sheet.Cell(row, 4).Value = GetStatusText(table.det.Stato);
                sheet.Cell(row, 5).Value = table.det.Descrizione1IT;
                sheet.Cell(row, 6).Value = table.det.DMC;
                sheet.Cell(row, 7).Value = table.det.Data?.ToString("dd/MM/yyyy");
                
                // Apply alternating row colors
                if (row % 2 == 0)
                {
                    sheet.Row(row).Style.Fill.BackgroundColor = XLColor.FromHtml("#f8f9fa");
                }
                
                row++;
            }
            
            // Auto-filter
            sheet.Range(1, 1, row - 1, 7).SetAutoFilter();
            
            // Adjust columns
            sheet.Columns().AdjustToContents();
        }

        private void AddTableSheet(IXLWorkbook workbook, TB_Composizione compData, TB_DettagliTavole detData)
        {
            var sheetName = compData.Tavola.Length > 31 
                ? compData.Tavola.Substring(0, 31) 
                : compData.Tavola;
                
            var worksheet = workbook.Worksheets.Add(sheetName);
            
            int row = 1;
            worksheet.Cell(row, 1).Value = "TAVOLA";
            worksheet.Cell(row, 2).Value = compData.Tavola;
            worksheet.Range(row, 2, row, 4).Merge();
            
            // Add all table data...
            // (Similar to ExportToExcelAsync implementation)
            
            worksheet.Columns().AdjustToContents();
        }

        #endregion
    }
}
