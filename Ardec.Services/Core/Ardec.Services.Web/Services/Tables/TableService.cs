using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace Ardec.Services.Web.Services.Tables;

/// <summary>
/// Table Service Implementation - Main class with TB_Tavole operations
/// Manages TB_Tavole with versioning support and TER relationships
/// </summary>
public partial class TableService : ITableService
{
    private readonly ArdecDbContext _context;
    private readonly ILogger<TableService> _logger;

    public TableService(ArdecDbContext context, ILogger<TableService> logger)
    {
        _context = context;
        _logger = logger;
    }

    #region TB_Tavole Operations

    public async Task<IEnumerable<TB_Tavole>> GetAllTablesAsync()
    {
        try
        {
            return await _context.TB_Tavole
                .OrderBy(t => t.Tavola)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all tables");
            throw;
        }
    }

    public async Task<IEnumerable<TB_Tavole>> GetTablesLazyAsync()
    {
        try
        {
            return await _context.TB_Tavole
                .AsNoTracking()
                .OrderBy(t => t.Tavola)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tables (lazy)");
            throw;
        }
    }

    public async Task<TB_Tavole?> GetTableByTavolaAsync(string tavola)
    {
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));

        try
        {
            return await _context.TB_Tavole
                .FirstOrDefaultAsync(t => t.Tavola == tavola);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving table by tavola: {Tavola}", tavola);
            throw;
        }
    }

    public async Task<IEnumerable<TB_Tavole>> GetTablesByTavolaAsync(string tavola)
    {
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));

        try
        {
            return await _context.TB_Tavole
                .Where(t => t.Tavola == tavola)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tables by tavola: {Tavola}", tavola);
            throw;
        }
    }

    public async Task<TB_Tavole> CreateTableAsync(TB_Tavole table)
    {
        if (table == null)
            throw new ArgumentNullException(nameof(table));
        if (string.IsNullOrWhiteSpace(table.Tavola))
            throw new ArgumentException("Tavola name is required", nameof(table));

        try
        {
            var exists = await TableExistsAsync(table.Tavola);
            if (exists)
                throw new InvalidOperationException($"Table '{table.Tavola}' already exists");

            _context.TB_Tavole.Add(table);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created table: {Tavola}", table.Tavola);
            return table;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating table: {Tavola}", table?.Tavola);
            throw;
        }
    }

    public async Task<TB_Tavole> UpdateTableAsync(TB_Tavole table)
    {
        if (table == null)
            throw new ArgumentNullException(nameof(table));

        try
        {
            var existingTable = await GetTableByTavolaAsync(table.Tavola);
            if (existingTable == null)
                throw new InvalidOperationException($"Table '{table.Tavola}' not found");

            // Update all Framework properties
            existingTable.TER = table.TER;
            existingTable.CodiceTecnico = table.CodiceTecnico;
            existingTable.Versione = table.Versione;
            existingTable.Stato = table.Stato;
            existingTable.Data = table.Data;
            existingTable.NonForniti = table.NonForniti;
            existingTable.Logo = table.Logo;
            existingTable.Figura = table.Figura;
            existingTable.Note = table.Note;
            existingTable.TECHNAME = table.TECHNAME;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Updated table: {Tavola}", table.Tavola);
            return existingTable;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating table: {Tavola}", table?.Tavola);
            throw;
        }
    }

    public async Task<bool> DeleteTableAsync(string tavola)
    {
        if (string.IsNullOrWhiteSpace(tavola))
            throw new ArgumentException("Tavola cannot be null or empty", nameof(tavola));

        try
        {
            // Delete all TB_DettagliTavole first
            var details = await _context.TB_DettagliTavole
                .Where(d => d.Tavola == tavola)
                .ToListAsync();

            if (details.Any())
                _context.TB_DettagliTavole.RemoveRange(details);

            // Delete TB_Tavole
            var table = await GetTableByTavolaAsync(tavola);
            if (table == null)
                return false;

            _context.TB_Tavole.Remove(table);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted table: {Tavola}", tavola);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting table: {Tavola}", tavola);
            throw;
        }
    }

    public async Task<bool> TableExistsAsync(string tavola)
    {
        if (string.IsNullOrWhiteSpace(tavola))
            return false;

        try
        {
            return await _context.TB_Tavole
                .AnyAsync(t => t.Tavola == tavola);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if table exists: {Tavola}", tavola);
            throw;
        }
    }

    public async Task<int> GetTablesCountAsync()
    {
        try
        {
            return await _context.TB_Tavole.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tables count");
            throw;
        }
    }

    // Framework core methods - Tables by TER through TB_Composizione
    public async Task<IEnumerable<TB_Tavole>> GetTablesByTerAsync(string ter, bool orderByTavola = true)
    {
        if (string.IsNullOrWhiteSpace(ter))
            return await GetTablesLazyAsync();

        try
        {
            var query = from t in _context.TB_Tavole
                        join c in _context.TB_Composizione on t.Tavola equals c.Tavola
                        where c.TER == ter
                        select t;

            return orderByTavola 
                ? await query.OrderBy(t => t.Tavola).ToListAsync()
                : await query.ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tables by TER: {TER}", ter);
            throw;
        }
    }

    #endregion
}
