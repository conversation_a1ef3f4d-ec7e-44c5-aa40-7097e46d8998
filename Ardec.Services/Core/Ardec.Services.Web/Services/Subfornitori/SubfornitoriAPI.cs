using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Shared.Authorization;

namespace Ardec.Services.Web.Features.Subfornitori;

// ===== DTOs =====

public record CreateSubfornitoriRequest
{
    [Required]
    [StringLength(255, ErrorMessage = "CodF non può superare i 255 caratteri")]
    public required string CodF { get; init; }

    [StringLength(255, ErrorMessage = "DescrizioneF non può superare i 255 caratteri")]
    public string? DescrizioneF { get; init; }

    [StringLength(255, ErrorMessage = "CEDOrder non può superare i 255 caratteri")]
    public string? CEDOrder { get; init; }

    public string? IndirizzoF { get; init; }
    public string? CittaNazioneF { get; init; }
}

public record UpdateSubfornitoriRequest
{
    [StringLength(255, ErrorMessage = "DescrizioneF non può superare i 255 caratteri")]
    public string? DescrizioneF { get; init; }

    [StringLength(255, ErrorMessage = "CEDOrder non può superare i 255 caratteri")]
    public string? CEDOrder { get; init; }

    public string? IndirizzoF { get; init; }
    public string? CittaNazioneF { get; init; }
}

public record SubfornitoriResponse
{
    public required string CodF { get; init; }
    public string? DescrizioneF { get; init; }
    public string? CEDOrder { get; init; }
    public string? IndirizzoF { get; init; }
    public string? CittaNazioneF { get; init; }
}

public record SubfornitoriSearchRequest
{
    public string? CodF { get; init; }
    public string? DescrizioneF { get; init; }
    public string? CEDOrder { get; init; }
    public string? SearchTerm { get; init; }
    public int Skip { get; init; } = 0;
    public int Take { get; init; } = 20;
    public string SortBy { get; init; } = "CodF";
    public bool SortDescending { get; init; } = false;
}

public record SubfornitoriPagedResult
{
    public required List<SubfornitoriResponse> Items { get; init; }
    public required int TotalCount { get; init; }
    public required int Skip { get; init; }
    public required int Take { get; init; }
    public bool HasNext => Skip + Take < TotalCount;
    public bool HasPrevious => Skip > 0;
}

public record SubfornitoriStatsResponse
{
    public int TotalSubfornitori { get; init; }
    public int WithDescription { get; init; }
    public int WithCEDOrder { get; init; }
    public int WithAddress { get; init; }
    public int WithCityNation { get; init; }
    public List<string> TopCities { get; init; } = [];
    public List<string> TopCEDOrders { get; init; } = [];
}

// ===== SERVICE INTERFACE =====

public interface ISubfornitoriService
{
    Task<SubfornitoriPagedResult> GetAllAsync(int skip = 0, int take = 20, CancellationToken cancellationToken = default);
    Task<SubfornitoriResponse?> GetByCodFAsync(string codF, CancellationToken cancellationToken = default);
    Task<SubfornitoriPagedResult> SearchAsync(SubfornitoriSearchRequest request, CancellationToken cancellationToken = default);
    Task<List<SubfornitoriResponse>> GetByCEDOrderAsync(string cedOrder, CancellationToken cancellationToken = default);
    Task<List<SubfornitoriResponse>> SearchByDescriptionAsync(string searchTerm, CancellationToken cancellationToken = default);
    Task<SubfornitoriResponse> CreateAsync(CreateSubfornitoriRequest request, CancellationToken cancellationToken = default);
    Task<SubfornitoriResponse?> UpdateAsync(string codF, UpdateSubfornitoriRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(string codF, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string codF, CancellationToken cancellationToken = default);
    Task<SubfornitoriStatsResponse> GetStatsAsync(CancellationToken cancellationToken = default);
}

// ===== SERVICE IMPLEMENTATION =====

public class SubfornitoriService : ISubfornitoriService
{
    private readonly ArdecDbContext _context;
    private readonly ILogger<SubfornitoriService> _logger;

    public SubfornitoriService(ArdecDbContext context, ILogger<SubfornitoriService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<SubfornitoriPagedResult> GetAllAsync(int skip = 0, int take = 20, CancellationToken cancellationToken = default)
    {
        var query = _context.TB_Subfornitori.AsNoTracking();
        var totalCount = await query.CountAsync(cancellationToken);
        
        var entities = await query
            .OrderBy(x => x.CodF)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
        
        var items = entities.Select(MapToResponse).ToList();

        return new SubfornitoriPagedResult { Items = items, TotalCount = totalCount, Skip = skip, Take = take };
    }

    public async Task<SubfornitoriResponse?> GetByCodFAsync(string codF, CancellationToken cancellationToken = default)
    {
        var entity = await _context.TB_Subfornitori
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.CodF == codF, cancellationToken);

        return entity != null ? MapToResponse(entity) : null;
    }

    public async Task<SubfornitoriPagedResult> SearchAsync(SubfornitoriSearchRequest request, CancellationToken cancellationToken = default)
    {
        var query = _context.TB_Subfornitori.AsNoTracking();

        if (!string.IsNullOrWhiteSpace(request.CodF))
            query = query.Where(x => EF.Functions.Like(x.CodF, $"%{request.CodF}%"));

        if (!string.IsNullOrWhiteSpace(request.DescrizioneF))
            query = query.Where(x => x.DescrizioneF != null && EF.Functions.Like(x.DescrizioneF, $"%{request.DescrizioneF}%"));

        if (!string.IsNullOrWhiteSpace(request.CEDOrder))
            query = query.Where(x => x.CEDOrder != null && EF.Functions.Like(x.CEDOrder, $"%{request.CEDOrder}%"));

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = $"%{request.SearchTerm}%";
            query = query.Where(x => 
                EF.Functions.Like(x.CodF, searchTerm) ||
                (x.DescrizioneF != null && EF.Functions.Like(x.DescrizioneF, searchTerm)) ||
                (x.CEDOrder != null && EF.Functions.Like(x.CEDOrder, searchTerm)) ||
                (x.IndirizzoF != null && EF.Functions.Like(x.IndirizzoF, searchTerm)) ||
                (x.CittaNazioneF != null && EF.Functions.Like(x.CittaNazioneF, searchTerm)));
        }

        query = request.SortBy.ToLower() switch
        {
            "descrizionef" => request.SortDescending ? query.OrderByDescending(x => x.DescrizioneF) : query.OrderBy(x => x.DescrizioneF),
            "cedorder" => request.SortDescending ? query.OrderByDescending(x => x.CEDOrder) : query.OrderBy(x => x.CEDOrder),
            _ => request.SortDescending ? query.OrderByDescending(x => x.CodF) : query.OrderBy(x => x.CodF)
        };

        var totalCount = await query.CountAsync(cancellationToken);
        var entities = await query
            .Skip(request.Skip)
            .Take(request.Take)
            .ToListAsync(cancellationToken);
        
        var items = entities.Select(MapToResponse).ToList();

        return new SubfornitoriPagedResult { Items = items, TotalCount = totalCount, Skip = request.Skip, Take = request.Take };
    }

    public async Task<List<SubfornitoriResponse>> GetByCEDOrderAsync(string cedOrder, CancellationToken cancellationToken = default)
    {
        var entities = await _context.TB_Subfornitori
            .AsNoTracking()
            .Where(x => x.CEDOrder == cedOrder)
            .OrderBy(x => x.CodF)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<List<SubfornitoriResponse>> SearchByDescriptionAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        var entities = await _context.TB_Subfornitori
            .AsNoTracking()
            .Where(x => x.DescrizioneF != null && EF.Functions.Like(x.DescrizioneF, $"%{searchTerm}%"))
            .OrderBy(x => x.CodF)
            .ToListAsync(cancellationToken);
        
        return entities.Select(MapToResponse).ToList();
    }

    public async Task<SubfornitoriResponse> CreateAsync(CreateSubfornitoriRequest request, CancellationToken cancellationToken = default)
    {
        var exists = await ExistsAsync(request.CodF, cancellationToken);
        if (exists)
            throw new InvalidOperationException($"Subfornitore con CodF '{request.CodF}' già esistente");

        var entity = new TB_Subfornitori
        {
            CodF = request.CodF,
            DescrizioneF = request.DescrizioneF,
            CEDOrder = request.CEDOrder,
            IndirizzoF = request.IndirizzoF,
            CittaNazioneF = request.CittaNazioneF
        };

        _context.TB_Subfornitori.Add(entity);
        await _context.SaveChangesAsync(cancellationToken);

        return MapToResponse(entity);
    }

    public async Task<SubfornitoriResponse?> UpdateAsync(string codF, UpdateSubfornitoriRequest request, CancellationToken cancellationToken = default)
    {
        var entity = await _context.TB_Subfornitori
            .FirstOrDefaultAsync(x => x.CodF == codF, cancellationToken);

        if (entity == null) return null;

        entity.DescrizioneF = request.DescrizioneF;
        entity.CEDOrder = request.CEDOrder;
        entity.IndirizzoF = request.IndirizzoF;
        entity.CittaNazioneF = request.CittaNazioneF;

        await _context.SaveChangesAsync(cancellationToken);
        return MapToResponse(entity);
    }

    public async Task<bool> DeleteAsync(string codF, CancellationToken cancellationToken = default)
    {
        var entity = await _context.TB_Subfornitori
            .FirstOrDefaultAsync(x => x.CodF == codF, cancellationToken);

        if (entity == null) return false;

        _context.TB_Subfornitori.Remove(entity);
        await _context.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<bool> ExistsAsync(string codF, CancellationToken cancellationToken = default)
    {
        return await _context.TB_Subfornitori
            .AsNoTracking()
            .AnyAsync(x => x.CodF == codF, cancellationToken);
    }

    public async Task<SubfornitoriStatsResponse> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        var totalSubfornitori = await _context.TB_Subfornitori.CountAsync(cancellationToken);
        var withDescription = await _context.TB_Subfornitori.Where(x => !string.IsNullOrWhiteSpace(x.DescrizioneF)).CountAsync(cancellationToken);
        var withCEDOrder = await _context.TB_Subfornitori.Where(x => !string.IsNullOrWhiteSpace(x.CEDOrder)).CountAsync(cancellationToken);
        var withAddress = await _context.TB_Subfornitori.Where(x => !string.IsNullOrWhiteSpace(x.IndirizzoF)).CountAsync(cancellationToken);
        var withCityNation = await _context.TB_Subfornitori.Where(x => !string.IsNullOrWhiteSpace(x.CittaNazioneF)).CountAsync(cancellationToken);

        var topCities = await _context.TB_Subfornitori
            .Where(x => !string.IsNullOrWhiteSpace(x.CittaNazioneF))
            .GroupBy(x => x.CittaNazioneF!)
            .OrderByDescending(g => g.Count())
            .Take(10)
            .Select(g => g.Key)
            .ToListAsync(cancellationToken);

        var topCEDOrders = await _context.TB_Subfornitori
            .Where(x => !string.IsNullOrWhiteSpace(x.CEDOrder))
            .GroupBy(x => x.CEDOrder!)
            .OrderByDescending(g => g.Count())
            .Take(10)
            .Select(g => g.Key)
            .ToListAsync(cancellationToken);

        return new SubfornitoriStatsResponse
        {
            TotalSubfornitori = totalSubfornitori,
            WithDescription = withDescription,
            WithCEDOrder = withCEDOrder,
            WithAddress = withAddress,
            WithCityNation = withCityNation,
            TopCities = topCities,
            TopCEDOrders = topCEDOrders
        };
    }

    private static readonly Func<TB_Subfornitori, SubfornitoriResponse> MapToResponse = entity => new()
    {
        CodF = entity.CodF,
        DescrizioneF = entity.DescrizioneF,
        CEDOrder = entity.CEDOrder,
        IndirizzoF = entity.IndirizzoF,
        CittaNazioneF = entity.CittaNazioneF
    };
}

// ===== CONTROLLER =====

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SubfornitoriController : ControllerBase
{
    private readonly ISubfornitoriService _subfornitoriService;
    private readonly ILogger<SubfornitoriController> _logger;

    public SubfornitoriController(ISubfornitoriService subfornitoriService, ILogger<SubfornitoriController> logger)
    {
        _subfornitoriService = subfornitoriService;
        _logger = logger;
    }

    [HttpGet]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<SubfornitoriPagedResult>> GetAll([FromQuery] int skip = 0, [FromQuery] int take = 20, CancellationToken cancellationToken = default)
    {
        if (take > 100) take = 100;
        var result = await _subfornitoriService.GetAllAsync(skip, take, cancellationToken);
        return Ok(result);
    }

    [HttpGet("{codF}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<SubfornitoriResponse>> GetByCodF(string codF, CancellationToken cancellationToken = default)
    {
        var result = await _subfornitoriService.GetByCodFAsync(codF, cancellationToken);
        return result == null ? NotFound($"Subfornitore con CodF '{codF}' non trovato") : Ok(result);
    }

    [HttpPost("search")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<SubfornitoriPagedResult>> Search([FromBody] SubfornitoriSearchRequest request, CancellationToken cancellationToken = default)
    {
        if (request.Take > 100) request = request with { Take = 100 };
        var result = await _subfornitoriService.SearchAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("by-cedorder/{cedOrder}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<SubfornitoriResponse>>> GetByCEDOrder(string cedOrder, CancellationToken cancellationToken = default)
    {
        var result = await _subfornitoriService.GetByCEDOrderAsync(cedOrder, cancellationToken);
        return Ok(result);
    }

    [HttpGet("search-description")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<SubfornitoriResponse>>> SearchByDescription([FromQuery] string searchTerm, CancellationToken cancellationToken = default)
    {
        var result = await _subfornitoriService.SearchByDescriptionAsync(searchTerm, cancellationToken);
        return Ok(result);
    }

    [HttpPost]
    [Authorize(Policy = Policies.WriteAccess)]
    public async Task<ActionResult<SubfornitoriResponse>> Create([FromBody] CreateSubfornitoriRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _subfornitoriService.CreateAsync(request, cancellationToken);
            return CreatedAtAction(nameof(GetByCodF), new { codF = result.CodF }, result);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
    }

    [HttpPut("{codF}")]
    [Authorize(Policy = Policies.WriteAccess)]
    public async Task<ActionResult<SubfornitoriResponse>> Update(string codF, [FromBody] UpdateSubfornitoriRequest request, CancellationToken cancellationToken = default)
    {
        var result = await _subfornitoriService.UpdateAsync(codF, request, cancellationToken);
        return result == null ? NotFound($"Subfornitore con CodF '{codF}' non trovato") : Ok(result);
    }

    [HttpDelete("{codF}")]
    [Authorize(Policy = Policies.DeleteAccess)]
    public async Task<ActionResult> Delete(string codF, CancellationToken cancellationToken = default)
    {
        var success = await _subfornitoriService.DeleteAsync(codF, cancellationToken);
        return success ? NoContent() : NotFound($"Subfornitore con CodF '{codF}' non trovato");
    }

    [HttpHead("{codF}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult> Exists(string codF, CancellationToken cancellationToken = default)
    {
        var exists = await _subfornitoriService.ExistsAsync(codF, cancellationToken);
        return exists ? Ok() : NotFound();
    }

    [HttpGet("stats")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<SubfornitoriStatsResponse>> GetStats(CancellationToken cancellationToken = default)
    {
        var result = await _subfornitoriService.GetStatsAsync(cancellationToken);
        return Ok(result);
    }
}
