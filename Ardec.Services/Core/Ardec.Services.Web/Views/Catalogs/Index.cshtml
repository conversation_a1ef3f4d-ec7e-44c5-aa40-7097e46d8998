@model Ardec.Services.Web.Controllers.Mvc.CatalogsIndexViewModel
@{
    ViewData["Title"] = "Cataloghi - MIL-STD-1388 Services";
    ViewBag.ToolbarTitle = "Cataloghi";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
}

@{
    ViewBag.ToolbarTitle = "Cataloghi Tecnici";
    ViewBag.ExtraButtons = @"<a href=""/Catalogs/Create"" class=""bg-ardec-primary hover:bg-ardec-primary-dark text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm"">
        <i class=""fas fa-plus mr-2""></i>Nuovo Catalogo
    </a>
    <button type=""button"" class=""bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium ml-2"">
        <i class=""fas fa-download mr-2""></i>Esporta
    </button>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Catalogs Table Card -->
<div class="bg-white rounded-lg shadow-sm border border-neutral-200">
    <div class="p-6">
        <!-- Flowbite Search and Filter Controls -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center pb-6 bg-white gap-4">
            <div class="flex flex-col md:flex-row md:items-center md:space-x-4 w-full">
                <!-- Search Input -->
                <label for="table-search" class="sr-only">Search</label>
                <div class="relative mb-4 md:mb-0">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                        </svg>
                    </div>
                    <input type="text" id="table-search" class="block w-full md:w-80 py-2.5 pl-4 pr-12 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 placeholder-gray-500" placeholder="Cerca cataloghi...">
                </div>
                
                <!-- Filter Dropdown -->
                <div class="relative">
                    <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown" class="inline-flex items-center justify-center py-2.5 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200" type="button">
                        <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="h-4 w-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
                        </svg>
                        Filtri
                        <svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                            <path clip-rule="evenodd" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                        </svg>
                    </button>
                    
                    <!-- Dropdown menu -->
                    <div id="filterDropdown" class="absolute top-full left-0 z-50 hidden w-48 mt-1 p-3 bg-white rounded-lg shadow-lg border border-gray-200">
                        <h6 class="mb-3 text-sm font-medium text-gray-900">Filtri</h6>
                        <ul class="space-y-2 text-sm" aria-labelledby="filterDropdownButton">
                            <li class="flex items-center">
                                <input id="filter-all" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-ardec-primary focus:ring-ardec-primary focus:ring-2" checked>
                                <label for="filter-all" class="ml-2 text-sm font-medium text-gray-900">Tutti (default)</label>
                            </li>
                            <li class="flex items-center">
                                <input id="filter-published" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-ardec-primary focus:ring-ardec-primary focus:ring-2">
                                <label for="filter-published" class="ml-2 text-sm font-medium text-gray-900">Pubblicati</label>
                            </li>
                            <li class="flex items-center">
                                <input id="filter-draft" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-ardec-primary focus:ring-ardec-primary focus:ring-2">
                                <label for="filter-draft" class="ml-2 text-sm font-medium text-gray-900">Bozze</label>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Flowbite DataTable -->
        <div class="overflow-x-auto shadow-sm ring-1 ring-black ring-opacity-5 rounded-lg">
            <table id="catalogsTable" class="w-full table-auto text-sm text-left text-gray-500 divide-y divide-gray-300">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 divide-y divide-gray-300">
                    <tr>
                        <th scope="col" class="w-32 px-6 py-3 cursor-pointer hover:bg-gray-100 whitespace-nowrap" onclick="sortTable(0)">
                            <div class="flex items-center justify-between">
                                <span>TER</span>
                                <svg class="w-3 h-3 opacity-30 hover:opacity-100 transition-opacity" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z"/>
                                </svg>
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(1)">
                            <div class="flex items-center justify-between">
                                <span>Titolo Breve</span>
                                <svg class="w-3 h-3 opacity-30 hover:opacity-100 transition-opacity" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z"/>
                                </svg>
                            </div>
                        </th>
                        <th scope="col" class="w-28 px-6 py-3 cursor-pointer hover:bg-gray-100 whitespace-nowrap" onclick="sortTable(2)">
                            <div class="flex items-center justify-between">
                                <span>Revisione</span>
                                <svg class="w-3 h-3 opacity-30 hover:opacity-100 transition-opacity" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z"/>
                                </svg>
                            </div>
                        </th>
                        <th scope="col" class="w-28 px-6 py-3 cursor-pointer hover:bg-gray-100 whitespace-nowrap" onclick="sortTable(3)">
                            <div class="flex items-center justify-between">
                                <span>Versione</span>
                                <svg class="w-3 h-3 opacity-30 hover:opacity-100 transition-opacity" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.50a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z"/>
                                </svg>
                            </div>
                        </th>
                        <th scope="col" class="w-24 px-6 py-3 text-center whitespace-nowrap">Lingua</th>
                        <th scope="col" class="w-32 px-6 py-3 text-center whitespace-nowrap">Info Name</th>
                        <th scope="col" class="w-32 px-6 py-3 text-center">
                            <span class="sr-only">Azioni</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var catalog in Model.Catalogs)
                    {
                        <tr class="bg-white border-b hover:bg-gray-50" data-ter="@catalog.TER">
                            <td class="px-6 py-4">
                                <span class="font-semibold text-ardec-primary">@catalog.TER</span>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="font-medium text-gray-900">@catalog.TitoloBreve</div>
                                    @if (!string.IsNullOrEmpty(catalog.TER))
                                    {
                                        <div class="text-sm text-gray-500">ID: @catalog.TER</div>
                                    }
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-ardec-100 text-info">
                                    @(catalog.Revisione ?? "N/A")
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-ardec-100 text-success">
                                    @(catalog.Versione ?? "N/A")
                                </span>
                            </td>
                            <td class="px-6 py-4 text-center">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-globe text-gray-400 mr-2"></i>
                                    <span class="text-sm font-medium">IT</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-center">
                                <span class="text-gray-500 text-sm">-</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex justify-center space-x-1">
                                    <a href="/Tables?ter=@catalog.TER"
                                       class="inline-flex items-center px-2 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
                                       title="Tavole">
                                        <i class="fas fa-table"></i>
                                    </a>
                                    <a href="@Url.Action("Details", new { id = catalog.TER })"
                                       class="inline-flex items-center px-2 py-1 text-sm text-info hover:text-ardec-primary hover:bg-ardec-50 rounded transition-colors"
                                       title="Visualizza">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="@Url.Action("Edit", new { id = catalog.TER })"
                                       class="inline-flex items-center px-2 py-1 text-sm text-neutral-600 hover:text-neutral-800 hover:bg-neutral-100 rounded transition-colors"
                                       title="Modifica">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="@Url.Action("Delete", new { id = catalog.TER })"
                                       class="inline-flex items-center px-2 py-1 text-sm text-danger hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                                       title="Elimina">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    <div class="px-6 py-4 border-t border-neutral-200 bg-neutral-50 rounded-b-lg">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 text-sm text-neutral-600">
            <span class="flex items-center">
                <i class="fas fa-database mr-2 text-ardec-primary"></i>
                Totale: <strong class="mx-2">@Model.TotalCount</strong> cataloghi
            </span>
            <span class="flex items-center">
                <i class="fas fa-clock mr-2 text-neutral-400"></i>
                Aggiornato: <span class="ml-1 font-medium">@DateTime.Now.ToString("dd/MM/yyyy HH:mm")</span>
            </span>
        </div>
    </div>
</div>

@await Html.PartialAsync("_ToastContainer")

@section Scripts {
    <script>
        let currentSortColumn = 0;
        let sortDirection = 'asc';
        let catalogsData = [];

        $(document).ready(function() {
            // Initialize Flowbite components with delay to ensure it's loaded
            setTimeout(() => {
                if (window.Flowbite && typeof window.Flowbite.init === 'function') {
                    window.Flowbite.init();
                    console.log('✅ Flowbite initialized successfully');
                } else {
                    console.warn('⚠️ Flowbite not available, using fallback functionality');
                }
            }, 100);

            // Store original data for filtering/sorting
            catalogsData = Array.from($('#catalogsTable tbody tr')).map(row => {
                return {
                    element: row,
                    data: Array.from(row.cells).map(cell => cell.textContent.trim()),
                    ter: $(row).data('ter')
                };
            });

            console.log(`Initialized ${catalogsData.length} catalog rows`);

            // Setup search functionality
            setupSearch();
            
            // Setup filter functionality 
            setupFilters();

            console.log('✅ Flowbite Catalogs DataTable initialized successfully!');
        });

        // Search functionality
        function setupSearch() {
            const searchInput = document.getElementById('table-search');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    filterTable(searchTerm);
                });
            }
        }

        // Filter functionality
        function setupFilters() {
            // Setup manual dropdown toggle as fallback for Flowbite
            const dropdownButton = document.getElementById('filterDropdownButton');
            const dropdownMenu = document.getElementById('filterDropdown');
            
            if (dropdownButton && dropdownMenu) {
                dropdownButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    dropdownMenu.classList.toggle('hidden');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!dropdownButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
                        dropdownMenu.classList.add('hidden');
                    }
                });
            }
            
            console.log('Filters setup completed');
        }

        // Filter table rows based on search term
        function filterTable(searchTerm) {
            const tbody = document.querySelector('#catalogsTable tbody');
            
            catalogsData.forEach(item => {
                const rowText = item.data.join(' ').toLowerCase();
                const shouldShow = !searchTerm || rowText.includes(searchTerm);
                
                if (shouldShow) {
                    item.element.style.display = '';
                    $(item.element).removeClass('hidden');
                } else {
                    item.element.style.display = 'none';
                    $(item.element).addClass('hidden');
                }
            });

            // Update visible count
            updateVisibleCount();
        }

        // Sort table by column
        function sortTable(columnIndex) {
            // Toggle sort direction if same column
            if (currentSortColumn === columnIndex) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortDirection = 'asc';
                currentSortColumn = columnIndex;
            }

            // Sort the data
            catalogsData.sort((a, b) => {
                let aVal = a.data[columnIndex].toLowerCase();
                let bVal = b.data[columnIndex].toLowerCase();
                
                // Handle numeric values for sorting
                if (!isNaN(aVal) && !isNaN(bVal)) {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                    return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
                }
                
                // Text sorting
                if (sortDirection === 'asc') {
                    return aVal.localeCompare(bVal);
                } else {
                    return bVal.localeCompare(aVal);
                }
            });

            // Re-render the table body
            const tbody = document.querySelector('#catalogsTable tbody');
            tbody.innerHTML = '';
            catalogsData.forEach(item => {
                tbody.appendChild(item.element);
            });

            // Update sort indicators (optional visual enhancement)
            updateSortIndicators(columnIndex, sortDirection);

            console.log(`Sorted by column ${columnIndex} (${sortDirection})`);
        }

        // Update sort visual indicators
        function updateSortIndicators(activeColumn, direction) {
            // Remove active classes from all headers
            const headers = document.querySelectorAll('#catalogsTable th.cursor-pointer');
            headers.forEach((header, index) => {
                const svg = header.querySelector('svg');
                if (svg) {
                    svg.style.opacity = index === activeColumn ? '1' : '0.3';
                    
                    // Rotate arrow based on direction
                    if (index === activeColumn) {
                        svg.style.transform = direction === 'desc' ? 'rotate(180deg)' : 'rotate(0deg)';
                    }
                }
            });
        }

        // Update visible count in footer
        function updateVisibleCount() {
            const visibleRows = catalogsData.filter(item => item.element.style.display !== 'none').length;
            const totalCount = catalogsData.length;
            
            // Update footer count if needed
            const countSpan = document.querySelector('[class*="border-t"] strong');
            if (countSpan && visibleRows !== totalCount) {
                countSpan.textContent = `${visibleRows} di ${totalCount}`;
            } else if (countSpan) {
                countSpan.textContent = totalCount;
            }
        }

        // Confirm delete with modern styling
        function confirmDelete(ter) {
            // Use native confirm for now - could be enhanced with Flowbite modal
            if (confirm(`🗑️ Eliminare il catalogo "${ter}"?\n\n⚠️ Questa azione non può essere annullata.`)) {
                // TODO: Implement AJAX delete call
                console.log('🗑️ Delete catalog:', ter);
                
                // Show success message
                showFlowbiteToast('Catalogo eliminato', `Il catalogo ${ter} è stato eliminato con successo.`, 'success');
                
                // Remove row from table (temporary - should be done after successful API call)
                const row = document.querySelector(`tr[data-ter="${ter}"]`);
                if (row) {
                    row.remove();
                    // Update catalogsData array
                    catalogsData = catalogsData.filter(item => item.ter !== ter);
                    updateVisibleCount();
                }
            }
        }

        // Flowbite-styled toast notifications
        function showFlowbiteToast(title, message, type = 'info') {
            // Create toast HTML with Flowbite classes
            const toastId = 'toast-' + Date.now();
            const iconMap = {
                success: 'text-green-500 bg-green-100',
                error: 'text-red-500 bg-red-100', 
                warning: 'text-orange-500 bg-orange-100',
                info: 'text-blue-500 bg-blue-100'
            };
            
            const iconClass = iconMap[type] || iconMap.info;
            
            const toastHtml = `
                <div id="${toastId}" class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow" role="alert">
                    <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 ${iconClass} rounded-lg">
                        <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                        </svg>
                        <span class="sr-only">${type} icon</span>
                    </div>
                    <div class="ml-3 text-sm font-normal">
                        <strong>${title}</strong><br/>${message}
                    </div>
                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8" onclick="document.getElementById('${toastId}').remove()" aria-label="Close">
                        <span class="sr-only">Close</span>
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                    </button>
                </div>
            `;
            
            // Add to toast container
            const container = document.getElementById('toast-container') || (() => {
                const div = document.createElement('div');
                div.id = 'toast-container';
                div.className = 'fixed bottom-4 right-4 z-50 space-y-2';
                document.body.appendChild(div);
                return div;
            })();
            
            container.insertAdjacentHTML('beforeend', toastHtml);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                const toast = document.getElementById(toastId);
                if (toast) {
                    toast.remove();
                }
            }, 5000);
        }

        // Make functions available globally
        window.sortTable = sortTable;
        window.confirmDelete = confirmDelete;
        window.showFlowbiteToast = showFlowbiteToast;
    </script>
}
