@model Ardec.Services.Web.Data.Models.TB_Cataloghi
@{
    ViewBag.ToolbarTitle = "Nuovo Catalogo";
    ViewBag.ShowSave = true;
    ViewBag.ShowUndoRedo = false;
    ViewBag.ExtraButtons = @"<a href=""/Catalogs"" class=""bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium"">
        <i class=""fas fa-arrow-left mr-2""></i>Torna all'elenco
    </a>";
}

@await Html.PartialAsync("_Toolbar")

<form asp-action="Create" method="post" class="space-y-6">
    @Html.AntiForgeryToken()
    
    <!-- Main Card -->
    <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
        <div class="card-ardec-body">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                
                <!-- <PERSON><PERSON><PERSON> -->
                <div class="space-y-6">
                    <!-- Informazioni Base -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                            <i class="fas fa-info-circle mr-2 text-ardec-primary"></i>Informazioni Base
                        </h3>
                        
                        <!-- TER - Campo obbligatorio -->
                        <div>
                            <label asp-for="TER" class="block text-sm font-medium text-gray-700 mb-1">
                                TER <span class="text-red-500">*</span>
                            </label>
                            <input asp-for="TER" 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                   placeholder="Inserisci codice TER"
                                   maxlength="155" 
                                   required />
                            <span asp-validation-for="TER" class="text-red-500 text-sm"></span>
                        </div>

                        <!-- Titolo Breve -->
                        <div>
                            <label asp-for="TitoloBreve" class="block text-sm font-medium text-gray-700 mb-1">
                                Titolo Breve
                            </label>
                            <input asp-for="TitoloBreve" 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                   placeholder="Inserisci titolo breve"
                                   maxlength="255" />
                            <span asp-validation-for="TitoloBreve" class="text-red-500 text-sm"></span>
                        </div>

                        <!-- Titolo Completo -->
                        <div>
                            <label asp-for="Titolo" class="block text-sm font-medium text-gray-700 mb-1">
                                Titolo Completo
                            </label>
                            <input asp-for="Titolo" 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                   placeholder="Inserisci titolo completo"
                                   maxlength="255" />
                            <span asp-validation-for="Titolo" class="text-red-500 text-sm"></span>
                        </div>

                        <!-- Titolo Secondario -->
                        <div>
                            <label asp-for="Titolo2" class="block text-sm font-medium text-gray-700 mb-1">
                                Titolo Secondario <span class="text-red-500">*</span>
                            </label>
                            <textarea asp-for="Titolo2" 
                                      rows="3"
                                      class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                      placeholder="Inserisci titolo secondario"
                                      maxlength="255"
                                      required></textarea>
                            <span asp-validation-for="Titolo2" class="text-red-500 text-sm"></span>
                        </div>

                        <!-- Titolo Gabbia/Figure -->
                        <div>
                            <label asp-for="TitoloGabbia" class="block text-sm font-medium text-gray-700 mb-1">
                                Titolo Figure/Gabbia
                            </label>
                            <textarea asp-for="TitoloGabbia" 
                                      rows="2"
                                      class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                      placeholder="Inserisci titolo per figure/gabbia"
                                      maxlength="255"></textarea>
                            <span asp-validation-for="TitoloGabbia" class="text-red-500 text-sm"></span>
                        </div>
                    </div>

                    <!-- Classificazioni -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                            <i class="fas fa-tags mr-2 text-ardec-primary"></i>Classificazioni
                        </h3>
                        
                        <!-- PubNUC -->
                        <div>
                            <label asp-for="PubNUC" class="block text-sm font-medium text-gray-700 mb-1">
                                PubNUC
                            </label>
                            <input asp-for="PubNUC" 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                   placeholder="Inserisci codice PubNUC"
                                   maxlength="255" />
                            <span asp-validation-for="PubNUC" class="text-red-500 text-sm"></span>
                        </div>

                        <!-- Reparto -->
                        <div>
                            <label asp-for="Reparto" class="block text-sm font-medium text-gray-700 mb-1">
                                Reparto
                            </label>
                            <input asp-for="Reparto" 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                   placeholder="Inserisci reparto"
                                   maxlength="255" />
                            <span asp-validation-for="Reparto" class="text-red-500 text-sm"></span>
                        </div>
                    </div>
                </div>

                <!-- Colonna Destra -->
                <div class="space-y-6">
                    <!-- Versioning -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                            <i class="fas fa-code-branch mr-2 text-ardec-primary"></i>Versioning
                        </h3>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <!-- Base -->
                            <div>
                                <label asp-for="Base" class="block text-sm font-medium text-gray-700 mb-1">
                                    Base
                                </label>
                                <input asp-for="Base" 
                                       class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                       placeholder="Es. 1.0"
                                       maxlength="255" />
                                <span asp-validation-for="Base" class="text-red-500 text-sm"></span>
                            </div>

                            <!-- Revisione -->
                            <div>
                                <label asp-for="Revi" class="block text-sm font-medium text-gray-700 mb-1">
                                    Revisione
                                </label>
                                <input asp-for="Revi" 
                                       class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                       placeholder="Es. A, B, C"
                                       maxlength="255" />
                                <span asp-validation-for="Revi" class="text-red-500 text-sm"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Flags e Opzioni -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                            <i class="fas fa-flag mr-2 text-ardec-primary"></i>Opzioni
                        </h3>
                        
                        <div class="space-y-3">
                            <!-- S1000D -->
                            <div class="flex items-center">
                                <input type="checkbox" name="S1000D" value="true" 
                                       class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-gray-300 rounded" />
                                <input type="hidden" name="S1000D" value="false" />
                                <label class="ml-2 block text-sm text-gray-700">
                                    Standard S1000D
                                </label>
                            </div>

                            <!-- IsUSA -->
                            <div class="flex items-center">
                                <input type="checkbox" name="IsUSA" value="true" 
                                       class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-gray-300 rounded" />
                                <input type="hidden" name="IsUSA" value="false" />
                                <label class="ml-2 block text-sm text-gray-700">
                                    Standard USA
                                </label>
                            </div>

                            <!-- IsMT -->
                            <div class="flex items-center">
                                <input type="checkbox" name="IsMT" value="true" 
                                       class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-gray-300 rounded" />
                                <input type="hidden" name="IsMT" value="false" />
                                <label class="ml-2 block text-sm text-gray-700">
                                    Manual Tecnico
                                </label>
                            </div>

                            <!-- IsExNato -->
                            <div class="flex items-center">
                                <input type="checkbox" name="IsExNato" value="true" 
                                       class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-gray-300 rounded" />
                                <input type="hidden" name="IsExNato" value="false" />
                                <label class="ml-2 block text-sm text-gray-700">
                                    Extra NATO
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Informazioni Aggiuntive -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                            <i class="fas fa-info mr-2 text-ardec-primary"></i>Informazioni Aggiuntive
                        </h3>
                        
                        <!-- Info Name -->
                        <div>
                            <label asp-for="InfoName" class="block text-sm font-medium text-gray-700 mb-1">
                                Nome Informazioni
                            </label>
                            <input asp-for="InfoName" 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                   placeholder="Nome file informazioni"
                                   maxlength="255" />
                            <span asp-validation-for="InfoName" class="text-red-500 text-sm"></span>
                        </div>

                        <!-- Applique -->
                        <div>
                            <label asp-for="Applique" class="block text-sm font-medium text-gray-700 mb-1">
                                Applique
                            </label>
                            <input asp-for="Applique" 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                                   placeholder="Informazioni applique"
                                   maxlength="255" />
                            <span asp-validation-for="Applique" class="text-red-500 text-sm"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Note -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div>
                    <label asp-for="CEDNote" class="block text-sm font-medium text-gray-700 mb-1">
                        <i class="fas fa-sticky-note mr-1 text-ardec-primary"></i>Note
                    </label>
                    <textarea asp-for="CEDNote" 
                              rows="3" 
                              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" 
                              placeholder="Inserisci note aggiuntive sul catalogo..."></textarea>
                    <span asp-validation-for="CEDNote" class="text-red-500 text-sm"></span>
                </div>
            </div>
        </div>

        <!-- Footer con azioni -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i>
                    I campi contrassegnati con <span class="text-red-500">*</span> sono obbligatori
                </div>
                <div class="flex space-x-3">
                    <a href="/Catalogs" 
                       class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium">
                        <i class="fas fa-times mr-2"></i>Annulla
                    </a>
                    <button type="submit" 
                            class="bg-ardec-primary hover:bg-ardec-primary-dark text-white px-6 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm">
                        <i class="fas fa-save mr-2"></i>Crea Catalogo
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

@await Html.PartialAsync("_ToastContainer")

@section Scripts {
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validation-unobtrusive/3.2.12/jquery.validate.unobtrusive.min.js"></script>
    <script>
        $(document).ready(function() {
            // Auto-uppercase per TER
            $('#TER').on('input', function() {
                this.value = this.value.toUpperCase();
            });

            // Validazione client-side migliorata
            $('form').on('submit', function(e) {
                let isValid = true;
                const ter = $('#TER').val().trim();
                
                if (!ter) {
                    isValid = false;
                    showFlowbiteToast('Errore Validazione', 'Il campo TER è obbligatorio', 'error');
                }
                
                if (ter.length > 155) {
                    isValid = false;
                    showFlowbiteToast('Errore Validazione', 'Il TER non può superare 155 caratteri', 'error');
                }
                
                if (!isValid) {
                    e.preventDefault();
                    return false;
                }
                
                // Mostra loading
                $(this).find('button[type="submit"]').prop('disabled', true)
                    .html('<i class="fas fa-spinner fa-spin mr-2"></i>Creazione in corso...');
            });

            console.log('✅ Create Catalog form initialized');
        });

        // Toast notifications function
        function showFlowbiteToast(title, message, type = 'info') {
            const toastId = 'toast-' + Date.now();
            const iconMap = {
                success: 'text-green-500 bg-green-100',
                error: 'text-red-500 bg-red-100', 
                warning: 'text-orange-500 bg-orange-100',
                info: 'text-blue-500 bg-blue-100'
            };
            
            const iconClass = iconMap[type] || iconMap.info;
            
            const toastHtml = `
                <div id="${toastId}" class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow" role="alert">
                    <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 ${iconClass} rounded-lg">
                        <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                        </svg>
                        <span class="sr-only">${type} icon</span>
                    </div>
                    <div class="ml-3 text-sm font-normal">
                        <strong>${title}</strong><br/>${message}
                    </div>
                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8" onclick="document.getElementById('${toastId}').remove()">
                        <span class="sr-only">Close</span>
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                    </button>
                </div>
            `;
            
            const container = document.getElementById('toast-container') || (() => {
                const div = document.createElement('div');
                div.id = 'toast-container';
                div.className = 'fixed bottom-4 right-4 z-50 space-y-2';
                document.body.appendChild(div);
                return div;
            })();
            
            container.insertAdjacentHTML('beforeend', toastHtml);
            
            setTimeout(() => {
                const toast = document.getElementById(toastId);
                if (toast) toast.remove();
            }, 5000);
        }
    </script>
}
