@model Ardec.Services.Web.Data.Models.TB_Cataloghi
@{
    ViewBag.ToolbarTitle = $"Elimina Catalogo - {Model.TER}";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
    ViewBag.ExtraButtons = @"<a href=""/Catalogs"" class=""bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium"">
        <i class=""fas fa-arrow-left mr-2""></i>Torna all'elenco
    </a>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Alert di Warning -->
<div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-red-400"></i>
        </div>
        <div class="ml-3">
            <p class="text-sm text-red-700">
                <strong>Attenzione:</strong> Stai per eliminare definitivamente il catalogo <strong>@Model.TER</strong>. 
                Questa operazione non può essere annullata e comporterà la rimozione di tutti i dati associati.
            </p>
        </div>
    </div>
</div>

<!-- Main Card -->
<div class="bg-white rounded-lg shadow-sm border border-red-200">
    <div class="card-ardec-body">
        <div class="text-center mb-6">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-trash-alt text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Conferma Eliminazione Catalogo</h3>
            <p class="text-sm text-gray-500">
                I dati del catalogo che verranno eliminati sono mostrati di seguito
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            <!-- Colonna Sinistra -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-gray-900 border-b border-gray-200 pb-2">
                    <i class="fas fa-info-circle mr-2 text-red-500"></i>Informazioni Principali
                </h4>
                
                <!-- TER -->
                <div class="bg-gray-50 rounded-md p-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">TER</label>
                    <div class="text-lg font-mono text-gray-900">@(Model.TER ?? "N/A")</div>
                </div>

                <!-- Titolo Breve -->
                <div class="bg-gray-50 rounded-md p-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Titolo Breve</label>
                    <div class="text-sm text-gray-900">@(Model.TitoloBreve ?? "N/A")</div>
                </div>

                <!-- Titolo -->
                <div class="bg-gray-50 rounded-md p-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Titolo</label>
                    <div class="text-sm text-gray-900">@(Model.Titolo ?? "N/A")</div>
                </div>

                <!-- Base e Revisione -->
                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-gray-50 rounded-md p-3">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Base</label>
                        <div class="text-sm font-mono text-gray-900">@(Model.Base ?? "N/A")</div>
                    </div>
                    <div class="bg-gray-50 rounded-md p-3">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Revisione</label>
                        <div class="text-sm font-mono text-gray-900">@(Model.Revi ?? "N/A")</div>
                    </div>
                </div>
            </div>

            <!-- Colonna Destra -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-gray-900 border-b border-gray-200 pb-2">
                    <i class="fas fa-database mr-2 text-red-500"></i>Dati Correlati
                </h4>

                <!-- Composizioni/Tavole -->
                @if (Model.TB_Composizione?.Any() == true)
                {
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                            <div>
                                <div class="font-medium text-yellow-900">Tavole Associate</div>
                                <div class="text-yellow-700 text-sm">@Model.TB_Composizione.Count() tavole verranno eliminate</div>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="bg-green-50 border border-green-200 rounded-md p-3">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                            <div class="text-green-900 text-sm">Nessuna tavola associata</div>
                        </div>
                    </div>
                }

                <!-- RVT -->
                @if (Model.TB_CataloghiRVT?.Any() == true)
                {
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                            <div>
                                <div class="font-medium text-yellow-900">RVT Collegate</div>
                                <div class="text-yellow-700 text-sm">@Model.TB_CataloghiRVT.Count() RVT verranno eliminate</div>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="bg-green-50 border border-green-200 rounded-md p-3">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                            <div class="text-green-900 text-sm">Nessuna RVT collegata</div>
                        </div>
                    </div>
                }

                <!-- Opzioni Attive -->
                <div class="bg-gray-50 rounded-md p-3">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Opzioni Attive</label>
                    <div class="space-y-1 text-sm">
                        @if (Model.S1000D == true)
                        {
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-600 mr-2"></i>
                                <span>Standard S1000D</span>
                            </div>
                        }
                        @if (Model.IsUSA == true)
                        {
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-600 mr-2"></i>
                                <span>Standard USA</span>
                            </div>
                        }
                        @if (Model.IsMT == true)
                        {
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-600 mr-2"></i>
                                <span>Manual Tecnico</span>
                            </div>
                        }
                        @if (Model.IsExNato == true)
                        {
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-600 mr-2"></i>
                                <span>Extra NATO</span>
                            </div>
                        }
                        @if (Model.S1000D != true && Model.IsUSA != true && Model.IsMT != true && Model.IsExNato != true)
                        {
                            <div class="text-gray-500 italic">Nessuna opzione attiva</div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Note se presenti -->
        @if (!string.IsNullOrEmpty(Model.CEDNote))
        {
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="bg-gray-50 rounded-md p-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Note</label>
                    <div class="text-sm text-gray-900 whitespace-pre-wrap">@Model.CEDNote</div>
                </div>
            </div>
        }
    </div>

    <!-- Footer con azioni -->
    <div class="px-6 py-4 bg-red-50 border-t border-red-200 rounded-b-lg">
        <div class="flex items-center justify-between">
            <div class="text-sm text-red-700">
                <i class="fas fa-exclamation-triangle mr-1"></i>
                Questa operazione è <strong>irreversibile</strong>
            </div>
            <div class="flex space-x-3">
                <a href="/Catalogs" 
                   class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium">
                    <i class="fas fa-times mr-2"></i>Annulla
                </a>
                <form asp-action="Delete" method="post" class="inline">
                    @Html.AntiForgeryToken()
                    <input type="hidden" asp-for="TER" />
                    <button type="submit" 
                            id="delete-button"
                            class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm">
                        <i class="fas fa-trash-alt mr-2"></i>Elimina Definitivamente
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_ToastContainer")

@section Scripts {
    <script>
        $(document).ready(function() {
            // Conferma eliminazione con doppio click/conferma
            $('#delete-button').on('click', function(e) {
                e.preventDefault();
                
                const catalogTER = '@Model.TER';
                const hasAssociatedData = @((Model.TB_Composizione?.Any() == true || Model.TB_CataloghiRVT?.Any() == true).ToString().ToLower());
                
                let confirmMessage = `Sei sicuro di voler eliminare definitivamente il catalogo ${catalogTER}?`;
                
                if (hasAssociatedData) {
                    confirmMessage += '\n\nATTENZIONE: Verranno eliminate anche tutte le tavole e RVT associate.';
                }
                
                if (confirm(confirmMessage)) {
                    // Secondo livello di conferma per operazioni critiche
                    const finalConfirm = prompt(`Per confermare l'eliminazione, digita "${catalogTER}" nel campo sottostante:`);
                    
                    if (finalConfirm === catalogTER) {
                        // Disabilita il pulsante e mostra loading
                        $(this).prop('disabled', true)
                              .html('<i class="fas fa-spinner fa-spin mr-2"></i>Eliminazione in corso...');
                        
                        // Submit del form
                        $(this).closest('form')[0].submit();
                    } else if (finalConfirm !== null) {
                        showFlowbiteToast('Errore', 'Il TER inserito non corrisponde. Eliminazione annullata.', 'error');
                    }
                }
            });

            console.log('✅ Delete Catalog view initialized');
        });

        // Toast notifications function
        function showFlowbiteToast(title, message, type = 'info') {
            const toastId = 'toast-' + Date.now();
            const iconMap = {
                success: 'text-green-500 bg-green-100',
                error: 'text-red-500 bg-red-100', 
                warning: 'text-orange-500 bg-orange-100',
                info: 'text-blue-500 bg-blue-100'
            };
            
            const iconClass = iconMap[type] || iconMap.info;
            
            const toastHtml = `
                <div id="${toastId}" class="flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow" role="alert">
                    <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 ${iconClass} rounded-lg">
                        <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                        </svg>
                        <span class="sr-only">${type} icon</span>
                    </div>
                    <div class="ml-3 text-sm font-normal">
                        <strong>${title}</strong><br/>${message}
                    </div>
                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8" onclick="document.getElementById('${toastId}').remove()">
                        <span class="sr-only">Close</span>
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                    </button>
                </div>
            `;
            
            const container = document.getElementById('toast-container') || (() => {
                const div = document.createElement('div');
                div.id = 'toast-container';
                div.className = 'fixed bottom-4 right-4 z-50 space-y-2';
                document.body.appendChild(div);
                return div;
            })();
            
            container.insertAdjacentHTML('beforeend', toastHtml);
            
            setTimeout(() => {
                const toast = document.getElementById(toastId);
                if (toast) toast.remove();
            }, 5000);
        }
    </script>
}
