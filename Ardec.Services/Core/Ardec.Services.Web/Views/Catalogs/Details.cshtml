@model Ardec.Services.Web.Data.Models.TB_Cataloghi
@{
    ViewBag.ToolbarTitle = $"Dettagli Catalogo - {Model.TER}";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
    ViewBag.ExtraButtons = @"<a href=""/Tables?ter=" + Model.TER + @""" class=""bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium"">
        <i class=""fas fa-table mr-2""></i>Tavole Catalogo
    </a>
    <a href=""/Catalogs"" class=""bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium"">
        <i class=""fas fa-arrow-left mr-2""></i>Torna all'elenco
    </a>
    <a href=""/Catalogs/Edit/" + Model.TER + @""" class=""bg-ardec-primary hover:bg-ardec-primary-dark text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium"">
        <i class=""fas fa-edit mr-2""></i>Modifica
    </a>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Main Card -->
<div class="bg-white rounded-lg shadow-sm border border-neutral-200">
    <div class="card-ardec-body">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            <!-- Colonna Sinistra -->
            <div class="space-y-6">
                <!-- Informazioni Base -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        <i class="fas fa-info-circle mr-2 text-ardec-primary"></i>Informazioni Base
                    </h3>
                    
                    <!-- TER -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">TER</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 font-mono h-11 flex items-center">
                            @(Model.TER ?? "N/A")
                        </div>
                    </div>

                    <!-- Titolo Breve -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Titolo Breve</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 min-h-[88px] flex items-start">
                            <span class="whitespace-pre-wrap leading-relaxed">@(Model.TitoloBreve ?? "N/A")</span>
                        </div>
                    </div>

                    <!-- Titolo Completo -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Titolo Completo</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 min-h-[110px] flex items-start">
                            <span class="whitespace-pre-wrap leading-relaxed">@(Model.Titolo ?? "N/A")</span>
                        </div>
                    </div>

                    <!-- Titolo Secondario -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Titolo Secondario</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 min-h-[88px] flex items-start">
                            <span class="whitespace-pre-wrap leading-relaxed">@(Model.Titolo2 ?? "N/A")</span>
                        </div>
                    </div>

                    <!-- Titolo Gabbia/Figure -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Titolo Figure/Gabbia</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 h-11 flex items-center">
                            @(Model.TitoloGabbia ?? "N/A")
                        </div>
                    </div>
                </div>

                <!-- Classificazioni -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        <i class="fas fa-tags mr-2 text-ardec-primary"></i>Classificazioni
                    </h3>
                    
                    <!-- PubNUC -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">PubNUC</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 h-11 flex items-center">
                            @(Model.PubNUC ?? "N/A")
                        </div>
                    </div>

                    <!-- Reparto -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reparto</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 h-11 flex items-center">
                            @(Model.Reparto ?? "N/A")
                        </div>
                    </div>

                    <!-- Numero Stampato -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Numero Stampato</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 h-11 flex items-center">
                            @(Model.NStampato ?? "N/A")
                        </div>
                    </div>
                </div>
            </div>

            <!-- Colonna Destra -->
            <div class="space-y-6">
                <!-- Versioning -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        <i class="fas fa-code-branch mr-2 text-ardec-primary"></i>Versioning
                    </h3>
                    
                    <!-- Base -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Base</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 font-mono h-11 flex items-center">
                            @(Model.Base ?? "N/A")
                        </div>
                    </div>

                    <!-- Revisione -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Revisione</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 font-mono h-11 flex items-center">
                            @(Model.Revi ?? "N/A")
                        </div>
                    </div>
                </div>

                <!-- Flags e Opzioni -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        <i class="fas fa-flag mr-2 text-ardec-primary"></i>Opzioni
                    </h3>
                    
                    <div class="space-y-4">
                        <!-- S1000D -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-5 h-5 mr-3">
                                @if (Model.S1000D == true)
                                {
                                    <i class="fas fa-check-circle text-green-500"></i>
                                }
                                else
                                {
                                    <i class="fas fa-times-circle text-red-500"></i>
                                }
                            </div>
                            <label class="block text-sm text-gray-700">
                                Standard S1000D
                            </label>
                        </div>

                        <!-- IsUSA -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-5 h-5 mr-3">
                                @if (Model.IsUSA == true)
                                {
                                    <i class="fas fa-check-circle text-green-500"></i>
                                }
                                else
                                {
                                    <i class="fas fa-times-circle text-red-500"></i>
                                }
                            </div>
                            <label class="block text-sm text-gray-700">
                                Standard USA
                            </label>
                        </div>

                        <!-- IsMT -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-5 h-5 mr-3">
                                @if (Model.IsMT == true)
                                {
                                    <i class="fas fa-check-circle text-green-500"></i>
                                }
                                else
                                {
                                    <i class="fas fa-times-circle text-red-500"></i>
                                }
                            </div>
                            <label class="block text-sm text-gray-700">
                                Manual Tecnico
                            </label>
                        </div>

                        <!-- IsExNato -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-5 h-5 mr-3">
                                @if (Model.IsExNato == true)
                                {
                                    <i class="fas fa-check-circle text-green-500"></i>
                                }
                                else
                                {
                                    <i class="fas fa-times-circle text-red-500"></i>
                                }
                            </div>
                            <label class="block text-sm text-gray-700">
                                Extra NATO
                            </label>
                        </div>

                        <!-- CEDTONUMBER -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-5 h-5 mr-3">
                                @if (Model.CEDTONUMBER != null && Model.CEDTONUMBER != 0)
                                {
                                    <i class="fas fa-check-circle text-green-500"></i>
                                }
                                else
                                {
                                    <i class="fas fa-times-circle text-red-500"></i>
                                }
                            </div>
                            <label class="block text-sm text-gray-700">
                                CED To Number
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Informazioni Aggiuntive -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        <i class="fas fa-info mr-2 text-ardec-primary"></i>Informazioni Aggiuntive
                    </h3>
                    
                    <!-- Info Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Nome Informazioni</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 h-11 flex items-center">
                            @(Model.InfoName ?? "N/A")
                        </div>
                    </div>

                    <!-- Applique -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Applique</label>
                        <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 h-11 flex items-center">
                            @(Model.Applique ?? "N/A")
                        </div>
                    </div>

                    <!-- CEDLanguage -->
                    @if (!string.IsNullOrEmpty(Model.CEDLanguage))
                    {
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Lingua CED</label>
                            <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 h-11 flex items-center">
                                @switch (Model.CEDLanguage)
                                {
                                    case "IT":
                                        <span>🇮🇹 Italiano</span>
                                        break;
                                    case "EN":
                                        <span>🇬🇧 Inglese</span>
                                        break;
                                    case "FR":
                                        <span>🇫🇷 Francese</span>
                                        break;
                                    case "ES":
                                        <span>🇪🇸 Spagnolo</span>
                                        break;
                                    case "PT":
                                        <span>🇵🇹 Portoghese</span>
                                        break;
                                    case "TED":
                                        <span>🇩🇪 Tedesco</span>
                                        break;
                                    case "USA":
                                        <span>🇺🇸 USA</span>
                                        break;
                                    default:
                                        <span>@Model.CEDLanguage</span>
                                        break;
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Note -->
        @if (!string.IsNullOrEmpty(Model.CEDNote))
        {
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-sticky-note mr-1 text-ardec-primary"></i>Note
                    </label>
                    <div class="block w-full px-3 py-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm text-sm text-gray-900 min-h-[88px] flex items-start">
                        <span class="whitespace-pre-wrap leading-relaxed">@Model.CEDNote</span>
                    </div>
                </div>
            </div>
        }

        <!-- Metadati -->
        <div class="mt-6 pt-6 border-t border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 mb-4">
                <i class="fas fa-database mr-2 text-ardec-primary"></i>Metadati
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                
                @if (Model.TB_Composizione?.Any() == true)
                {
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-3 hover:bg-blue-100 transition-all duration-200 cursor-pointer" onclick="window.location.href='/Tables?ter=@Model.TER'">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-layer-group text-blue-600 mr-2"></i>
                                <div>
                                    <div class="font-medium text-blue-900">Tavole Associate</div>
                                    <div class="text-blue-700">@Model.TB_Composizione.Count() tavole</div>
                                </div>
                            </div>
                            <div class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </div>
                    </div>
                }

                @if (Model.TB_CataloghiRVT?.Any() == true)
                {
                    <div class="bg-green-50 border border-green-200 rounded-md p-3">
                        <div class="flex items-center">
                            <i class="fas fa-list-alt text-green-600 mr-2"></i>
                            <div>
                                <div class="font-medium text-green-900">RVT Collegate</div>
                                <div class="text-green-700">@Model.TB_CataloghiRVT.Count() RVT</div>
                            </div>
                        </div>
                    </div>
                }

                <div class="bg-gray-50 border border-gray-200 rounded-md p-3">
                    <div class="flex items-center">
                        <i class="fas fa-key text-gray-600 mr-2"></i>
                        <div>
                            <div class="font-medium text-gray-900">Chiave Primaria</div>
                            <div class="text-gray-700 font-mono">@Model.TER</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer con azioni -->
    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
                <i class="fas fa-eye mr-1"></i>
                Visualizzazione dettagli catalogo
            </div>
            <div class="flex space-x-3">
                <a href="/Catalogs" 
                   class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium">
                    <i class="fas fa-arrow-left mr-2"></i>Torna all'elenco
                </a>
                <a href="/Tables?ter=@Model.TER" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm">
                    <i class="fas fa-table mr-2"></i>Tavole
                </a>
                <a href="/Catalogs/Edit/@Model.TER" 
                   class="bg-ardec-primary hover:bg-ardec-primary-dark text-white px-6 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm">
                    <i class="fas fa-edit mr-2"></i>Modifica
                </a>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_ToastContainer")

@section Scripts {
    <script>
        $(document).ready(function() {
            console.log('✅ Details Catalog view initialized');
        });
    </script>
}
