@model Ardec.Services.Web.Models.Tables.TablesIndexViewModel
@{
    ViewData["Title"] = "Tavole - MIL-STD-1388 Services";
    ViewBag.ToolbarTitle = Model.IsFilteredByCatalog 
        ? $"Tavole - {Model.CatalogTitle}" 
        : "Tavole Tecniche";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
}

@{
    ViewBag.ExtraButtons = @"<a href=""/Tables/Create"" class=""bg-ardec-primary hover:bg-ardec-primary-dark text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm"">
        <i class=""fas fa-plus mr-2""></i>Nuova Tavola
    </a>
    <button type=""button"" class=""bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium ml-2"">
        <i class=""fas fa-download mr-2""></i>Esporta
    </button>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Tables Grid Card -->
<div class="bg-white rounded-lg shadow-sm border border-neutral-200">
    <div class="p-6">
        <!-- Simplified Filter Controls -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center pb-4 bg-white gap-4">
            <div class="flex flex-col md:flex-row md:items-center md:space-x-4">
                <!-- TER Filter Only -->
                <div class="relative">
                    <select id="terFilter" class="inline-flex items-center justify-center py-2.5 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200">
                        <option value="">Tutti i cataloghi</option>
                        @foreach (var catalog in ViewBag.AllCatalogs as List<Ardec.Services.Web.Data.Models.TB_Cataloghi> ?? new List<Ardec.Services.Web.Data.Models.TB_Cataloghi>())
                        {
                            var isSelected = Model.FilterTER == catalog.TER;
                            <option value="@catalog.TER" selected="@isSelected">
                                @catalog.TER - @catalog.TitoloBreve
                            </option>
                        }
                    </select>
                </div>
            </div>
        </div>
        
        <!-- DataTables Grid (pattern identico a Catalogs) -->
        <div class="overflow-x-auto shadow-sm ring-1 ring-black ring-opacity-5 rounded-lg">
            <table id="tablesTable" class="w-full table-auto text-sm text-left text-gray-500 divide-y divide-gray-300">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 divide-y divide-gray-300">
                    <tr>
                        <th scope="col" class="w-32 px-6 py-3 cursor-pointer hover:bg-gray-100 whitespace-nowrap">
                            <span>Tavola</span>
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100">
                            <span>Codice Tecnico</span>
                        </th>
                        @if (!Model.IsFilteredByCatalog)
                        {
                            <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100">
                                <span>Catalogo</span>
                            </th>
                        }
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100">
                            <span>Versioni & Stati</span>
                        </th>
                        <th scope="col" class="w-32 px-6 py-3 cursor-pointer hover:bg-gray-100 whitespace-nowrap">
                            <span>Modificata</span>
                        </th>
                        <th scope="col" class="w-32 px-6 py-3 text-center">
                            <span>Azioni</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach (var table in Model.Tables)
                    {
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-ardec-primary">
                                    @table.Tavola
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">@table.CodiceTecnico</div>
                            </td>
                            @if (!Model.IsFilteredByCatalog)
                            {
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">@table.TER</div>
                                </td>
                            }
                            <td class="px-6 py-4">
                                <div class="flex flex-wrap gap-1">
                                    @foreach (var versionPair in table.VersionStatusPairs)
                                    {
                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded @versionPair.StatusBadgeClass">
                                            v@(versionPair.Versione)
                                        </span>
                                    }
                                </div>
                                @if (!table.VersionStatusPairs.Any())
                                {
                                    <span class="text-xs text-gray-400">Nessuna versione</span>
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                @table.LastModified?.ToString("dd/MM/yyyy")
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <div class="flex items-center justify-center space-x-2">
                                    <a href="/Tables/Details/@table.Tavola" 
                                       class="text-ardec-primary hover:text-ardec-primary-dark transition-colors"
                                       title="Visualizza dettagli">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/Tables/Details/@table.Tavola?edit=true" 
                                       class="text-green-600 hover:text-green-800 transition-colors"
                                       title="Modifica">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="text-red-600 hover:text-red-800 transition-colors"
                                            onclick="confirmDelete('@table.Tavola')"
                                            title="Elimina">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@section Scripts
{
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize DataTables with optimized settings
            const tablesTable = $('#tablesTable').DataTable({
                responsive: true,
                pageLength: 25,
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/it-IT.json'
                },
                columnDefs: [
                    { targets: -1, orderable: false, searchable: false } // Actions column
                ],
                order: [[0, 'asc']], // Order by Tavola
                dom: 'Bfrtip', // Show search, pagination, info - remove length selector
                searching: true, // Enable DataTable native search
                paging: true,
                info: true
            });
            
            // TER filter
            $('#terFilter').on('change', function() {
                const ter = this.value;
                window.location.href = ter ? `/Tables?ter=${encodeURIComponent(ter)}` : '/Tables';
            });
        });
        
        function confirmDelete(tavola) {
            if (confirm(`Confermi l'eliminazione della tavola ${tavola}?`)) {
                // TODO: Implement delete functionality
                alert('Delete functionality to be implemented');
            }
        }
    </script>
}