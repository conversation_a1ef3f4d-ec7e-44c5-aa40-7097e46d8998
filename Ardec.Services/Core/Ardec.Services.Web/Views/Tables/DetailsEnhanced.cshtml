@model Ardec.Services.Web.Models.Tables.TableDetailsViewModel
@{
    ViewData["Title"] = $"Tavola {Model.Tavola} - Dettagli";
    ViewData["PageIcon"] = "fas fa-table";
    
    ViewBag.ToolbarTitle = $"Tavola: {Model.Tavola}";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
    
    var backToTablesUrl = !string.IsNullOrEmpty(Model.TER) ? $"/Tables?ter={Model.TER}" : "/Tables";
    
    ViewBag.ExtraButtons = $@"
        <a href=""{backToTablesUrl}"" class=""bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium"">
            <i class=""fas fa-arrow-left mr-2""></i>Torna alle Tavole
        </a>
        <button type=""button"" onclick=""exportTable()"" class=""bg-ardec-primary hover:bg-ardec-primary-dark text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm ml-2"">
            <i class=""fas fa-download mr-2""></i>Esporta
        </button>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Version Selector Bar -->
<div class="bg-gradient-to-r from-ardec-primary to-ardec-primary-dark rounded-lg shadow-sm mb-6 text-white">
    <div class="px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <!-- Table Info -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center justify-center w-12 h-12 bg-white bg-opacity-20 rounded-lg">
                        <i class="fas fa-table text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">@Model.Tavola</h1>
                        <p class="text-ardec-100 text-sm">
                            @if (!string.IsNullOrEmpty(Model.CodiceTecnico))
                            {
                                <span class="font-mono">@Model.CodiceTecnico</span>
                            }
                            @if (!string.IsNullOrEmpty(Model.TER))
                            {
                                <span class="ml-2">Catalogo: <span class="font-mono">@Model.TER</span></span>
                            }
                        </p>
                    </div>
                </div>
                
                <!-- Version Selector -->
                <div class="flex items-center space-x-3">
                    <label class="text-sm font-medium">Versione:</label>
                    <select id="versionSelector" 
                            class="bg-white bg-opacity-20 border border-white border-opacity-30 text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                            onchange="changeVersion(this.value)">
                        <option value="@Model.Versione" selected>@Model.Versione</option>
                    </select>
                </div>
                
                <!-- Status Badge -->
                <div>
                    @{
                        var statusClass = Model.Stato switch
                        {
                            "00" => "bg-green-500",
                            "01" => "bg-yellow-500",
                            "02" => "bg-blue-500",
                            "03" => "bg-red-500",
                            _ => "bg-gray-500"
                        };
                        var statusText = Model.Stato switch
                        {
                            "00" => "Approvata",
                            "01" => "In Revisione",
                            "02" => "Bozza",
                            "03" => "Problemi",
                            _ => "N/A"
                        };
                    }
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white bg-opacity-20 backdrop-blur">
                        <span class="w-2 h-2 rounded-full @statusClass mr-2"></span>
                        @statusText
                    </span>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="flex items-center space-x-3">
                @if (Model.Data.HasValue)
                {
                    <div class="text-sm">
                        <i class="fas fa-calendar mr-1"></i>
                        @Model.Data.Value.ToString("dd/MM/yyyy")
                    </div>
                }
                <button type="button" onclick="printTable()" 
                        class="bg-white bg-opacity-20 hover:bg-white hover:bg-opacity-30 border border-white border-opacity-30 px-3 py-1 rounded transition-all duration-200">
                    <i class="fas fa-print"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Main Content with Tabs -->
<div class="bg-white rounded-lg shadow-sm border border-neutral-200">
    <!-- Tabs Navigation -->
    <div class="border-b border-gray-200">
        <nav class="flex -mb-px" aria-label="Tabs">
            <button onclick="switchTab('general')" 
                    class="tab-button active px-6 py-3 border-b-2 border-ardec-primary text-sm font-medium text-ardec-primary">
                <i class="fas fa-info-circle mr-2"></i>Generale
            </button>
            <button onclick="switchTab('descriptions-it')" 
                    class="tab-button px-6 py-3 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 text-sm font-medium">
                <i class="fas fa-align-left mr-2"></i>Descrizioni IT
            </button>
            <button onclick="switchTab('descriptions-intl')" 
                    class="tab-button px-6 py-3 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 text-sm font-medium">
                <i class="fas fa-globe mr-2"></i>Descrizioni INT
            </button>
            <button onclick="switchTab('technical')" 
                    class="tab-button px-6 py-3 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 text-sm font-medium">
                <i class="fas fa-cogs mr-2"></i>Dati Tecnici
            </button>
            <button onclick="switchTab('modifications')" 
                    class="tab-button px-6 py-3 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 text-sm font-medium">
                <i class="fas fa-history mr-2"></i>Modifiche
            </button>
        </nav>
    </div>
    
    <div class="p-6">
        <!-- General Tab -->
        <div id="tab-general" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Column 1: Base Info -->
                <div class="space-y-4">
                    <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wider">Informazioni Base</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <label class="block text-xs font-medium text-gray-500 mb-1">Tavola</label>
                            <div class="text-sm font-mono bg-gray-50 px-3 py-2 rounded-lg">@Model.Tavola</div>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(Model.CodiceTecnico))
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">Cod. Tecnico</label>
                                <div class="text-sm font-mono bg-gray-50 px-3 py-2 rounded-lg">@Model.CodiceTecnico</div>
                            </div>
                        }
                        
                        @if (Model.nTavola.HasValue)
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">Numero Tavola</label>
                                <div class="text-sm bg-gray-50 px-3 py-2 rounded-lg">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        @Model.nTavola.Value.ToString("F0")
                                    </span>
                                </div>
                            </div>
                        }
                    </div>
                </div>
                
                <!-- Column 2: S1000D Info -->
                <div class="space-y-4">
                    <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wider">S1000D</h3>
                    
                    <div class="space-y-3">
                        @if (!string.IsNullOrEmpty(Model.DMC))
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">DMC</label>
                                <div class="text-sm font-mono bg-gray-50 px-3 py-2 rounded-lg text-ardec-primary">@Model.DMC</div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.TECHNAME))
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">Techname</label>
                                <div class="text-sm bg-gray-50 px-3 py-2 rounded-lg">@Model.TECHNAME</div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.INFONAME))
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">Infoname</label>
                                <div class="text-sm bg-gray-50 px-3 py-2 rounded-lg">@Model.INFONAME</div>
                            </div>
                        }
                    </div>
                </div>
                
                <!-- Column 3: Media & Links -->
                <div class="space-y-4">
                    <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wider">Media & Collegamenti</h3>
                    
                    <div class="space-y-3">
                        @if (!string.IsNullOrEmpty(Model.Logo))
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">Logo</label>
                                <div class="text-sm font-mono bg-gray-50 px-3 py-2 rounded-lg">
                                    <i class="fas fa-image text-blue-500 mr-1"></i>@Model.Logo
                                </div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.Figura))
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">Figura</label>
                                <div class="text-sm font-mono bg-gray-50 px-3 py-2 rounded-lg">
                                    <i class="fas fa-image text-green-500 mr-1"></i>@Model.Figura
                                </div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.TER))
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">Catalogo</label>
                                <div class="text-sm bg-gray-50 px-3 py-2 rounded-lg">
                                    <a href="/Catalogs/Details/@Model.TER" class="text-ardec-primary hover:text-ardec-primary-dark">
                                        <i class="fas fa-book mr-1"></i>@Model.TER
                                    </a>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Italian Descriptions Tab -->
        <div id="tab-descriptions-it" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                @if (!string.IsNullOrEmpty(Model.Descrizione1IT))
                {
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-semibold text-gray-900">ITA 1</h4>
                            <span class="text-xs text-gray-500">Descrizione Primaria</span>
                        </div>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <p class="text-sm text-gray-900 whitespace-pre-wrap">@Model.Descrizione1IT</p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.Descrizione2IT))
                {
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-semibold text-gray-900">ITA 2</h4>
                            <span class="text-xs text-gray-500">Descrizione Secondaria</span>
                        </div>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <p class="text-sm text-gray-900 whitespace-pre-wrap">@Model.Descrizione2IT</p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.Descrizione3IT))
                {
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-semibold text-gray-900">ITA 3</h4>
                            <span class="text-xs text-gray-500">Descrizione Terziaria</span>
                        </div>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <p class="text-sm text-gray-900 whitespace-pre-wrap">@Model.Descrizione3IT</p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.GruppoITA))
                {
                    <div class="lg:col-span-3 mt-4">
                        <h4 class="text-sm font-semibold text-gray-900 mb-2">Gruppo ITA</h4>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <p class="text-sm font-mono">@Model.GruppoITA</p>
                        </div>
                    </div>
                }
            </div>
        </div>
        
        <!-- International Descriptions Tab -->
        <div id="tab-descriptions-intl" class="tab-content hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @if (!string.IsNullOrEmpty(Model.GruppoENG))
                {
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">🇬🇧</span>
                            <h4 class="text-sm font-semibold text-gray-900">English</h4>
                        </div>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                            <p class="text-sm font-mono">@Model.GruppoENG</p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.GruppoFRA))
                {
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">🇫🇷</span>
                            <h4 class="text-sm font-semibold text-gray-900">Français</h4>
                        </div>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                            <p class="text-sm font-mono">@Model.GruppoFRA</p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.GruppoPOR))
                {
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">🇵🇹</span>
                            <h4 class="text-sm font-semibold text-gray-900">Português</h4>
                        </div>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                            <p class="text-sm font-mono">@Model.GruppoPOR</p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.GruppoESP))
                {
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">🇪🇸</span>
                            <h4 class="text-sm font-semibold text-gray-900">Español</h4>
                        </div>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                            <p class="text-sm font-mono">@Model.GruppoESP</p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.GruppoTED))
                {
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">🇩🇪</span>
                            <h4 class="text-sm font-semibold text-gray-900">Deutsch</h4>
                        </div>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                            <p class="text-sm font-mono">@Model.GruppoTED</p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.GruppoUSA))
                {
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">🇺🇸</span>
                            <h4 class="text-sm font-semibold text-gray-900">USA</h4>
                        </div>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                            <p class="text-sm font-mono">@Model.GruppoUSA</p>
                        </div>
                    </div>
                }
            </div>
        </div>
        
        <!-- Technical Data Tab -->
        <div id="tab-technical" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- S1000D Column -->
                <div class="space-y-4">
                    <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wider border-b pb-2">S1000D Data</h3>
                    
                    <div class="grid grid-cols-2 gap-3">
                        @if (!string.IsNullOrEmpty(Model.SBC))
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">SBC</label>
                                <div class="text-sm font-mono bg-gray-50 px-3 py-2 rounded">@Model.SBC</div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.SNS))
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">SNS</label>
                                <div class="text-sm font-mono bg-gray-50 px-3 py-2 rounded">@Model.SNS</div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.ICN_TITLE))
                        {
                            <div class="col-span-2">
                                <label class="block text-xs font-medium text-gray-500 mb-1">ICN Title</label>
                                <div class="text-sm bg-gray-50 px-3 py-2 rounded">@Model.ICN_TITLE</div>
                            </div>
                        }
                    </div>
                </div>
                
                <!-- CED & Pagination Column -->
                <div class="space-y-4">
                    <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wider border-b pb-2">CED & Paginazione</h3>
                    
                    <div class="grid grid-cols-2 gap-3">
                        @if (Model.CEDNPagina.HasValue)
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">CED N. Pagina</label>
                                <div class="text-sm bg-gray-50 px-3 py-2 rounded">@Model.CEDNPagina.Value</div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.CEDIGNPagina))
                        {
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">CED IGN Pagina</label>
                                <div class="text-sm bg-gray-50 px-3 py-2 rounded">@Model.CEDIGNPagina</div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Modifications Tab -->
        <div id="tab-modifications" class="tab-content hidden">
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Modifica Tavola -->
                    <div class="border rounded-lg p-4 @(Model.ModificaTavola ? "bg-green-50 border-green-200" : "bg-gray-50 border-gray-200")">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-sm font-semibold text-gray-900">Modifica Tavola</h4>
                            @if (Model.ModificaTavola)
                            {
                                <i class="fas fa-check-circle text-green-500"></i>
                            }
                            else
                            {
                                <i class="fas fa-times-circle text-gray-400"></i>
                            }
                        </div>
                        @if (Model.DataModificaTavola.HasValue)
                        {
                            <p class="text-sm text-gray-600">
                                <i class="fas fa-calendar mr-1"></i>
                                @Model.DataModificaTavola.Value.ToString("dd/MM/yyyy")
                            </p>
                        }
                        else
                        {
                            <p class="text-sm text-gray-500">Nessuna modifica</p>
                        }
                    </div>
                    
                    <!-- Modifica Testo -->
                    <div class="border rounded-lg p-4 @(Model.ModificaTesto ? "bg-green-50 border-green-200" : "bg-gray-50 border-gray-200")">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-sm font-semibold text-gray-900">Modifica Testo</h4>
                            @if (Model.ModificaTesto)
                            {
                                <i class="fas fa-check-circle text-green-500"></i>
                            }
                            else
                            {
                                <i class="fas fa-times-circle text-gray-400"></i>
                            }
                        </div>
                        @if (Model.DataModificaTesto.HasValue)
                        {
                            <p class="text-sm text-gray-600">
                                <i class="fas fa-calendar mr-1"></i>
                                @Model.DataModificaTesto.Value.ToString("dd/MM/yyyy")
                            </p>
                        }
                        else
                        {
                            <p class="text-sm text-gray-500">Nessuna modifica</p>
                        }
                    </div>
                    
                    <!-- Non Forniti -->
                    @if (Model.NonForniti.HasValue)
                    {
                        <div class="border rounded-lg p-4 @(Model.NonForniti.Value > 0 ? "bg-yellow-50 border-yellow-200" : "bg-green-50 border-green-200")">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-sm font-semibold text-gray-900">Non Forniti</h4>
                                @if (Model.NonForniti.Value > 0)
                                {
                                    <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                                }
                                else
                                {
                                    <i class="fas fa-check-circle text-green-500"></i>
                                }
                            </div>
                            <p class="text-sm @(Model.NonForniti.Value > 0 ? "text-yellow-700" : "text-green-700")">
                                @(Model.NonForniti.Value > 0 ? $"{Model.NonForniti.Value} elementi non forniti" : "Tutti gli elementi forniti")
                            </p>
                        </div>
                    }
                </div>
                
                <!-- Note -->
                @if (!string.IsNullOrEmpty(Model.Note))
                {
                    <div class="mt-6">
                        <h4 class="text-sm font-semibold text-gray-900 mb-2">
                            <i class="fas fa-sticky-note mr-1 text-ardec-primary"></i>Note
                        </h4>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <p class="text-sm text-gray-900 whitespace-pre-wrap">@Model.Note</p>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_ToastContainer")

@section Scripts {
    <script>
        function switchTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('border-ardec-primary', 'text-ardec-primary', 'active');
                btn.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Show selected tab
            const selectedTab = document.getElementById('tab-' + tabName);
            if (selectedTab) {
                selectedTab.classList.remove('hidden');
            }
            
            // Add active class to clicked button
            event.target.closest('.tab-button').classList.add('border-ardec-primary', 'text-ardec-primary', 'active');
            event.target.closest('.tab-button').classList.remove('border-transparent', 'text-gray-500');
        }
        
        function changeVersion(version) {
            // Get current URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const tavola = urlParams.get('tavola') || '@Model.Tavola';
            const ter = urlParams.get('ter') || '@Model.TER';
            
            // Redirect to new version
            window.location.href = `/Tables/Details?tavola=${tavola}&ter=${ter}&version=${version}`;
        }
        
        function exportTable() {
            const tavola = '@Model.Tavola';
            const version = '@Model.Versione';
            
            console.log(`Exporting table ${tavola} version ${version}`);
            // Implement export logic
            showToast('Info', `Esportazione tavola ${tavola} v${version} in corso...`, 'info');
        }
        
        function printTable() {
            window.print();
        }
        
        function showToast(title, message, type = 'info') {
            // Reuse toast function from main layout
            if (window.parent && window.parent.showToast) {
                window.parent.showToast(title, message, type);
            } else {
                console.log(`${type}: ${title} - ${message}`);
            }
        }
        
        $(document).ready(function() {
            console.log('✅ Enhanced Table Details view initialized');
        });
    </script>
    
    <style>
        @@media print {
            .tab-content.hidden {
                display: block !important;
            }
            .tab-button {
                display: none;
            }
            nav, .toolbar {
                display: none;
            }
        }
    </style>
}
