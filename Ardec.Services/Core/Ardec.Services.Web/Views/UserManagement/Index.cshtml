@model Ardec.Services.Web.Features.UserManagement.UserListViewModel
@{
    ViewData["Title"] = "Gestione Utenti - MIL-STD-1388 Services";
    ViewBag.ToolbarTitle = "Gestione Utenti";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
}

@{
    ViewBag.ToolbarTitle = "Gestione Utenti";
    ViewBag.ExtraButtons = $@"<button type=""button"" class=""bg-ardec-primary hover:bg-ardec-primary-dark text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm"" onclick=""window.location.href='{Url.Action("Create")}'"">" +
        "<i class=\"fas fa-user-plus mr-2\"></i>Nuovo Utente" +
    "</button>" +
    "<button type=\"button\" class=\"bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium ml-2\" onclick=\"window.location.href='" + Url.Action("Statistics") + "'\">" +
        "<i class=\"fas fa-chart-bar mr-2\"></i>Statistiche" +
    "</button>" +
    "<button type=\"button\" class=\"bg-white border border-neutral-300 text-neutral-600 hover:bg-neutral-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium ml-2\" onclick=\"window.location.href='" + Url.Action("Roles") + "'\">" +
        "<i class=\"fas fa-shield-alt mr-2\"></i>Ruoli" +
    "</button>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Users Table Card -->
<div class="bg-white rounded-lg shadow-sm border border-neutral-200">
    <div class="p-6">
        <!-- Search and Filter Controls -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center pb-6 bg-white gap-4">
            <div class="flex flex-col md:flex-row md:items-center md:space-x-4 w-full">
                <!-- Search Input -->
                <label for="table-search" class="sr-only">Search</label>
                <div class="relative mb-4 md:mb-0">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                        </svg>
                    </div>
                    <input type="text" id="table-search" class="block w-full md:w-80 py-2.5 pl-4 pr-12 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 placeholder-gray-500" placeholder="Cerca utenti..." value="@Model.SearchTerm">
                </div>
                
                <!-- Role Filter Dropdown -->
                <div class="relative">
                    <button id="roleFilterButton" data-dropdown-toggle="roleFilterDropdown" class="inline-flex items-center justify-center py-2.5 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200" type="button">
                        <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="h-4 w-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
                        </svg>
                        @(string.IsNullOrEmpty(Model.SelectedRole) ? "Tutti i ruoli" : Model.SelectedRole)
                        <svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                            <path clip-rule="evenodd" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                        </svg>
                    </button>
                    
                    <!-- Dropdown menu -->
                    <div id="roleFilterDropdown" class="absolute top-full left-0 z-50 hidden w-48 mt-1 p-3 bg-white rounded-lg shadow-lg border border-gray-200">
                        <h6 class="mb-3 text-sm font-medium text-gray-900">Filtra per ruolo</h6>
                        <ul class="space-y-2 text-sm" aria-labelledby="roleFilterButton">
                            <li>
                                <button onclick="filterByRole('')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded @(string.IsNullOrEmpty(Model.SelectedRole) ? "bg-gray-100 font-medium" : "")">
                                    Tutti i ruoli
                                </button>
                            </li>
                            @foreach (var role in Model.AvailableRoles)
                            {
                                <li>
                                    <button onclick="filterByRole('@role.Value')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded @(role.Value == Model.SelectedRole ? "bg-gray-100 font-medium" : "")">
                                        @role.Text
                                    </button>
                                </li>
                            }
                        </ul>
                    </div>
                </div>

                <!-- Page Size Selector -->
                <div class="relative">
                    <select id="page-size" class="py-2.5 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 focus:z-10 focus:ring-4 focus:ring-gray-200">
                        <option value="10" selected="@(Model.PageSize == 10)">10 per pagina</option>
                        <option value="25" selected="@(Model.PageSize == 25)">25 per pagina</option>
                        <option value="50" selected="@(Model.PageSize == 50)">50 per pagina</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-gradient-to-r from-ardec-primary to-ardec-primary-dark p-4 rounded-lg text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-90">Totale Utenti</p>
                        <p class="text-2xl font-bold">@Model.TotalUsers</p>
                    </div>
                    <i class="fas fa-users text-2xl opacity-75"></i>
                </div>
            </div>
            <div class="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-lg text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-90">Utenti Attivi</p>
                        <p class="text-2xl font-bold">@Model.Users.Count(u => !u.IsLockedOut)</p>
                    </div>
                    <i class="fas fa-check-circle text-2xl opacity-75"></i>
                </div>
            </div>
            <div class="bg-gradient-to-r from-orange-500 to-orange-600 p-4 rounded-lg text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-90">Utenti Bloccati</p>
                        <p class="text-2xl font-bold">@Model.Users.Count(u => u.IsLockedOut)</p>
                    </div>
                    <i class="fas fa-lock text-2xl opacity-75"></i>
                </div>
            </div>
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-lg text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-90">Email Non Confermate</p>
                        <p class="text-2xl font-bold">@Model.Users.Count(u => !u.EmailConfirmed)</p>
                    </div>
                    <i class="fas fa-envelope text-2xl opacity-75"></i>
                </div>
            </div>
        </div>

        <!-- DataTable -->
        <div class="overflow-x-auto shadow-sm ring-1 ring-black ring-opacity-5 rounded-lg">
            <table id="usersTable" class="w-full table-auto text-sm text-left text-gray-500 divide-y divide-gray-300">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 divide-y divide-gray-300">
                    <tr>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(0)">
                            <div class="flex items-center justify-between">
                                <span>Utente</span>
                                <svg class="w-3 h-3 opacity-30 hover:opacity-100 transition-opacity" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z"/>
                                </svg>
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(1)">
                            <div class="flex items-center justify-between">
                                <span>Ruoli</span>
                                <svg class="w-3 h-3 opacity-30 hover:opacity-100 transition-opacity" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z"/>
                                </svg>
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(2)">
                            <div class="flex items-center justify-between">
                                <span>Stato</span>
                                <svg class="w-3 h-3 opacity-30 hover:opacity-100 transition-opacity" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.50a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z"/>
                                </svg>
                            </div>
                        </th>
                        <th scope="col" class="w-32 px-6 py-3 text-center whitespace-nowrap">Ultimo Accesso</th>
                        <th scope="col" class="w-32 px-6 py-3 text-center">
                            <span class="sr-only">Azioni</span>
                        </th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    @foreach (var user in Model.Users)
                    {
                        <tr class="bg-white border-b hover:bg-gray-50" data-user-id="@user.Id" data-email="@user.Email">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 w-10 h-10">
                                        <div class="w-10 h-10 bg-ardec-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user text-ardec-primary"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="font-medium text-gray-900">@user.UserName</div>
                                        <div class="text-sm text-gray-500">@user.Email</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                @if (user.Roles.Any())
                                {
                                    <div class="flex flex-wrap gap-1">
                                        @foreach (var role in user.Roles)
                                        {
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetRoleBadgeClasses(role)">
                                                @role
                                            </span>
                                        }
                                    </div>
                                }
                                else
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Nessun ruolo
                                    </span>
                                }
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-col space-y-1">
                                    @if (user.IsLockedOut)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-lock mr-1"></i>
                                            Bloccato
                                        </span>
                                        @if (user.LockoutEnd.HasValue)
                                        {
                                            <span class="text-xs text-gray-500">Fino: @user.LockoutEnd.Value.ToString("dd/MM/yyyy HH:mm")</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>
                                            Attivo
                                        </span>
                                    }
                                    
                                    @if (!user.EmailConfirmed)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-envelope mr-1"></i>
                                            Email non confermata
                                        </span>
                                    }
                                    
                                    @if (user.AccessFailedCount > 0)
                                    {
                                        <span class="text-xs text-orange-600">Tentativi falliti: @user.AccessFailedCount</span>
                                    }
                                </div>
                            </td>
                            <td class="px-6 py-4 text-center">
                                @if (user.LastLoginDate.HasValue)
                                {
                                    <div class="text-sm text-gray-900">@user.LastLoginDate.Value.ToString("dd/MM/yyyy")</div>
                                    <div class="text-xs text-gray-500">@user.LastLoginDate.Value.ToString("HH:mm")</div>
                                }
                                else
                                {
                                    <span class="text-sm text-gray-400">Mai effettuato</span>
                                }
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex justify-center">
                                    <button id="<EMAIL>" data-dropdown-toggle="<EMAIL>" class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200" type="button">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    
                                    <!-- Dropdown menu -->
                                    <div id="<EMAIL>" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44">
                                        <ul class="py-2 text-sm text-gray-700" aria-labelledby="<EMAIL>">
                                            <li>
                                                <a href="@Url.Action("Details", new { id = user.Id })" class="block px-4 py-2 hover:bg-gray-100">
                                                    <i class="fas fa-eye mr-2 text-ardec-primary"></i>
                                                    Visualizza
                                                </a>
                                            </li>
                                            <li>
                                                <a href="@Url.Action("Edit", new { id = user.Id })" class="block px-4 py-2 hover:bg-gray-100">
                                                    <i class="fas fa-edit mr-2 text-blue-600"></i>
                                                    Modifica
                                                </a>
                                            </li>
                                        </ul>
                                        <div class="py-2">
                                            <button onclick="toggleLockout('@user.Id', '@user.Email', @user.IsLockedOut.ToString().ToLower())" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                @if (user.IsLockedOut)
                                                {
                                                    <i class="fas fa-unlock mr-2 text-green-600"></i>
                                                    <span>Sblocca</span>
                                                }
                                                else
                                                {
                                                    <i class="fas fa-lock mr-2 text-orange-600"></i>
                                                    <span>Blocca</span>
                                                }
                                            </button>
                                            <button onclick="resetPassword('@user.Id', '@user.Email')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="fas fa-key mr-2 text-blue-600"></i>
                                                Reset Password
                                            </button>
                                        </div>
                                        @if (user.Email != "<EMAIL>")
                                        {
                                            <div class="py-2">
                                                <button onclick="deleteUser('@user.Id', '@user.Email')" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                                    <i class="fas fa-trash mr-2"></i>
                                                    Elimina
                                                </button>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>

        @if (!Model.Users.Any())
        {
            <div class="text-center py-16">
                <div class="mx-auto h-24 w-24 text-gray-400">
                    <i class="fas fa-users text-6xl"></i>
                </div>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Nessun utente trovato</h3>
                <p class="mt-1 text-sm text-gray-500">Inizia creando il tuo primo utente.</p>
                <div class="mt-6">
                    <a href="@Url.Action("Create")" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-ardec-primary hover:bg-ardec-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ardec-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Nuovo Utente
                    </a>
                </div>
            </div>
        }
    </div>
    
    <!-- Footer with pagination and info -->
    @if (Model.TotalPages > 1)
    {
        <div class="px-6 py-4 border-t border-neutral-200 bg-neutral-50 rounded-b-lg">
            <!-- Pagination -->
            <nav class="flex items-center justify-between">
                <div class="flex flex-1 justify-between sm:hidden">
                    @if (Model.CurrentPage > 1)
                    {
                        <a href="@GetPageUrl(Model.CurrentPage - 1)" class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Precedente
                        </a>
                    }
                    @if (Model.CurrentPage < Model.TotalPages)
                    {
                        <a href="@GetPageUrl(Model.CurrentPage + 1)" class="relative ml-3 inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Successivo
                        </a>
                    }
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Visualizzati <span class="font-medium">@((Model.CurrentPage - 1) * Model.PageSize + 1)</span> - <span class="font-medium">@Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalUsers)</span> di <span class="font-medium">@Model.TotalUsers</span> utenti
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            @if (Model.CurrentPage > 1)
                            {
                                <a href="@GetPageUrl(Model.CurrentPage - 1)" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            }

                            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                            {
                                @if (i == Model.CurrentPage)
                                {
                                    <span aria-current="page" class="z-10 bg-ardec-primary border-ardec-primary text-white relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        @i
                                    </span>
                                }
                                else
                                {
                                    <a href="@GetPageUrl(i)" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        @i
                                    </a>
                                }
                            }

                            @if (Model.CurrentPage < Model.TotalPages)
                            {
                                <a href="@GetPageUrl(Model.CurrentPage + 1)" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            }
                        </nav>
                    </div>
                </div>
            </nav>
        </div>
    }
    else if (Model.Users.Any())
    {
        <div class="px-6 py-4 border-t border-neutral-200 bg-neutral-50 rounded-b-lg">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 text-sm text-neutral-600">
                <span class="flex items-center">
                    <i class="fas fa-users mr-2 text-ardec-primary"></i>
                    Totale: <strong class="mx-2">@Model.TotalUsers</strong> utenti
                </span>
                <span class="flex items-center">
                    <i class="fas fa-clock mr-2 text-neutral-400"></i>
                    Aggiornato: <span class="ml-1 font-medium">@DateTime.Now.ToString("dd/MM/yyyy HH:mm")</span>
                </span>
            </div>
        </div>
    }
</div>

<!-- Reset Password Modal -->
<div id="resetPasswordModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow">
            <div class="flex items-start justify-between p-4 border-b rounded-t">
                <h3 class="text-xl font-semibold text-gray-900">
                    Reset Password Utente
                </h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center" data-modal-hide="resetPasswordModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 space-y-6">
                <form id="resetPasswordForm">
                    <input type="hidden" id="resetPasswordUserId" />
                    <div class="mb-4">
                        <p class="text-sm text-gray-700">Stai per reimpostare la password per l'utente: <strong id="resetPasswordUserEmail" class="text-ardec-primary"></strong></p>
                    </div>
                    <div class="mb-4">
                        <label for="newPassword" class="block mb-2 text-sm font-medium text-gray-900">Nuova Password</label>
                        <input type="password" id="newPassword" name="NewPassword" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" required minlength="6" />
                    </div>
                    <div class="mb-4">
                        <label for="confirmNewPassword" class="block mb-2 text-sm font-medium text-gray-900">Conferma Nuova Password</label>
                        <input type="password" id="confirmNewPassword" name="ConfirmNewPassword" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" required />
                    </div>
                    <div class="flex items-center mb-4">
                        <input id="forcePasswordChange" name="ForcePasswordChange" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" />
                        <label for="forcePasswordChange" class="ml-2 text-sm font-medium text-gray-900">Richiedi cambio password al prossimo accesso</label>
                    </div>
                </form>
            </div>
            <div class="flex items-center p-6 space-x-2 border-t border-gray-200 rounded-b">
                <button type="button" onclick="submitPasswordReset()" class="text-white bg-ardec-primary hover:bg-ardec-primary-dark focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">
                    <i class="fas fa-key mr-1"></i>
                    Reset Password
                </button>
                <button data-modal-hide="resetPasswordModal" type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10">
                    Annulla
                </button>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_ToastContainer")

@functions {
    string GetRoleBadgeClasses(string role) => role switch
    {
        "Admin" => "bg-red-100 text-red-800",
        "PowerUser" => "bg-orange-100 text-orange-800", 
        "Editor" => "bg-blue-100 text-blue-800",
        "Viewer" => "bg-gray-100 text-gray-800",
        _ => "bg-purple-100 text-purple-800"
    };

    string GetPageUrl(int page)
    {
        var routeValues = new Dictionary<string, object?>
        {
            ["page"] = page,
            ["pageSize"] = Model.PageSize
        };

        if (!string.IsNullOrEmpty(Model.SearchTerm))
            routeValues["search"] = Model.SearchTerm;

        if (!string.IsNullOrEmpty(Model.SelectedRole))
            routeValues["role"] = Model.SelectedRole;

        return Url.Action("Index", routeValues)!;
    }
}

@section Scripts {
    <script>
        let usersData = [];
        let currentSortColumn = 0;
        let sortDirection = 'asc';

        $(document).ready(function() {
            // Initialize Flowbite components
            setTimeout(() => {
                if (window.Flowbite && typeof window.Flowbite.init === 'function') {
                    window.Flowbite.init();
                    console.log('✅ Flowbite initialized successfully');
                } else {
                    console.warn('⚠️ Flowbite not available, using fallback functionality');
                    setupFallbackDropdowns();
                }
            }, 100);

            // Store original data for filtering/sorting
            usersData = Array.from($('#usersTable tbody tr')).map(row => {
                return {
                    element: row,
                    data: Array.from(row.cells).map(cell => cell.textContent.trim()),
                    userId: $(row).data('user-id'),
                    email: $(row).data('email')
                };
            });

            console.log(`Initialized ${usersData.length} user rows`);

            // Setup search functionality
            setupSearch();
            
            // Setup page size change
            setupPageSize();

            console.log('✅ User Management DataTable initialized successfully!');
        });

        // Search functionality
        function setupSearch() {
            const searchInput = document.getElementById('table-search');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    filterTable(searchTerm);
                });
            }
        }

        // Page size functionality  
        function setupPageSize() {
            const pageSize = document.getElementById('page-size');
            if (pageSize) {
                pageSize.addEventListener('change', function() {
                    const newPageSize = this.value;
                    const url = new URL(window.location);
                    url.searchParams.set('pageSize', newPageSize);
                    url.searchParams.set('page', '1'); // Reset to first page
                    window.location.href = url.toString();
                });
            }
        }

        // Fallback dropdown functionality if Flowbite fails
        function setupFallbackDropdowns() {
            const roleButton = document.getElementById('roleFilterButton');
            const roleDropdown = document.getElementById('roleFilterDropdown');
            
            if (roleButton && roleDropdown) {
                roleButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    roleDropdown.classList.toggle('hidden');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!roleButton.contains(e.target) && !roleDropdown.contains(e.target)) {
                        roleDropdown.classList.add('hidden');
                    }
                });
            }

            // Setup individual user action dropdowns
            $('[id^="userActionsButton-"]').each(function() {
                const button = this;
                const dropdownId = button.getAttribute('data-dropdown-toggle');
                const dropdown = document.getElementById(dropdownId);
                
                if (dropdown) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        dropdown.classList.toggle('hidden');
                    });
                }
            });
        }

        // Filter by role
        function filterByRole(role) {
            const url = new URL(window.location);
            if (role) {
                url.searchParams.set('role', role);
            } else {
                url.searchParams.delete('role');
            }
            url.searchParams.set('page', '1'); // Reset to first page
            window.location.href = url.toString();
        }

        // Filter table rows based on search term
        function filterTable(searchTerm) {
            usersData.forEach(item => {
                const rowText = item.data.join(' ').toLowerCase();
                const shouldShow = !searchTerm || rowText.includes(searchTerm);
                
                if (shouldShow) {
                    item.element.style.display = '';
                    $(item.element).removeClass('hidden');
                } else {
                    item.element.style.display = 'none';
                    $(item.element).addClass('hidden');
                }
            });
        }

        // Sort table by column
        function sortTable(columnIndex) {
            // Toggle sort direction if same column
            if (currentSortColumn === columnIndex) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortDirection = 'asc';
                currentSortColumn = columnIndex;
            }

            // Sort the data
            usersData.sort((a, b) => {
                let aVal = a.data[columnIndex].toLowerCase();
                let bVal = b.data[columnIndex].toLowerCase();
                
                // Handle numeric values for sorting
                if (!isNaN(aVal) && !isNaN(bVal)) {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                    return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
                }
                
                // Text sorting
                if (sortDirection === 'asc') {
                    return aVal.localeCompare(bVal);
                } else {
                    return bVal.localeCompare(aVal);
                }
            });

            // Re-render the table body
            const tbody = document.querySelector('#usersTable tbody');
            tbody.innerHTML = '';
            usersData.forEach(item => {
                tbody.appendChild(item.element);
            });

            console.log(`Sorted by column ${columnIndex} (${sortDirection})`);
        }

        // Toggle Lockout User
        async function toggleLockout(userId, userEmail, isCurrentlyLocked) {
            const action = isCurrentlyLocked ? 'sbloccare' : 'bloccare';
            
            if (!confirm(`Sei sicuro di voler ${action} l'utente ${userEmail}?`)) {
                return;
            }

            try {
                const token = $('input[name="__RequestVerificationToken"]').val();
                const response = await fetch(`/UserManagement/api/users/${userId}/toggle-lockout`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showFlowbiteToast('Successo', result.message, 'success');
                    location.reload();
                } else {
                    showFlowbiteToast('Errore', result.message, 'error');
                }
            } catch (error) {
                console.error('Error toggling lockout:', error);
                showFlowbiteToast('Errore', 'Errore di connessione', 'error');
            }
        }

        // Delete User
        async function deleteUser(userId, userEmail) {
            if (!confirm(`Sei sicuro di voler eliminare l'utente ${userEmail}?\n\nQuesta azione non può essere annullata!`)) {
                return;
            }

            try {
                const token = $('input[name="__RequestVerificationToken"]').val();
                const response = await fetch(`/UserManagement/api/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showFlowbiteToast('Successo', result.message, 'success');
                    location.reload();
                } else {
                    showFlowbiteToast('Errore', result.message, 'error');
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                showFlowbiteToast('Errore', 'Errore di connessione', 'error');
            }
        }

        // Reset Password
        function resetPassword(userId, userEmail) {
            document.getElementById('resetPasswordUserId').value = userId;
            document.getElementById('resetPasswordUserEmail').textContent = userEmail;
            document.getElementById('resetPasswordForm').reset();
            
            // Show modal using Flowbite or fallback
            const modal = new window.Modal(document.getElementById('resetPasswordModal'));
            modal.show();
        }

        // Submit Password Reset
        async function submitPasswordReset() {
            const form = document.getElementById('resetPasswordForm');
            const formData = new FormData(form);
            const userId = document.getElementById('resetPasswordUserId').value;
            const newPassword = formData.get('NewPassword');
            const confirmPassword = formData.get('ConfirmNewPassword');
            
            if (newPassword !== confirmPassword) {
                showFlowbiteToast('Errore', 'Le password non coincidono', 'error');
                return;
            }
            
            try {
                const token = $('input[name="__RequestVerificationToken"]').val();
                const response = await fetch(`/UserManagement/api/users/${userId}/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    },
                    body: JSON.stringify({
                        NewPassword: newPassword,
                        ConfirmNewPassword: confirmPassword,
                        ForcePasswordChange: formData.get('ForcePasswordChange') === 'on'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showFlowbiteToast('Successo', result.message, 'success');
                    const modal = window.Modal.getInstance(document.getElementById('resetPasswordModal'));
                    modal.hide();
                } else {
                    showFlowbiteToast('Errore', result.message, 'error');
                }
            } catch (error) {
                console.error('Error resetting password:', error);
                showFlowbiteToast('Errore', 'Errore di connessione', 'error');
            }
        }

        // Make functions available globally
        window.sortTable = sortTable;
        window.filterByRole = filterByRole;
        window.toggleLockout = toggleLockout;
        window.deleteUser = deleteUser;
        window.resetPassword = resetPassword;
        window.submitPasswordReset = submitPasswordReset;
    </script>
}
