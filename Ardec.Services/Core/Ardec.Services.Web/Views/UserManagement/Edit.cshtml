@model Ardec.Services.Web.Features.UserManagement.EditUserViewModel
@{
    ViewData["Title"] = $"Modifica Utente: {Model.UserName} - MIL-STD-1388 Services";
    ViewBag.ToolbarTitle = $"Modifica Utente: {Model.UserName}";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
}

@{
    ViewBag.ExtraButtons = $"<a href=\"{Url.Action("Details", new { id = Model.Id })}\" class=\"bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium\">" +
        "<i class=\"fas fa-eye mr-2\"></i>Visualizza" +
    "</a>" +
    $"<a href=\"{Url.Action("Index")}\" class=\"bg-white border border-neutral-300 text-neutral-600 hover:bg-neutral-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium\">" +
        "<i class=\"fas fa-arrow-left mr-2\"></i>Torna alla Lista" +
    "</a>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Edit User Form -->
<div class="max-w-4xl mx-auto">
    <form asp-action="Edit" method="post" id="editUserForm" class="space-y-6">
        @Html.AntiForgeryToken()
        <input asp-for="Id" type="hidden" />
        <input asp-for="@string.Join(",", Model.CurrentRoles)" name="CurrentRolesHidden" type="hidden" />
        
        <!-- User Status Alert -->
        @if (Model.IsLockedOut)
        {
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Utente Bloccato</h3>
                        <div class="mt-2 text-sm text-red-700">
                            @if (Model.LockoutEnd.HasValue)
                            {
                                <p>Questo utente è bloccato fino al @Model.LockoutEnd.Value.ToString("dd/MM/yyyy HH:mm")</p>
                            }
                            else
                            {
                                <p>Questo utente è attualmente bloccato</p>
                            }
                            @if (Model.AccessFailedCount > 0)
                            {
                                <p>Tentativi di accesso falliti: @Model.AccessFailedCount</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        
        @if (!Model.EmailConfirmed)
        {
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-envelope text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Email Non Confermata</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>L'indirizzo email di questo utente non è stato ancora confermato.</p>
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Main Information Card -->
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-user-edit mr-2 text-ardec-primary"></i>
                    Informazioni Utente
                </h3>
                <p class="mt-1 text-sm text-gray-600">Modifica i dati dell'utente selezionato.</p>
            </div>
            
            <div class="p-6 space-y-6">
                <!-- User ID (Read-only) -->
                <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                    <div class="flex items-center">
                        <i class="fas fa-id-badge text-gray-400 mr-2"></i>
                        <span class="text-sm font-medium text-gray-600">ID Utente:</span>
                        <span class="ml-2 text-sm text-gray-900 font-mono">@Model.Id</span>
                    </div>
                </div>

                <!-- Email and Username Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label asp-for="Email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email *
                        </label>
                        <input asp-for="Email" type="email" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" required>
                        <span asp-validation-for="Email" class="text-red-600 text-sm"></span>
                    </div>
                    
                    <div>
                        <label asp-for="UserName" class="block text-sm font-medium text-gray-700 mb-2">
                            Nome Utente *
                        </label>
                        <input asp-for="UserName" type="text" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" required>
                        <span asp-validation-for="UserName" class="text-red-600 text-sm"></span>
                    </div>
                </div>
                
                <!-- Account Settings -->
                <div class="space-y-4">
                    <h4 class="text-sm font-medium text-gray-700">Impostazioni Account</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center">
                            <input asp-for="EmailConfirmed" type="checkbox" class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-gray-300 rounded">
                            <label asp-for="EmailConfirmed" class="ml-2 block text-sm text-gray-700">
                                Email confermata
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input asp-for="LockoutEnabled" type="checkbox" class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-gray-300 rounded">
                            <label asp-for="LockoutEnabled" class="ml-2 block text-sm text-gray-700">
                                Abilita blocco account
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Account Statistics -->
                @if (Model.AccessFailedCount > 0 || Model.LastLoginDate.HasValue)
                {
                    <div class="bg-blue-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-blue-800 mb-3">Statistiche Account</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            @if (Model.LastLoginDate.HasValue)
                            {
                                <div class="flex items-center">
                                    <i class="fas fa-clock text-blue-600 mr-2"></i>
                                    <span class="text-blue-700">Ultimo accesso: @Model.LastLoginDate.Value.ToString("dd/MM/yyyy HH:mm")</span>
                                </div>
                            }
                            @if (Model.AccessFailedCount > 0)
                            {
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-triangle text-orange-600 mr-2"></i>
                                    <span class="text-orange-700">Tentativi falliti: @Model.AccessFailedCount</span>
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Roles Management Card -->
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
            <div class="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-shield-alt mr-2 text-ardec-primary"></i>
                        Gestione Ruoli
                    </h3>
                    <p class="mt-1 text-sm text-gray-600">Modifica i ruoli assegnati all'utente.</p>
                </div>
                <div class="flex space-x-2">
                    <button type="button" onclick="selectAllRoles()" class="text-sm text-ardec-primary hover:text-ardec-primary-dark">
                        Seleziona Tutti
                    </button>
                    <span class="text-gray-300">|</span>
                    <button type="button" onclick="clearAllRoles()" class="text-sm text-gray-600 hover:text-gray-800">
                        Deseleziona Tutti
                    </button>
                </div>
            </div>
            
            <div class="p-6">
                <!-- Current Roles Display -->
                <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Ruoli Attuali:</h4>
                    <div class="flex flex-wrap gap-2">
                        @if (Model.CurrentRoles.Any())
                        {
                            @foreach (var role in Model.CurrentRoles)
                            {
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetRoleBadgeClasses(role)">
                                    @role
                                </span>
                            }
                        }
                        else
                        {
                            <span class="text-sm text-gray-500 italic">Nessun ruolo assegnato</span>
                        }
                    </div>
                </div>

                <!-- Role Selection -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @for (int i = 0; i < Model.AvailableRoles.Count; i++)
                    {
                        var role = Model.AvailableRoles[i];
                        <div class="relative">
                            <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ardec-primary transition-colors role-selection" data-role="@role.Value">
                                <input type="checkbox" name="SelectedRoles" value="@role.Value" 
                                       id="<EMAIL>"
                                       class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-gray-300 rounded"
                                       @(Model.SelectedRoles.Contains(role.Value) ? "checked" : "")>
                                <div class="ml-3 flex-1">
                                    <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 cursor-pointer">
                                        @role.Value
                                    </label>
                                    <p class="text-xs text-gray-500">@GetRoleDescription(role.Value)</p>
                                </div>
                                <div class="ml-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetRoleBadgeClasses(role.Value)">
                                        @role.Value
                                    </span>
                                </div>
                                @if (Model.CurrentRoles.Contains(role.Value))
                                {
                                    <div class="absolute -top-2 -right-2">
                                        <span class="inline-flex items-center justify-center w-5 h-5 bg-green-100 text-green-600 rounded-full text-xs">
                                            <i class="fas fa-check"></i>
                                        </span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
                
                @if (!Model.AvailableRoles.Any())
                {
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-circle text-3xl text-gray-400 mb-2"></i>
                        <p class="text-gray-500">Nessun ruolo disponibile nel sistema.</p>
                    </div>
                }
            </div>
        </div>

        <!-- Quick Actions Card -->
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-bolt mr-2 text-ardec-primary"></i>
                    Azioni Rapide
                </h3>
                <p class="mt-1 text-sm text-gray-600">Operazioni comuni per questo utente.</p>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button type="button" onclick="resetPassword('@Model.Id', '@Model.Email')" class="flex items-center justify-center px-4 py-3 border border-blue-300 text-blue-700 hover:bg-blue-50 rounded-lg transition-colors">
                        <i class="fas fa-key mr-2"></i>
                        Reset Password
                    </button>
                    
                    <button type="button" onclick="toggleLockout('@Model.Id', '@Model.Email', @Model.IsLockedOut.ToString().ToLower())" class="flex items-center justify-center px-4 py-3 border @(Model.IsLockedOut ? "border-green-300 text-green-700 hover:bg-green-50" : "border-orange-300 text-orange-700 hover:bg-orange-50") rounded-lg transition-colors">
                        @if (Model.IsLockedOut)
                        {
                            <i class="fas fa-unlock mr-2"></i>
                            <span>Sblocca Utente</span>
                        }
                        else
                        {
                            <i class="fas fa-lock mr-2"></i>
                            <span>Blocca Utente</span>
                        }
                    </button>
                    
                    @if (!Model.EmailConfirmed)
                    {
                        <button type="button" onclick="confirmEmail('@Model.Id', '@Model.Email')" class="flex items-center justify-center px-4 py-3 border border-green-300 text-green-700 hover:bg-green-50 rounded-lg transition-colors">
                            <i class="fas fa-envelope-check mr-2"></i>
                            Conferma Email
                        </button>
                    }
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between pt-6">
            <div>
                @if (Model.Email != "<EMAIL>")
                {
                    <button type="button" onclick="deleteUser('@Model.Id', '@Model.Email')" class="bg-red-600 hover:bg-red-700 text-white focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center">
                        <i class="fas fa-trash mr-2"></i>
                        Elimina Utente
                    </button>
                }
            </div>
            <div class="flex space-x-4">
                <a href="@Url.Action("Index")" class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center">
                    <i class="fas fa-times mr-2"></i>
                    Annulla
                </a>
                <button type="submit" class="bg-ardec-primary hover:bg-ardec-primary-dark text-white focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center">
                    <i class="fas fa-save mr-2"></i>
                    Salva Modifiche
                </button>
            </div>
        </div>
    </form>
</div>

@await Html.PartialAsync("_ToastContainer")

@functions {
    string GetRoleDescription(string role) => role switch
    {
        "Admin" => "Amministratore del sistema - Accesso completo a tutte le funzionalità",
        "PowerUser" => "Utente con privilegi avanzati - Può modificare dati ed esportare",
        "Editor" => "Editor - Può modificare parti e tavole assegnate",
        "Viewer" => "Visualizzatore - Accesso in sola lettura",
        _ => "Ruolo personalizzato"
    };
    
    string GetRoleBadgeClasses(string role) => role switch
    {
        "Admin" => "bg-red-100 text-red-800",
        "PowerUser" => "bg-orange-100 text-orange-800", 
        "Editor" => "bg-blue-100 text-blue-800",
        "Viewer" => "bg-gray-100 text-gray-800",
        _ => "bg-purple-100 text-purple-800"
    };
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize Flowbite components
            setTimeout(() => {
                if (window.Flowbite && typeof window.Flowbite.init === 'function') {
                    window.Flowbite.init();
                    console.log('✅ Flowbite initialized successfully');
                }
            }, 100);

            // Highlight role changes
            $('input[name="SelectedRoles"]').on('change', function() {
                const roleContainer = $(this).closest('.role-selection');
                const isChecked = $(this).is(':checked');
                const roleName = $(this).val();
                const wasOriginallySelected = JSON.parse('@Html.Raw(Json.Serialize(Model.CurrentRoles))').includes(roleName);
                
                // Reset styles
                roleContainer.removeClass('border-green-300 border-orange-300 bg-green-50 bg-orange-50');
                
                if (isChecked && !wasOriginallySelected) {
                    // Role being added
                    roleContainer.addClass('border-green-300 bg-green-50');
                } else if (!isChecked && wasOriginallySelected) {
                    // Role being removed
                    roleContainer.addClass('border-orange-300 bg-orange-50');
                }
            });

            // Form validation before submit
            $('#editUserForm').on('submit', function(e) {
                // Show loading state
                const submitBtn = $(this).find('button[type="submit"]');
                submitBtn.prop('disabled', true);
                submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Salvataggio in corso...');
            });

            console.log('✅ Edit User form initialized successfully!');
        });

        // Role selection helpers
        function selectAllRoles() {
            $('input[name="SelectedRoles"]').prop('checked', true).trigger('change');
        }

        function clearAllRoles() {
            $('input[name="SelectedRoles"]').prop('checked', false).trigger('change');
        }

        // Quick action functions (use the same as in Index view)
        async function toggleLockout(userId, userEmail, isCurrentlyLocked) {
            const action = isCurrentlyLocked ? 'sbloccare' : 'bloccare';
            
            if (!confirm(`Sei sicuro di voler ${action} l'utente ${userEmail}?`)) {
                return;
            }

            try {
                const token = $('input[name="__RequestVerificationToken"]').val();
                const response = await fetch(`/UserManagement/api/users/${userId}/toggle-lockout`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showFlowbiteToast('Successo', result.message, 'success');
                    location.reload();
                } else {
                    showFlowbiteToast('Errore', result.message, 'error');
                }
            } catch (error) {
                console.error('Error toggling lockout:', error);
                showFlowbiteToast('Errore', 'Errore di connessione', 'error');
            }
        }

        async function deleteUser(userId, userEmail) {
            if (!confirm(`Sei sicuro di voler eliminare l'utente ${userEmail}?\\n\\nQuesta azione non può essere annullata!`)) {
                return;
            }

            try {
                const token = $('input[name="__RequestVerificationToken"]').val();
                const response = await fetch(`/UserManagement/api/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showFlowbiteToast('Successo', result.message, 'success');
                    setTimeout(() => {
                        window.location.href = '@Url.Action("Index")';
                    }, 2000);
                } else {
                    showFlowbiteToast('Errore', result.message, 'error');
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                showFlowbiteToast('Errore', 'Errore di connessione', 'error');
            }
        }

        function resetPassword(userId, userEmail) {
            // Use the reset password function from the main page
            if (window.resetPassword) {
                window.resetPassword(userId, userEmail);
            } else {
                showFlowbiteToast('Info', 'Utilizza il pulsante Reset Password dalla lista utenti per questa funzione', 'info');
            }
        }

        async function confirmEmail(userId, userEmail) {
            if (!confirm(`Confermare l'email per l'utente ${userEmail}?`)) {
                return;
            }

            try {
                const token = $('input[name="__RequestVerificationToken"]').val();
                const response = await fetch(`/UserManagement/api/users/${userId}/confirm-email`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showFlowbiteToast('Successo', result.message, 'success');
                    location.reload();
                } else {
                    showFlowbiteToast('Errore', result.message, 'error');
                }
            } catch (error) {
                console.error('Error confirming email:', error);
                showFlowbiteToast('Errore', 'Errore di connessione', 'error');
            }
        }

        // Make functions globally available
        window.selectAllRoles = selectAllRoles;
        window.clearAllRoles = clearAllRoles;
        window.toggleLockout = toggleLockout;
        window.deleteUser = deleteUser;
        window.resetPassword = resetPassword;
        window.confirmEmail = confirmEmail;
    </script>
}
