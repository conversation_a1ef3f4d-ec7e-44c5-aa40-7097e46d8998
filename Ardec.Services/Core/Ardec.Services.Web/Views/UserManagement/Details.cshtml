@model Ardec.Services.Web.Features.UserManagement.EditUserViewModel
@{
    ViewData["Title"] = $"Dettagli Utente: {Model.UserName} - MIL-STD-1388 Services";
    ViewBag.ToolbarTitle = $"Dettagli Utente: {Model.UserName}";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
}

@{
    ViewBag.ExtraButtons = $"<a href=\"{Url.Action("Index")}\" class=\"bg-white border border-neutral-300 text-neutral-600 hover:bg-neutral-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium\">" +
        "<i class=\"fas fa-arrow-left mr-2\"></i>Torna alla Lista" +
    "</a>";
}

@await Html.PartialAsync("_Toolbar")

<!-- User Details View -->
<div class="max-w-4xl mx-auto space-y-6">
    
    <!-- User Status Alerts -->
    @if (Model.IsLockedOut)
    {
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-red-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">🔒 Utente Bloccato</h3>
                    <div class="mt-2 text-sm text-red-700">
                        @if (Model.LockoutEnd.HasValue)
                        {
                            <p>Questo utente è bloccato fino al <strong>@Model.LockoutEnd.Value.ToString("dd/MM/yyyy HH:mm")</strong></p>
                        }
                        else
                        {
                            <p>Questo utente è attualmente bloccato permanentemente</p>
                        }
                        @if (Model.AccessFailedCount > 0)
                        {
                            <p>Tentativi di accesso falliti: <strong>@Model.AccessFailedCount</strong></p>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    
    @if (!Model.EmailConfirmed)
    {
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-envelope text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">📧 Email Non Confermata</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>L'indirizzo email di questo utente non è stato ancora confermato.</p>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- User Profile Card -->
    <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
        <div class="px-6 py-4 border-b border-neutral-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-16 h-16 bg-gradient-to-r from-ardec-primary to-ardec-primary-dark rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-2xl"></i>
                    </div>
                </div>
                <div class="ml-6 flex-1">
                    <h1 class="text-2xl font-bold text-gray-900">@Model.UserName</h1>
                    <p class="text-sm text-gray-600">@Model.Email</p>
                    <div class="mt-2 flex items-center space-x-4">
                        @if (Model.IsLockedOut)
                        {
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-lock mr-1"></i>
                                Bloccato
                            </span>
                        }
                        else
                        {
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                Attivo
                            </span>
                        }
                        
                        @if (Model.EmailConfirmed)
                        {
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-envelope-check mr-1"></i>
                                Email Confermata
                            </span>
                        }
                        else
                        {
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                <i class="fas fa-envelope mr-1"></i>
                                Email Non Confermata
                            </span>
                        }
                    </div>
                </div>
            </div>
        </div>
        
        <!-- User Information Grid -->
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Basic Info -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                        <i class="fas fa-info-circle mr-2 text-ardec-primary"></i>
                        Informazioni Base
                    </h3>
                    
                    <div class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">ID Utente</dt>
                            <dd class="text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded">@Model.Id</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Nome Utente</dt>
                            <dd class="text-sm text-gray-900">@Model.UserName</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Email</dt>
                            <dd class="text-sm text-gray-900">
                                <a href="mailto:@Model.Email" class="text-ardec-primary hover:text-ardec-primary-dark">
                                    @Model.Email
                                </a>
                            </dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Blocco Account</dt>
                            <dd class="text-sm">
                                @if (Model.LockoutEnabled)
                                {
                                    <span class="text-orange-600">
                                        <i class="fas fa-shield-alt mr-1"></i>
                                        Abilitato
                                    </span>
                                }
                                else
                                {
                                    <span class="text-gray-600">
                                        <i class="fas fa-shield-alt mr-1"></i>
                                        Disabilitato
                                    </span>
                                }
                            </dd>
                        </div>
                    </div>
                </div>

                <!-- Account Stats -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                        <i class="fas fa-chart-line mr-2 text-ardec-primary"></i>
                        Statistiche Account
                    </h3>
                    
                    <div class="space-y-3">
                        @if (Model.LastLoginDate.HasValue)
                        {
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Ultimo Accesso</dt>
                                <dd class="text-sm text-gray-900">
                                    <div>📅 @Model.LastLoginDate.Value.ToString("dd/MM/yyyy")</div>
                                    <div class="text-xs text-gray-500">🕐 @Model.LastLoginDate.Value.ToString("HH:mm:ss")</div>
                                </dd>
                            </div>
                        }
                        else
                        {
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Ultimo Accesso</dt>
                                <dd class="text-sm text-gray-500 italic">Mai effettuato</dd>
                            </div>
                        }
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Tentativi Falliti</dt>
                            <dd class="text-sm">
                                @if (Model.AccessFailedCount > 0)
                                {
                                    <span class="text-red-600 font-medium">⚠️ @Model.AccessFailedCount</span>
                                }
                                else
                                {
                                    <span class="text-green-600">✅ Nessuno</span>
                                }
                            </dd>
                        </div>
                        
                        @if (Model.LockoutEnd.HasValue)
                        {
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Bloccato Fino</dt>
                                <dd class="text-sm text-red-600">
                                    <div>📅 @Model.LockoutEnd.Value.ToString("dd/MM/yyyy")</div>
                                    <div class="text-xs">🕐 @Model.LockoutEnd.Value.ToString("HH:mm:ss")</div>
                                </dd>
                            </div>
                        }
                    </div>
                </div>

                <!-- Roles & Permissions -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                        <i class="fas fa-shield-alt mr-2 text-ardec-primary"></i>
                        Ruoli e Permessi
                    </h3>
                    
                    <div class="space-y-3">
                        @if (Model.CurrentRoles.Any())
                        {
                            <div>
                                <dt class="text-sm font-medium text-gray-500 mb-2">Ruoli Assegnati</dt>
                                <dd class="space-y-2">
                                    @foreach (var role in Model.CurrentRoles)
                                    {
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">@role</div>
                                                <div class="text-xs text-gray-500">@GetRoleDescription(role)</div>
                                            </div>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetRoleBadgeClasses(role)">
                                                @role
                                            </span>
                                        </div>
                                    }
                                </dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500 mb-2">Permessi Principali</dt>
                                <dd class="space-y-1">
                                    @{
                                        var permissions = GetUserPermissions(Model.CurrentRoles);
                                    }
                                    @foreach (var permission in permissions)
                                    {
                                        <div class="flex items-center text-xs text-gray-600">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            @permission
                                        </div>
                                    }
                                </dd>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-8">
                                <i class="fas fa-user-slash text-3xl text-gray-400 mb-2"></i>
                                <p class="text-sm text-gray-500">Nessun ruolo assegnato</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Card -->
    <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
        <div class="px-6 py-4 border-b border-neutral-200">
            <h3 class="text-lg font-medium text-gray-900">
                <i class="fas fa-bolt mr-2 text-ardec-primary"></i>
                Azioni Rapide
            </h3>
            <p class="mt-1 text-sm text-gray-600">Operazioni comuni per questo utente.</p>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <!-- Modifica Utente - Prima azione, più prominente -->
                <a href="@Url.Action("Edit", new { id = Model.Id })" class="flex flex-col items-center justify-center p-4 bg-ardec-primary text-white hover:bg-ardec-primary-dark rounded-lg transition-colors shadow-sm">
                    <i class="fas fa-edit text-2xl mb-2"></i>
                    <span class="text-sm font-medium">Modifica Utente</span>
                </a>
                
                <!-- Reset Password -->
                <button type="button" onclick="resetPassword('@Model.Id', '@Model.Email')" class="flex flex-col items-center justify-center p-4 border border-blue-300 text-blue-700 hover:bg-blue-50 rounded-lg transition-colors">
                    <i class="fas fa-key text-2xl mb-2"></i>
                    <span class="text-sm font-medium">Reset Password</span>
                </button>
                
                <!-- Blocca/Sblocca Utente -->
                <button type="button" onclick="toggleLockout('@Model.Id', '@Model.Email', @Model.IsLockedOut.ToString().ToLower())" class="flex flex-col items-center justify-center p-4 border @(Model.IsLockedOut ? "border-green-300 text-green-700 hover:bg-green-50" : "border-orange-300 text-orange-700 hover:bg-orange-50") rounded-lg transition-colors">
                    @if (Model.IsLockedOut)
                    {
                        <i class="fas fa-unlock text-2xl mb-2"></i>
                        <span class="text-sm font-medium">Sblocca Utente</span>
                    }
                    else
                    {
                        <i class="fas fa-lock text-2xl mb-2"></i>
                        <span class="text-sm font-medium">Blocca Utente</span>
                    }
                </button>
                
                <!-- Conferma Email (solo se necessario) -->
                @if (!Model.EmailConfirmed)
                {
                    <button type="button" onclick="confirmEmail('@Model.Id', '@Model.Email')" class="flex flex-col items-center justify-center p-4 border border-green-300 text-green-700 hover:bg-green-50 rounded-lg transition-colors">
                        <i class="fas fa-envelope-check text-2xl mb-2"></i>
                        <span class="text-sm font-medium">Conferma Email</span>
                    </button>
                }
                
                <!-- Visualizza Lista Utenti -->
                <a href="@Url.Action("Index")" class="flex flex-col items-center justify-center p-4 border border-gray-300 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <i class="fas fa-list text-2xl mb-2"></i>
                    <span class="text-sm font-medium">Lista Utenti</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Danger Zone (only if not admin) -->
    @if (Model.Email != "<EMAIL>")
    {
        <div class="bg-red-50 border border-red-200 rounded-lg">
            <div class="px-6 py-4 border-b border-red-200">
                <h3 class="text-lg font-medium text-red-800">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Zona Pericolosa
                </h3>
                <p class="mt-1 text-sm text-red-600">Azioni che non possono essere annullate.</p>
            </div>
            
            <div class="p-6">
                <button type="button" onclick="deleteUser('@Model.Id', '@Model.Email')" class="bg-red-600 hover:bg-red-700 text-white focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center">
                    <i class="fas fa-trash mr-2"></i>
                    Elimina Utente Permanentemente
                </button>
                <p class="mt-2 text-xs text-red-600">⚠️ Questa azione eliminerà definitivamente l'utente e non può essere annullata.</p>
            </div>
        </div>
    }

</div>

@await Html.PartialAsync("_ToastContainer")

@functions {
    string GetRoleDescription(string role) => role switch
    {
        "Admin" => "Amministratore del sistema - Accesso completo a tutte le funzionalità",
        "PowerUser" => "Utente con privilegi avanzati - Può modificare dati ed esportare",
        "Editor" => "Editor - Può modificare parti e tavole assegnate",
        "Viewer" => "Visualizzatore - Accesso in sola lettura",
        _ => "Ruolo personalizzato"
    };
    
    string GetRoleBadgeClasses(string role) => role switch
    {
        "Admin" => "bg-red-100 text-red-800",
        "PowerUser" => "bg-orange-100 text-orange-800", 
        "Editor" => "bg-blue-100 text-blue-800",
        "Viewer" => "bg-gray-100 text-gray-800",
        _ => "bg-purple-100 text-purple-800"
    };

    List<string> GetUserPermissions(List<string> roles)
    {
        var permissions = new List<string>();
        
        foreach (var role in roles)
        {
            switch (role)
            {
                case "Admin":
                    permissions.AddRange(new[] {
                        "Gestione completa del sistema",
                        "Gestione utenti e ruoli", 
                        "Eliminazione dati",
                        "Modifica cataloghi",
                        "Esportazione dati avanzata"
                    });
                    break;
                case "PowerUser":
                    permissions.AddRange(new[] {
                        "Modifica dati avanzata",
                        "Esportazione dati",
                        "Modifica cataloghi",
                        "Cambio stato tavole"
                    });
                    break;
                case "Editor":
                    permissions.AddRange(new[] {
                        "Modifica parti assegnate",
                        "Modifica tavole assegnate", 
                        "Creazione nuovi dati",
                        "Cambio stato tavole"
                    });
                    break;
                case "Viewer":
                    permissions.Add("Visualizzazione dati in sola lettura");
                    break;
            }
        }
        
        return permissions.Distinct().ToList();
    }
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize Flowbite components
            setTimeout(() => {
                if (window.Flowbite && typeof window.Flowbite.init === 'function') {
                    window.Flowbite.init();
                    console.log('✅ Flowbite initialized successfully');
                }
            }, 100);

            console.log('✅ User Details page initialized successfully!');
        });

        // Quick action functions (same as Edit view)
        async function toggleLockout(userId, userEmail, isCurrentlyLocked) {
            const action = isCurrentlyLocked ? 'sbloccare' : 'bloccare';
            
            if (!confirm(`Sei sicuro di voler ${action} l'utente ${userEmail}?`)) {
                return;
            }

            try {
                const response = await fetch(`/UserManagement/api/users/${userId}/toggle-lockout`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showFlowbiteToast('Successo', result.message, 'success');
                    location.reload();
                } else {
                    showFlowbiteToast('Errore', result.message, 'error');
                }
            } catch (error) {
                console.error('Error toggling lockout:', error);
                showFlowbiteToast('Errore', 'Errore di connessione', 'error');
            }
        }

        async function deleteUser(userId, userEmail) {
            if (!confirm(`Sei sicuro di voler eliminare l'utente ${userEmail}?\\n\\nQuesta azione non può essere annullata!`)) {
                return;
            }

            try {
                const response = await fetch(`/UserManagement/api/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showFlowbiteToast('Successo', result.message, 'success');
                    setTimeout(() => {
                        window.location.href = '@Url.Action("Index")';
                    }, 2000);
                } else {
                    showFlowbiteToast('Errore', result.message, 'error');
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                showFlowbiteToast('Errore', 'Errore di connessione', 'error');
            }
        }

        function resetPassword(userId, userEmail) {
            showFlowbiteToast('Info', 'Utilizza il pulsante Modifica per accedere al reset password', 'info');
        }

        async function confirmEmail(userId, userEmail) {
            if (!confirm(`Confermare l'email per l'utente ${userEmail}?`)) {
                return;
            }

            try {
                const response = await fetch(`/UserManagement/api/users/${userId}/confirm-email`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showFlowbiteToast('Successo', result.message, 'success');
                    location.reload();
                } else {
                    showFlowbiteToast('Errore', result.message, 'error');
                }
            } catch (error) {
                console.error('Error confirming email:', error);
                showFlowbiteToast('Errore', 'Errore di connessione', 'error');
            }
        }

        // Make functions globally available
        window.toggleLockout = toggleLockout;
        window.deleteUser = deleteUser;
        window.resetPassword = resetPassword;
        window.confirmEmail = confirmEmail;
    </script>
}
