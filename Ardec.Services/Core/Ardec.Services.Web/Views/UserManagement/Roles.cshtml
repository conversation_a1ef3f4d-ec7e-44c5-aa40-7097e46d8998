@model Ardec.Services.Web.Features.UserManagement.RoleManagementViewModel
@{
    ViewData["Title"] = "Gestione Ruoli e Permessi - MIL-STD-1388 Services";
    ViewBag.ToolbarTitle = "Gestione Ruoli e Permessi";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
}

@{
    ViewBag.ExtraButtons = $"<a href=\"{Url.Action("Index")}\" class=\"bg-white border border-neutral-300 text-neutral-600 hover:bg-neutral-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium\">" +
        "<i class=\"fas fa-arrow-left mr-2\"></i>Torna alla Lista" +
    "</a>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Roles and Permissions Management -->
<div class="space-y-6">
    
    <!-- Overview -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400 text-xl"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Sistema di Autorizzazione</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>Il sistema utilizza ruoli predefiniti con permessi specifici. Ogni utente può avere uno o più ruoli assegnati.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Roles Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        @foreach (var role in Model.Roles)
        {
            <div class="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 @GetRoleIconBg(role.Name) rounded-lg flex items-center justify-center">
                        <i class="@GetRoleIcon(role.Name) text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-6 flex-1">
                    <h3 class="text-lg font-medium text-gray-900">@role.Name</h3>
                    <p class="text-sm text-gray-600">@role.UserCount utenti</p>
                </div>
            </div>
            <div class="mt-4">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetRoleBadgeClasses(role.Name)">
                    @role.Name
                </span>
            </div>
            </div>
        }
    </div>

    <!-- Detailed Roles Table -->
    <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
        <div class="px-6 py-4 border-b border-neutral-200">
            <h3 class="text-lg font-medium text-gray-900">
                <i class="fas fa-users-cog mr-2 text-ardec-primary"></i>
                Ruoli del Sistema
            </h3>
            <p class="mt-1 text-sm text-gray-600">Dettaglio completo di tutti i ruoli e le loro caratteristiche.</p>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Ruolo
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Descrizione
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Utenti
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Permessi Principali
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach (var role in Model.Roles)
                    {
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 w-8 h-8">
                                    <div class="w-8 h-8 @GetRoleIconBg(role.Name) rounded-full flex items-center justify-center">
                                        <i class="@GetRoleIcon(role.Name) text-white text-sm"></i>
                                    </div>
                                </div>
                                <div class="ml-6">
                                    <div class="text-sm font-medium text-gray-900">@role.Name</div>
                                    <div class="mt-1">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium @GetRoleBadgeClasses(role.Name)">
                                            @role.Name
                                        </span>
                                    </div>
                                </div>
                            </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">@role.GetDescription()</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <div class="text-sm text-gray-900 font-medium">@role.UserCount</div>
                                @if (role.UserCount > 0)
                                {
                                    <div class="text-xs text-gray-500">
                                        @(Model.Roles.Sum(r => r.UserCount) > 0 ? Math.Round((double)role.UserCount / Model.Roles.Sum(r => r.UserCount) * 100, 1) : 0)% del totale
                                    </div>
                                }
                            </td>
                            <td class="px-6 py-4">
                                <div class="space-y-1">
                                    @{
                                        var permissions = GetRolePermissions(role.Name);
                                    }
                                    @foreach (var permission in permissions.Take(3))
                                    {
                                        <div class="flex items-center text-xs text-gray-600">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            @permission
                                        </div>
                                    }
                                    @if (permissions.Count > 3)
                                    {
                                        <div class="text-xs text-gray-500 italic">
                                            e altri @(permissions.Count - 3) permessi...
                                        </div>
                                    }
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>

    <!-- Policies and Permissions -->
    <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
        <div class="px-6 py-4 border-b border-neutral-200">
            <h3 class="text-lg font-medium text-gray-900">
                <i class="fas fa-shield-alt mr-2 text-ardec-primary"></i>
                Policy e Permessi
            </h3>
            <p class="mt-1 text-sm text-gray-600">Politiche di autorizzazione definite nel sistema.</p>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                @foreach (var policy in Model.Policies)
                {
                    <div class="border border-gray-200 rounded-lg p-4 hover:border-ardec-primary transition-colors">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="text-sm font-medium text-gray-900">@policy.Name</h4>
                                <p class="text-xs text-gray-600 mt-1">@policy.GetDescription()</p>
                            </div>
                            <div class="ml-4">
                                <i class="fas fa-key text-ardec-primary"></i>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="text-xs font-medium text-gray-500 mb-2">Ruoli Autorizzati:</div>
                            <div class="flex flex-wrap gap-1">
                                @foreach (var role in policy.RequiredRoles)
                                {
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium @GetRoleBadgeClasses(role)">
                                        @role
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Role Hierarchy Visualization -->
    <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
        <div class="px-6 py-4 border-b border-neutral-200">
            <h3 class="text-lg font-medium text-gray-900">
                <i class="fas fa-sitemap mr-2 text-ardec-primary"></i>
                Gerarchia dei Ruoli
            </h3>
            <p class="mt-1 text-sm text-gray-600">Visualizzazione della gerarchia di autorizzazioni dal più alto al più basso privilegio.</p>
        </div>
        
        <div class="p-6">
            <div class="space-y-4">
                <!-- Admin Level -->
                    <div class="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex-shrink-0 w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-crown text-white"></i>
                        </div>
                        <div class="ml-6 flex-1">
                            <div class="text-sm font-medium text-red-800">Admin - Livello Massimo</div>
                            <div class="text-xs text-red-600">Accesso completo a tutte le funzionalità del sistema</div>
                        </div>
                        <div class="text-xs text-red-600">
                            @(Model.Roles.FirstOrDefault(r => r.Name == "Admin")?.UserCount ?? 0) utenti
                        </div>
                    </div>

                <div class="ml-4 border-l-2 border-gray-300 pl-4 space-y-3">
                    <!-- PowerUser Level -->
                    <div class="flex items-center p-3 bg-orange-50 border border-orange-200 rounded-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-user-cog text-white text-sm"></i>
                        </div>
                        <div class="ml-5 flex-1">
                            <div class="text-sm font-medium text-orange-800">PowerUser - Livello Avanzato</div>
                            <div class="text-xs text-orange-600">Modifica dati ed export avanzati</div>
                        </div>
                        <div class="text-xs text-orange-600">
                            @(Model.Roles.FirstOrDefault(r => r.Name == "PowerUser")?.UserCount ?? 0) utenti
                        </div>
                    </div>

                    <div class="ml-4 border-l-2 border-gray-300 pl-4 space-y-3">
                        <!-- Editor Level -->
                        <div class="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-edit text-white text-sm"></i>
                            </div>
                            <div class="ml-5 flex-1">
                                <div class="text-sm font-medium text-blue-800">Editor - Livello Intermedio</div>
                                <div class="text-xs text-blue-600">Modifica parti e tavole assegnate</div>
                            </div>
                            <div class="text-xs text-blue-600">
                                @(Model.Roles.FirstOrDefault(r => r.Name == "Editor")?.UserCount ?? 0) utenti
                            </div>
                        </div>

                        <div class="ml-4 border-l-2 border-gray-300 pl-4">
                            <!-- Viewer Level -->
                            <div class="flex items-center p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                <div class="flex-shrink-0 w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-eye text-white text-sm"></i>
                                </div>
                                <div class="ml-5 flex-1">
                                    <div class="text-sm font-medium text-gray-800">Viewer - Livello Base</div>
                                    <div class="text-xs text-gray-600">Solo visualizzazione dati</div>
                                </div>
                                <div class="text-xs text-gray-600">
                                    @(Model.Roles.FirstOrDefault(r => r.Name == "Viewer")?.UserCount ?? 0) utenti
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_ToastContainer")

@functions {
    string GetRoleBadgeClasses(string role) => role switch
    {
        "Admin" => "bg-red-100 text-red-800",
        "PowerUser" => "bg-orange-100 text-orange-800", 
        "Editor" => "bg-blue-100 text-blue-800",
        "Viewer" => "bg-gray-100 text-gray-800",
        _ => "bg-purple-100 text-purple-800"
    };

    string GetRoleIconBg(string role) => role switch
    {
        "Admin" => "bg-red-500",
        "PowerUser" => "bg-orange-500", 
        "Editor" => "bg-blue-500",
        "Viewer" => "bg-gray-500",
        _ => "bg-purple-500"
    };

    string GetRoleIcon(string role) => role switch
    {
        "Admin" => "fas fa-crown",
        "PowerUser" => "fas fa-user-cog", 
        "Editor" => "fas fa-edit",
        "Viewer" => "fas fa-eye",
        _ => "fas fa-user"
    };

    List<string> GetRolePermissions(string role)
    {
        return role switch
        {
            "Admin" => new List<string>
            {
                "Gestione completa del sistema",
                "Gestione utenti e ruoli",
                "Eliminazione dati",
                "Modifica cataloghi",
                "Esportazione dati avanzata",
                "Accesso alle statistiche",
                "Configurazione sistema"
            },
            "PowerUser" => new List<string>
            {
                "Modifica dati avanzata",
                "Esportazione dati",
                "Modifica cataloghi",
                "Cambio stato tavole",
                "Accesso report avanzati"
            },
            "Editor" => new List<string>
            {
                "Modifica parti assegnate",
                "Modifica tavole assegnate",
                "Creazione nuovi dati",
                "Cambio stato tavole"
            },
            "Viewer" => new List<string>
            {
                "Visualizzazione dati in sola lettura",
                "Accesso ai cataloghi",
                "Consultazione tavole"
            },
            _ => new List<string> { "Permessi personalizzati" }
        };
    }
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize Flowbite components
            setTimeout(() => {
                if (window.Flowbite && typeof window.Flowbite.init === 'function') {
                    window.Flowbite.init();
                    console.log('✅ Flowbite initialized successfully');
                }
            }, 100);

            console.log('✅ Roles management page initialized successfully!');
        });
    </script>
}
