@model Ardec.Services.Web.Features.UserManagement.CreateUserViewModel
@{
    ViewData["Title"] = "Nuovo Utente - MIL-STD-1388 Services";
    ViewBag.ToolbarTitle = "Nuovo Utente";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
}

@{
    ViewBag.ExtraButtons = $"<a href=\"{Url.Action("Index")}\" class=\"bg-white border border-neutral-300 text-neutral-600 hover:bg-neutral-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium\">" +
        "<i class=\"fas fa-arrow-left mr-2\"></i>Torna alla Lista" +
    "</a>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Create User Form -->
<div class="max-w-4xl mx-auto">
    <form asp-action="Create" method="post" id="createUserForm" class="space-y-6">
        @Html.AntiForgeryToken()
        
        <!-- Main Card -->
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-user-plus mr-2 text-ardec-primary"></i>
                    Informazioni Utente
                </h3>
                <p class="mt-1 text-sm text-gray-600">Inserisci i dati del nuovo utente del sistema.</p>
            </div>
            
            <div class="p-6 space-y-6">
                <!-- Email and Username Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label asp-for="Email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email *
                        </label>
                        <input asp-for="Email" type="email" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" placeholder="<EMAIL>" required>
                        <span asp-validation-for="Email" class="text-red-600 text-sm"></span>
                    </div>
                    
                    <div>
                        <label asp-for="UserName" class="block text-sm font-medium text-gray-700 mb-2">
                            Nome Utente *
                        </label>
                        <input asp-for="UserName" type="text" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" placeholder="username" required>
                        <span asp-validation-for="UserName" class="text-red-600 text-sm"></span>
                        <p class="mt-1 text-xs text-gray-500">Nome univoco per l'identificazione dell'utente</p>
                    </div>
                </div>

                <!-- Password Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label asp-for="Password" class="block text-sm font-medium text-gray-700 mb-2">
                            Password *
                        </label>
                        <div class="relative">
                            <input asp-for="Password" type="password" id="password" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" required minlength="6">
                            <button type="button" onclick="togglePasswordVisibility('password')" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye" id="password-eye"></i>
                            </button>
                        </div>
                        <span asp-validation-for="Password" class="text-red-600 text-sm"></span>
                        <p class="mt-1 text-xs text-gray-500">Minimo 6 caratteri</p>
                    </div>
                    
                    <div>
                        <label asp-for="ConfirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                            Conferma Password *
                        </label>
                        <div class="relative">
                            <input asp-for="ConfirmPassword" type="password" id="confirmPassword" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-ardec-primary focus:border-ardec-primary sm:text-sm" required>
                            <button type="button" onclick="togglePasswordVisibility('confirmPassword')" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye" id="confirmPassword-eye"></i>
                            </button>
                        </div>
                        <span asp-validation-for="ConfirmPassword" class="text-red-600 text-sm"></span>
                    </div>
                </div>
                
                <!-- Account Settings -->
                <div class="space-y-4">
                    <h4 class="text-sm font-medium text-gray-700">Impostazioni Account</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center">
                            <input asp-for="EmailConfirmed" type="checkbox" class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-gray-300 rounded">
                            <label asp-for="EmailConfirmed" class="ml-2 block text-sm text-gray-700">
                                Email confermata
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input asp-for="LockoutEnabled" type="checkbox" class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-gray-300 rounded">
                            <label asp-for="LockoutEnabled" class="ml-2 block text-sm text-gray-700">
                                Abilita blocco account
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Roles Assignment Card -->
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-shield-alt mr-2 text-ardec-primary"></i>
                    Assegnazione Ruoli
                </h3>
                <p class="mt-1 text-sm text-gray-600">Seleziona i ruoli da assegnare all'utente.</p>
            </div>
            
            <div class="p-6">
                <span asp-validation-for="SelectedRoles" class="text-red-600 text-sm block mb-4"></span>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @for (int i = 0; i < Model.AvailableRoles.Count; i++)
                    {
                        var role = Model.AvailableRoles[i];
                        <div class="relative">
                            <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ardec-primary transition-colors">
                                <input type="checkbox" name="SelectedRoles" value="@role.Value" 
                                       id="<EMAIL>"
                                       class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-gray-300 rounded"
                                       @(Model.SelectedRoles.Contains(role.Value) ? "checked" : "")>
                                <div class="ml-3 flex-1">
                                    <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 cursor-pointer">
                                        @role.Value
                                    </label>
                                    <p class="text-xs text-gray-500">@GetRoleDescription(role.Value)</p>
                                </div>
                                <div class="ml-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetRoleBadgeClasses(role.Value)">
                                        @role.Value
                                    </span>
                                </div>
                            </div>
                        </div>
                    }
                </div>
                
                @if (!Model.AvailableRoles.Any())
                {
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-circle text-3xl text-gray-400 mb-2"></i>
                        <p class="text-gray-500">Nessun ruolo disponibile nel sistema.</p>
                    </div>
                }
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-4 pt-6">
            <a href="@Url.Action("Index")" class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center">
                <i class="fas fa-times mr-2"></i>
                Annulla
            </a>
            <button type="submit" class="bg-ardec-primary hover:bg-ardec-primary-dark text-white focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center">
                <i class="fas fa-user-plus mr-2"></i>
                Crea Utente
            </button>
        </div>
    </form>
</div>

@await Html.PartialAsync("_ToastContainer")

@functions {
    string GetRoleDescription(string role) => role switch
    {
        "Admin" => "Amministratore del sistema - Accesso completo a tutte le funzionalità",
        "PowerUser" => "Utente con privilegi avanzati - Può modificare dati ed esportare",
        "Editor" => "Editor - Può modificare parti e tavole assegnate",
        "Viewer" => "Visualizzatore - Accesso in sola lettura",
        _ => "Ruolo personalizzato"
    };
    
    string GetRoleBadgeClasses(string role) => role switch
    {
        "Admin" => "bg-red-100 text-red-800",
        "PowerUser" => "bg-orange-100 text-orange-800", 
        "Editor" => "bg-blue-100 text-blue-800",
        "Viewer" => "bg-gray-100 text-gray-800",
        _ => "bg-purple-100 text-purple-800"
    };
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize Flowbite components
            setTimeout(() => {
                if (window.Flowbite && typeof window.Flowbite.init === 'function') {
                    window.Flowbite.init();
                    console.log('✅ Flowbite initialized successfully');
                }
            }, 100);

            // Real-time password confirmation validation
            $('#confirmPassword').on('input', function() {
                const password = $('#password').val();
                const confirmPassword = $(this).val();
                
                if (confirmPassword && password !== confirmPassword) {
                    $(this).removeClass('border-gray-300').addClass('border-red-300');
                    $(this).next().text('Le password non coincidono');
                } else {
                    $(this).removeClass('border-red-300').addClass('border-gray-300');
                    $(this).next().text('');
                }
            });

            // Form validation before submit
            $('#createUserForm').on('submit', function(e) {
                const selectedRoles = $('input[name="SelectedRoles"]:checked').length;
                
                if (selectedRoles === 0) {
                    e.preventDefault();
                    showFlowbiteToast('Errore', 'Seleziona almeno un ruolo per l\'utente', 'error');
                    $('html, body').animate({
                        scrollTop: $('#createUserForm').find('input[name="SelectedRoles"]').first().offset().top - 100
                    }, 500);
                    return false;
                }

                // Show loading state
                const submitBtn = $(this).find('button[type="submit"]');
                submitBtn.prop('disabled', true);
                submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Creazione in corso...');
            });

            // Auto-generate username from email
            $('#Email').on('input', function() {
                const email = $(this).val();
                const username = $('#UserName').val();
                
                // Only auto-fill if username is empty
                if (!username && email.includes('@@')) {
                    const emailName = email.split('@@')[0];
                    $('#UserName').val(emailName);
                }
            });

            console.log('✅ Create User form initialized successfully!');
        });

        // Toggle password visibility
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.getElementById(inputId + '-eye');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Role selection helpers
        function selectAllRoles() {
            $('input[name="SelectedRoles"]').prop('checked', true);
        }

        function clearAllRoles() {
            $('input[name="SelectedRoles"]').prop('checked', false);
        }

        // Make functions globally available
        window.togglePasswordVisibility = togglePasswordVisibility;
        window.selectAllRoles = selectAllRoles;
        window.clearAllRoles = clearAllRoles;
    </script>
}
