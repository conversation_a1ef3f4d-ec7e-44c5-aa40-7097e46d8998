@model Ardec.Services.Web.Features.UserManagement.UserStatisticsViewModel
@{
    ViewData["Title"] = "Statistiche Utenti - MIL-STD-1388 Services";
    ViewBag.ToolbarTitle = "Statistiche Utenti";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
}

@{
    ViewBag.ExtraButtons = $"<a href=\"{Url.Action("Index")}\" class=\"bg-white border border-neutral-300 text-neutral-600 hover:bg-neutral-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium\">" +
        "<i class=\"fas fa-arrow-left mr-2\"></i>Torna alla Lista" +
    "</a>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Statistics Dashboard -->
<div class="space-y-6">
    
    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gradient-to-r from-ardec-primary to-ardec-primary-dark p-6 rounded-lg text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm opacity-90">Totale Utenti</p>
                    <p class="text-3xl font-bold">@Model.TotalUsers</p>
                    <p class="text-xs opacity-75 mt-1">Registrati nel sistema</p>
                </div>
                <i class="fas fa-users text-4xl opacity-75"></i>
            </div>
        </div>
        
        <div class="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm opacity-90">Utenti Attivi</p>
                    <p class="text-3xl font-bold">@Model.ActiveUsers</p>
                    <p class="text-xs opacity-75 mt-1">@(Model.TotalUsers > 0 ? Math.Round((double)Model.ActiveUsers / Model.TotalUsers * 100, 1) : 0)% del totale</p>
                </div>
                <i class="fas fa-check-circle text-4xl opacity-75"></i>
            </div>
        </div>
        
        <div class="bg-gradient-to-r from-orange-500 to-orange-600 p-6 rounded-lg text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm opacity-90">Utenti Bloccati</p>
                    <p class="text-3xl font-bold">@Model.LockedUsers</p>
                    <p class="text-xs opacity-75 mt-1">@(Model.TotalUsers > 0 ? Math.Round((double)Model.LockedUsers / Model.TotalUsers * 100, 1) : 0)% del totale</p>
                </div>
                <i class="fas fa-lock text-4xl opacity-75"></i>
            </div>
        </div>
        
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm opacity-90">Email Non Confermate</p>
                    <p class="text-3xl font-bold">@Model.UnconfirmedEmails</p>
                    <p class="text-xs opacity-75 mt-1">@(Model.TotalUsers > 0 ? Math.Round((double)Model.UnconfirmedEmails / Model.TotalUsers * 100, 1) : 0)% del totale</p>
                </div>
                <i class="fas fa-envelope text-4xl opacity-75"></i>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        <!-- Users by Role Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-chart-pie mr-2 text-ardec-primary"></i>
                    Distribuzione per Ruoli
                </h3>
                <span class="text-sm text-gray-500">@Model.TotalUsers utenti totali</span>
            </div>
            
            @if (Model.UsersByRole.Any())
            {
                <div class="space-y-4">
                    @foreach (var roleStats in Model.UsersByRole.OrderByDescending(x => x.Value))
                    {
                        var percentage = Model.TotalUsers > 0 ? Math.Round((double)roleStats.Value / Model.TotalUsers * 100, 1) : 0;
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3 flex-1">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetRoleBadgeClasses(roleStats.Key)">
                                        @roleStats.Key
                                    </span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-sm font-medium text-gray-900">@roleStats.Value utenti</span>
                                        <span class="text-sm text-gray-500">@percentage%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="@GetRoleProgressClass(roleStats.Key) h-2 rounded-full transition-all duration-300" style="width: @percentage%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-8">
                    <i class="fas fa-chart-pie text-3xl text-gray-400 mb-2"></i>
                    <p class="text-gray-500">Nessun dato disponibile</p>
                </div>
            }
        </div>

        <!-- Account Status Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-chart-bar mr-2 text-ardec-primary"></i>
                    Stato degli Account
                </h3>
            </div>
            
            <div class="space-y-4">
                <!-- Active Users -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span class="text-sm font-medium text-gray-900">Attivi</span>
                    </div>
                    <div class="text-right">
                        <span class="text-sm font-medium text-gray-900">@Model.ActiveUsers</span>
                        <div class="w-32 bg-gray-200 rounded-full h-2 mt-1">
                            <div class="bg-green-500 h-2 rounded-full" style="width: @(Model.TotalUsers > 0 ? (double)Model.ActiveUsers / Model.TotalUsers * 100 : 0)%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Locked Users -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <span class="text-sm font-medium text-gray-900">Bloccati</span>
                    </div>
                    <div class="text-right">
                        <span class="text-sm font-medium text-gray-900">@Model.LockedUsers</span>
                        <div class="w-32 bg-gray-200 rounded-full h-2 mt-1">
                            <div class="bg-orange-500 h-2 rounded-full" style="width: @(Model.TotalUsers > 0 ? (double)Model.LockedUsers / Model.TotalUsers * 100 : 0)%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Unconfirmed Emails -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span class="text-sm font-medium text-gray-900">Email Non Confermate</span>
                    </div>
                    <div class="text-right">
                        <span class="text-sm font-medium text-gray-900">@Model.UnconfirmedEmails</span>
                        <div class="w-32 bg-gray-200 rounded-full h-2 mt-1">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: @(Model.TotalUsers > 0 ? (double)Model.UnconfirmedEmails / Model.TotalUsers * 100 : 0)%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    @if (Model.RecentActivity.Any())
    {
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-history mr-2 text-ardec-primary"></i>
                    Attività Recenti
                </h3>
                <p class="mt-1 text-sm text-gray-600">Ultime attività degli utenti nel sistema.</p>
            </div>
            
            <div class="p-6">
                <div class="flow-root">
                    <ul role="list" class="-mb-8">
                        @for (int i = 0; i < Model.RecentActivity.Count; i++)
                        {
                            var activity = Model.RecentActivity[i];
                            <li>
                                <div class="relative pb-8">
                                    @if (i < Model.RecentActivity.Count - 1)
                                    {
                                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                    }
                                    <div class="relative flex space-x-3">
                                        <div>
                                            <span class="@GetActivityIconClass(activity.Action) h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white">
                                                <i class="@GetActivityIcon(activity.Action) text-white text-sm"></i>
                                            </span>
                                        </div>
                                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                            <div>
                                                <p class="text-sm text-gray-900">
                                                    <span class="font-medium">@activity.UserEmail</span> @GetActivityDescription(activity.Action)
                                                </p>
                                            </div>
                                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                <time datetime="@activity.Timestamp.ToString("yyyy-MM-ddTHH:mm:ss")">
                                                    @activity.Timestamp.ToString("dd/MM HH:mm")
                                                </time>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </div>
    }

    <!-- System Health -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">
                    @(Model.TotalUsers > 0 ? Math.Round((double)Model.ActiveUsers / Model.TotalUsers * 100, 1) : 0)%
                </div>
                <div class="text-sm text-gray-600 mt-1">Tasso di Attivazione</div>
                <div class="mt-4 @(Model.ActiveUsers >= Model.TotalUsers * 0.8 ? "text-green-600" : Model.ActiveUsers >= Model.TotalUsers * 0.6 ? "text-yellow-600" : "text-red-600")">
                    <i class="fas fa-@(Model.ActiveUsers >= Model.TotalUsers * 0.8 ? "check-circle" : Model.ActiveUsers >= Model.TotalUsers * 0.6 ? "exclamation-triangle" : "times-circle") text-2xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">
                    @(Model.TotalUsers > 0 ? Math.Round((double)Model.LockedUsers / Model.TotalUsers * 100, 1) : 0)%
                </div>
                <div class="text-sm text-gray-600 mt-1">Tasso di Blocco</div>
                <div class="mt-4 @(Model.LockedUsers <= Model.TotalUsers * 0.1 ? "text-green-600" : Model.LockedUsers <= Model.TotalUsers * 0.2 ? "text-yellow-600" : "text-red-600")">
                    <i class="fas fa-@(Model.LockedUsers <= Model.TotalUsers * 0.1 ? "check-circle" : Model.LockedUsers <= Model.TotalUsers * 0.2 ? "exclamation-triangle" : "times-circle") text-2xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">
                    @(Model.TotalUsers > 0 ? Math.Round((double)(Model.TotalUsers - Model.UnconfirmedEmails) / Model.TotalUsers * 100, 1) : 0)%
                </div>
                <div class="text-sm text-gray-600 mt-1">Email Confermate</div>
                <div class="mt-4 @((Model.TotalUsers - Model.UnconfirmedEmails) >= Model.TotalUsers * 0.9 ? "text-green-600" : (Model.TotalUsers - Model.UnconfirmedEmails) >= Model.TotalUsers * 0.7 ? "text-yellow-600" : "text-red-600")">
                    <i class="fas fa-@((Model.TotalUsers - Model.UnconfirmedEmails) >= Model.TotalUsers * 0.9 ? "check-circle" : (Model.TotalUsers - Model.UnconfirmedEmails) >= Model.TotalUsers * 0.7 ? "exclamation-triangle" : "times-circle") text-2xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    string GetRoleBadgeClasses(string role) => role switch
    {
        "Admin" => "bg-red-100 text-red-800",
        "PowerUser" => "bg-orange-100 text-orange-800", 
        "Editor" => "bg-blue-100 text-blue-800",
        "Viewer" => "bg-gray-100 text-gray-800",
        _ => "bg-purple-100 text-purple-800"
    };

    string GetRoleProgressClass(string role) => role switch
    {
        "Admin" => "bg-red-500",
        "PowerUser" => "bg-orange-500", 
        "Editor" => "bg-blue-500",
        "Viewer" => "bg-gray-500",
        _ => "bg-purple-500"
    };

    string GetActivityIconClass(string action) => action switch
    {
        "Login" => "bg-green-500",
        "Logout" => "bg-gray-500",
        "Failed Login" => "bg-red-500",
        "Password Reset" => "bg-blue-500",
        "Account Locked" => "bg-orange-500",
        _ => "bg-gray-500"
    };

    string GetActivityIcon(string action) => action switch
    {
        "Login" => "fas fa-sign-in-alt",
        "Logout" => "fas fa-sign-out-alt",
        "Failed Login" => "fas fa-exclamation-triangle",
        "Password Reset" => "fas fa-key",
        "Account Locked" => "fas fa-lock",
        _ => "fas fa-circle"
    };

    string GetActivityDescription(string action) => action switch
    {
        "Login" => "ha effettuato l'accesso",
        "Logout" => "ha effettuato il logout", 
        "Failed Login" => "ha tentato l'accesso (fallito)",
        "Password Reset" => "ha reimpostato la password",
        "Account Locked" => "è stato bloccato",
        _ => action
    };
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize Flowbite components
            setTimeout(() => {
                if (window.Flowbite && typeof window.Flowbite.init === 'function') {
                    window.Flowbite.init();
                    console.log('✅ Flowbite initialized successfully');
                }
            }, 100);

            // Auto-refresh statistics every 5 minutes
            setInterval(function() {
                location.reload();
            }, 5 * 60 * 1000);

            console.log('✅ Statistics page initialized successfully!');
        });
    </script>
}
