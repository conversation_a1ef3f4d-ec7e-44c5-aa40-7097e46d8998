@{
    ViewData["Title"] = "Dashboard - MIL-STD-1388 Services";
    ViewBag.ToolbarTitle = "Dashboard";
    ViewBag.ShowSave = false;
    ViewBag.ShowUndoRedo = false;
}

@await Html.PartialAsync("_Toolbar")

<!-- Statistics Cards Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <!-- Cataloghi Card -->
    <div class="bg-white rounded-lg border border-neutral-200 border-l-4 border-l-ardec-primary shadow-ardec hover:shadow-ardec-lg transition-shadow">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="text-xs font-semibold text-ardec-primary uppercase tracking-wider mb-2">
                        Cataloghi
                    </div>
                    <div class="text-2xl font-bold text-neutral-800">@Model.Statistics.CatalogsCount</div>
                </div>
                <div class="p-3 bg-ardec-primary/10 rounded-full">
                    <i class="fas fa-book text-2xl text-ardec-primary"></i>
                </div>
            </div>
            <div class="mt-4">
                <a href="@Url.Action("Index", "Catalogs")" class="inline-flex items-center text-sm font-medium text-ardec-primary hover:text-ardec-primary-dark transition-colors">
                    <i class="fas fa-arrow-right mr-2"></i>Gestisci
                </a>
            </div>
        </div>
    </div>

    <!-- Tavole Card -->
    <div class="bg-white rounded-lg border border-neutral-200 border-l-4 border-l-info shadow-ardec hover:shadow-ardec-lg transition-shadow">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="text-xs font-semibold text-info uppercase tracking-wider mb-2">
                        Tavole
                    </div>
                    <div class="text-2xl font-bold text-neutral-800">@Model.Statistics.TablesCount</div>
                </div>
                <div class="p-3 bg-ardec-100 rounded-full">
                    <i class="fas fa-table text-2xl text-info"></i>
                </div>
            </div>
            <div class="mt-4">
                <a href="@Url.Action("Index", "Tables")" class="inline-flex items-center text-sm font-medium text-info hover:text-ardec-primary transition-colors">
                    <i class="fas fa-arrow-right mr-2"></i>Gestisci
                </a>
            </div>
        </div>
    </div>

    <!-- Parti Card -->
    <div class="bg-white rounded-lg border border-neutral-200 border-l-4 border-l-warning shadow-ardec hover:shadow-ardec-lg transition-shadow">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="text-xs font-semibold text-warning uppercase tracking-wider mb-2">
                        Parti
                    </div>
                    <div class="text-2xl font-bold text-neutral-800">@Model.Statistics.PartsCount</div>
                </div>
                <div class="p-3 bg-ardec-100 rounded-full">
                    <i class="fas fa-puzzle-piece text-2xl text-warning"></i>
                </div>
            </div>
            <div class="mt-4">
                <a href="@Url.Action("Index", "Parts")" class="inline-flex items-center text-sm font-medium text-warning hover:text-ardec-primary transition-colors">
                    <i class="fas fa-arrow-right mr-2"></i>Gestisci
                </a>
            </div>
        </div>
    </div>

    <!-- Sistema Status Card -->
    <div class="bg-white rounded-lg border border-neutral-200 border-l-4 border-l-success shadow-ardec hover:shadow-ardec-lg transition-shadow">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="text-xs font-semibold text-success uppercase tracking-wider mb-2">
                        Sistema
                    </div>
                    <div class="text-xl font-bold text-success">Attivo</div>
                </div>
                <div class="p-3 bg-ardec-100 rounded-full">
                    <i class="fas fa-check-circle text-2xl text-success"></i>
                </div>
            </div>
            <div class="mt-4">
                <div class="text-xs text-neutral-500">
                    <i class="fas fa-clock mr-1"></i>
                    Aggiornato: @Model.Statistics.LastUpdate.ToString("HH:mm")
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Items Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Recent Catalogs -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-lg border border-neutral-200 shadow-ardec">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h3 class="text-lg font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-book mr-3 text-ardec-primary"></i>
                    Cataloghi Recenti
                </h3>
            </div>
            <div class="p-6">
                @if (Model.RecentItems.Catalogs?.Count > 0)
                {
                    <div class="space-y-3">
                        @foreach (var catalog in Model.RecentItems.Catalogs)
                        {
                            <div class="flex items-center justify-between p-3 rounded-lg bg-neutral-50 hover:bg-neutral-100 transition-colors">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0 w-10 h-10 bg-ardec-primary/10 rounded-full flex items-center justify-center">
                                        <i class="fas fa-book text-ardec-primary"></i>
                                    </div>
                                    <div>
                                        <h6 class="font-semibold text-neutral-800 mb-1">@catalog.TitoloBreve</h6>
                                        <p class="text-sm text-neutral-600">@catalog.TER</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm text-neutral-500">@catalog.Base</span>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-12">
                        <i class="fas fa-book text-4xl text-neutral-300 mb-4"></i>
                        <p class="text-neutral-500">Nessun catalogo disponibile</p>
                    </div>
                }
            </div>
            @if (Model.RecentItems.Catalogs?.Count > 0)
            {
                <div class="px-6 py-4 border-t border-neutral-200">
                    <a href="@Url.Action("Index", "Catalogs")" class="inline-flex items-center justify-center px-4 py-2 border border-ardec-primary text-ardec-primary font-medium rounded-lg hover:bg-ardec-primary hover:text-white transition-colors">
                        <i class="fas fa-list mr-2"></i>Visualizza Tutti
                    </a>
                </div>
            }
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="lg:col-span-1">
        <div class="bg-white rounded-lg border border-neutral-200 shadow-ardec">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h3 class="text-lg font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-bolt mr-3 text-ardec-primary"></i>
                    Azioni Rapide
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="/api-docs" class="block p-3 rounded-lg border border-neutral-200 hover:border-ardec-primary hover:bg-ardec-primary/5 transition-colors group">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-code text-ardec-primary"></i>
                                <span class="font-medium text-neutral-800">API Documentation</span>
                            </div>
                            <i class="fas fa-external-link-alt text-neutral-400 group-hover:text-ardec-primary transition-colors"></i>
                        </div>
                    </a>
                    
                    <a href="@Url.Action("Profile", "Account")" class="block p-3 rounded-lg border border-neutral-200 hover:border-info hover:bg-ardec-50 transition-colors group">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-user text-info"></i>
                                <span class="font-medium text-neutral-800">Profilo Utente</span>
                            </div>
                            <i class="fas fa-chevron-right text-neutral-400 group-hover:text-info transition-colors"></i>
                        </div>
                    </a>
                    
                    <a href="@Url.Action("Settings", "Account")" class="block p-3 rounded-lg border border-neutral-200 hover:border-warning hover:bg-ardec-50 transition-colors group">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-cog text-warning"></i>
                                <span class="font-medium text-neutral-800">Impostazioni</span>
                            </div>
                            <i class="fas fa-chevron-right text-neutral-400 group-hover:text-warning transition-colors"></i>
                        </div>
                    </a>
                    
                    <button onclick="location.reload()" class="w-full p-3 rounded-lg border border-neutral-200 hover:border-success hover:bg-ardec-50 transition-colors group text-left">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-sync-alt text-success"></i>
                                <span class="font-medium text-neutral-800">Aggiorna Dati</span>
                            </div>
                            <i class="fas fa-redo text-neutral-400 group-hover:text-success transition-colors"></i>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
