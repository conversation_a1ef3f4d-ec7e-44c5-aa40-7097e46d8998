@{
    ViewData["Title"] = "Dichiarazione di Accessibilità";
}

<!-- Accessibility Header -->
<div class="mb-8">
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-neutral-800 mb-2">
                <i class="fas fa-universal-access text-ardec-primary mr-3"></i>
                Dichiarazione di Accessibilità
            </h1>
            <p class="text-neutral-600">Impegno di ARDEC S.p.A. per l'accessibilità digitale</p>
        </div>
        <div>
            <small class="text-neutral-500">
                Ultimo aggiornamento: @DateTime.Now.ToString("dd MMMM yyyy")
            </small>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <div class="lg:col-span-2 space-y-6">
        <!-- Introduction -->
        <div class="bg-white rounded-lg border border-neutral-200 shadow-sm">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h5 class="text-lg font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-info-circle mr-2 text-ardec-primary"></i>
                    Il nostro impegno
                </h5>
            </div>
            <div class="px-6 py-6">
                <p class="text-lg text-neutral-700 mb-4">
                    ARDEC S.p.A. è impegnata a garantire l'accessibilità del sistema MIL-STD-1388 Services 
                    per tutti gli utenti, indipendentemente dalle loro capacità fisiche o cognitive.
                </p>
                <p class="text-neutral-600 mb-4">
                    Abbiamo progettato e sviluppato questo sistema seguendo le linee guida internazionali 
                    per l'accessibilità dei contenuti web (WCAG 2.1) e le migliori pratiche del settore.
                </p>
                <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                    <strong>Conformità WCAG 2.1 AA:</strong> Il sistema rispetta i criteri di accessibilità di livello AA.
                </div>
            </div>
        </div>

        <!-- Accessibility Features -->
        <div class="bg-white rounded-lg border border-neutral-200 shadow-sm">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h5 class="text-lg font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-list-check mr-2 text-ardec-primary"></i>
                    Caratteristiche di Accessibilità
                </h5>
            </div>
            <div class="px-6 py-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h6 class="font-semibold text-neutral-800 mb-4 flex items-center">
                            <i class="fas fa-eye mr-2 text-blue-600"></i>
                            Accessibilità Visiva
                        </h6>
                        <ul class="space-y-2">
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Contrasto colori conforme WCAG AA</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Testi ridimensionabili fino al 200%</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Font ad alta leggibilità (Inter)</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Indicatori focus visibili</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h6 class="font-semibold text-neutral-800 mb-4 flex items-center">
                            <i class="fas fa-keyboard mr-2 text-purple-600"></i>
                            Navigazione da Tastiera
                        </h6>
                        <ul class="space-y-2">
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Tutti gli elementi accessibili da tastiera</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Ordine di tabulazione logico</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Scorciatoie da tastiera disponibili</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Nessuna trappola per il focus</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h6 class="font-semibold text-neutral-800 mb-4 flex items-center">
                            <i class="fas fa-volume-up mr-2 text-orange-600"></i>
                            Screen Reader
                        </h6>
                        <ul class="space-y-2">
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Etichette semantiche complete</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Struttura heading appropriata</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Descrizioni alternative per immagini</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>ARIA labels e landmark</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h6 class="font-semibold text-neutral-800 mb-4 flex items-center">
                            <i class="fas fa-mobile-alt mr-2 text-green-600"></i>
                            Compatibilità Dispositivi
                        </h6>
                        <ul class="space-y-2">
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Design responsive per tutti i dispositivi</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Touch target di dimensione adeguata</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Orientamento schermo flessibile</span>
                            </li>
                            <li class="flex items-start text-sm text-neutral-600">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span>Compatibilità assistive technology</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Keyboard Shortcuts -->
        <div class="bg-white rounded-lg border border-neutral-200 shadow-sm">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h5 class="text-lg font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-keyboard mr-2 text-ardec-primary"></i>
                    Scorciatoie da Tastiera
                </h5>
            </div>
            <div class="px-6 py-6">
                <p class="text-neutral-600 mb-4">Il sistema supporta le seguenti scorciatoie da tastiera per migliorare la navigazione:</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                            <span class="text-neutral-700">Navigazione principale</span>
                            <kbd class="px-2 py-1 bg-neutral-200 rounded text-xs font-mono">Alt + N</kbd>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                            <span class="text-neutral-700">Vai al contenuto</span>
                            <kbd class="px-2 py-1 bg-neutral-200 rounded text-xs font-mono">Alt + C</kbd>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                            <span class="text-neutral-700">Cerca</span>
                            <kbd class="px-2 py-1 bg-neutral-200 rounded text-xs font-mono">Ctrl + K</kbd>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                            <span class="text-neutral-700">Dashboard</span>
                            <kbd class="px-2 py-1 bg-neutral-200 rounded text-xs font-mono">Alt + D</kbd>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                            <span class="text-neutral-700">Salva (quando disponibile)</span>
                            <kbd class="px-2 py-1 bg-neutral-200 rounded text-xs font-mono">Ctrl + S</kbd>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                            <span class="text-neutral-700">Annulla operazione</span>
                            <kbd class="px-2 py-1 bg-neutral-200 rounded text-xs font-mono">Ctrl + Z</kbd>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                            <span class="text-neutral-700">Aiuto/Help</span>
                            <kbd class="px-2 py-1 bg-neutral-200 rounded text-xs font-mono">F1</kbd>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                            <span class="text-neutral-700">Esci (Logout)</span>
                            <kbd class="px-2 py-1 bg-neutral-200 rounded text-xs font-mono">Alt + Q</kbd>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Standards Compliance -->
        <div class="bg-white rounded-lg border border-neutral-200 shadow-sm">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h5 class="text-lg font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-certificate mr-2 text-ardec-primary"></i>
                    Conformità agli Standard
                </h5>
            </div>
            <div class="px-6 py-6">
                <div class="space-y-4">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-green-50 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-check-double text-green-600"></i>
                        </div>
                        <div>
                            <h6 class="font-semibold text-neutral-800 mb-2">WCAG 2.1 - Livello AA</h6>
                            <p class="text-neutral-600 mb-2">
                                Il sistema è conforme ai criteri di successo di livello AA delle Web Content Accessibility Guidelines 2.1.
                            </p>
                            <small class="text-neutral-500">
                                Ultima verifica: @DateTime.Now.ToString("MMMM yyyy")
                            </small>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-universal-access text-blue-600"></i>
                        </div>
                        <div>
                            <h6 class="font-semibold text-neutral-800 mb-2">Accessibilità Web Italiana</h6>
                            <p class="text-neutral-600 mb-2">
                                Conformità alla Legge Stanca (L. 4/2004) e alle linee guida AgID per l'accessibilità.
                            </p>
                            <small class="text-neutral-500">
                                Rispetto dei requisiti tecnici nazionali
                            </small>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-purple-50 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-globe text-purple-600"></i>
                        </div>
                        <div>
                            <h6 class="font-semibold text-neutral-800 mb-2">Standard Internazionali</h6>
                            <p class="text-neutral-600 mb-2">
                                Aderenza alle convenzioni ISO/IEC 40500 e alle migliori pratiche internazionali.
                            </p>
                            <small class="text-neutral-500">
                                Riconoscimento a livello europeo e mondiale
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1 space-y-6">
        <!-- Feedback -->
        <div class="bg-white rounded-lg border border-neutral-200 shadow-sm">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h6 class="font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-comments mr-2 text-ardec-primary"></i>
                    Feedback e Segnalazioni
                </h6>
            </div>
            <div class="px-6 py-6">
                <p class="text-sm text-neutral-600 mb-4">
                    La tua opinione è importante per migliorare l'accessibilità del sistema.
                </p>
                
                <div class="space-y-3">
                    <a href="mailto:<EMAIL>" class="w-full bg-ardec-primary hover:bg-ardec-primary-dark text-white px-3 py-2 rounded-lg transition-colors inline-flex items-center justify-center font-medium text-sm">
                        <i class="fas fa-envelope mr-2"></i>Segnala Problema
                    </a>
                    <a href="tel:+39-xxx-xxxxxxx" class="w-full bg-white border border-neutral-300 text-neutral-700 hover:bg-neutral-50 px-3 py-2 rounded-lg transition-colors inline-flex items-center justify-center font-medium text-sm">
                        <i class="fas fa-phone mr-2"></i>Supporto Telefonico
                    </a>
                </div>
                
                <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <small class="text-blue-700">
                        <i class="fas fa-clock mr-1"></i>
                        <strong>Tempo di risposta:</strong> Entro 2 giorni lavorativi
                    </small>
                </div>
            </div>
        </div>

        <!-- Assistive Technologies -->
        <div class="bg-white rounded-lg border border-neutral-200 shadow-sm">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h6 class="font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-assistive-listening-systems mr-2 text-ardec-primary"></i>
                    Tecnologie Assistive
                </h6>
            </div>
            <div class="px-6 py-6">
                <p class="text-sm text-neutral-600 mb-4">Compatibilità testata con:</p>
                
                <div class="space-y-3">
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-600 mr-3"></i>
                        <span class="text-neutral-700">JAWS (Windows)</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-600 mr-3"></i>
                        <span class="text-neutral-700">NVDA (Windows)</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-600 mr-3"></i>
                        <span class="text-neutral-700">VoiceOver (macOS/iOS)</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-600 mr-3"></i>
                        <span class="text-neutral-700">Dragon NaturallySpeaking</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-600 mr-3"></i>
                        <span class="text-neutral-700">Switch Control</span>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-neutral-200">
                    <small class="text-neutral-500">
                        Testato regolarmente su versioni recenti delle tecnologie assistive più diffuse.
                    </small>
                </div>
            </div>
        </div>

        <!-- Quick Settings -->
        <div class="bg-white rounded-lg border border-neutral-200 shadow-sm">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h6 class="font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-sliders-h mr-2 text-ardec-primary"></i>
                    Impostazioni Rapide
                </h6>
            </div>
            <div class="px-6 py-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <label for="high-contrast" class="text-sm font-medium text-neutral-700">Alto Contrasto</label>
                        <button id="high-contrast" class="w-12 h-6 bg-neutral-300 rounded-full relative focus:outline-none focus:ring-2 focus:ring-ardec-primary" onclick="toggleHighContrast()">
                            <span class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5 transition-transform"></span>
                        </button>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <label for="large-text" class="text-sm font-medium text-neutral-700">Testo Grande</label>
                        <button id="large-text" class="w-12 h-6 bg-neutral-300 rounded-full relative focus:outline-none focus:ring-2 focus:ring-ardec-primary" onclick="toggleLargeText()">
                            <span class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5 transition-transform"></span>
                        </button>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <label for="reduce-motion" class="text-sm font-medium text-neutral-700">Riduci Animazioni</label>
                        <button id="reduce-motion" class="w-12 h-6 bg-neutral-300 rounded-full relative focus:outline-none focus:ring-2 focus:ring-ardec-primary" onclick="toggleReduceMotion()">
                            <span class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5 transition-transform"></span>
                        </button>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-neutral-200">
                    <button onclick="resetAccessibilitySettings()" class="w-full text-sm text-neutral-600 hover:text-neutral-800 underline">
                        Ripristina Impostazioni Default
                    </button>
                </div>
            </div>
        </div>

        <!-- Legal Links -->
        <div class="bg-white rounded-lg border border-neutral-200 shadow-sm">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h6 class="font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-balance-scale mr-2 text-ardec-primary"></i>
                    Informazioni Legali
                </h6>
            </div>
            <div class="px-6 py-6">
                <div class="space-y-2">
                    <a href="@Url.Action("Privacy", "Home")" class="w-full bg-white border border-neutral-300 text-neutral-700 hover:bg-neutral-50 px-3 py-2 rounded-lg transition-colors inline-flex items-center justify-center font-medium text-sm">
                        <i class="fas fa-shield-alt mr-2"></i>Privacy Policy
                    </a>
                    <a href="@Url.Action("CookiePolicy", "Home")" class="w-full bg-white border border-neutral-300 text-neutral-700 hover:bg-neutral-50 px-3 py-2 rounded-lg transition-colors inline-flex items-center justify-center font-medium text-sm">
                        <i class="fas fa-cookie-bite mr-2"></i>Cookie Policy
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
function toggleHighContrast() {
    const button = document.getElementById('high-contrast');
    const span = button.querySelector('span');
    const isActive = span.style.transform === 'translateX(1.5rem)';
    
    if (isActive) {
        span.style.transform = 'translateX(0)';
        button.classList.remove('bg-ardec-primary');
        button.classList.add('bg-neutral-300');
        document.body.classList.remove('high-contrast');
    } else {
        span.style.transform = 'translateX(1.5rem)';
        button.classList.remove('bg-neutral-300');
        button.classList.add('bg-ardec-primary');
        document.body.classList.add('high-contrast');
    }
    
    // Save preference
    localStorage.setItem('highContrast', !isActive);
}

function toggleLargeText() {
    const button = document.getElementById('large-text');
    const span = button.querySelector('span');
    const isActive = span.style.transform === 'translateX(1.5rem)';
    
    if (isActive) {
        span.style.transform = 'translateX(0)';
        button.classList.remove('bg-ardec-primary');
        button.classList.add('bg-neutral-300');
        document.documentElement.style.fontSize = '';
    } else {
        span.style.transform = 'translateX(1.5rem)';
        button.classList.remove('bg-neutral-300');
        button.classList.add('bg-ardec-primary');
        document.documentElement.style.fontSize = '118%';
    }
    
    // Save preference
    localStorage.setItem('largeText', !isActive);
}

function toggleReduceMotion() {
    const button = document.getElementById('reduce-motion');
    const span = button.querySelector('span');
    const isActive = span.style.transform === 'translateX(1.5rem)';
    
    if (isActive) {
        span.style.transform = 'translateX(0)';
        button.classList.remove('bg-ardec-primary');
        button.classList.add('bg-neutral-300');
        document.body.classList.remove('reduce-motion');
    } else {
        span.style.transform = 'translateX(1.5rem)';
        button.classList.remove('bg-neutral-300');
        button.classList.add('bg-ardec-primary');
        document.body.classList.add('reduce-motion');
    }
    
    // Save preference
    localStorage.setItem('reduceMotion', !isActive);
}

function resetAccessibilitySettings() {
    // Reset all toggles
    const toggles = ['high-contrast', 'large-text', 'reduce-motion'];
    toggles.forEach(id => {
        const button = document.getElementById(id);
        const span = button.querySelector('span');
        span.style.transform = 'translateX(0)';
        button.classList.remove('bg-ardec-primary');
        button.classList.add('bg-neutral-300');
    });
    
    // Reset styles
    document.body.classList.remove('high-contrast', 'reduce-motion');
    document.documentElement.style.fontSize = '';
    
    // Clear localStorage
    localStorage.removeItem('highContrast');
    localStorage.removeItem('largeText');
    localStorage.removeItem('reduceMotion');
    
    alert('Impostazioni di accessibilità ripristinate');
}

// Load saved preferences on page load
document.addEventListener('DOMContentLoaded', function() {
    if (localStorage.getItem('highContrast') === 'true') {
        toggleHighContrast();
    }
    if (localStorage.getItem('largeText') === 'true') {
        toggleLargeText();
    }
    if (localStorage.getItem('reduceMotion') === 'true') {
        toggleReduceMotion();
    }
});
</script>

<style>
.high-contrast {
    filter: contrast(150%);
}

.reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}
</style>
}
