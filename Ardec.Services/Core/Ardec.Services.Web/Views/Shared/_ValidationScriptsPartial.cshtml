@*
    Client-side validation scripts for ASP.NET Core MVC forms
    This partial view includes jQuery validation libraries for client-side form validation
*@

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" 
        integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" 
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js" 
        integrity="sha512-rstIgDs0xPgmG6RX1Aba4KV5cWJbAMcvRCVmglpam9SoHZiUCyQVDdH2LPlxoHtrv17XWblE/V/PP+Tr04hbtA==" 
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validation-unobtrusive/3.2.12/jquery.validate.unobtrusive.min.js" 
        integrity="sha512-o6XOBlF6OcOAhTS3rGTi7lG+1XBLLHIHvO4F6h5vGLU7EPGA2dJJ2BvJgJjwqjHZ3wkMK8NqmKnTi1LUg0b3Qg==" 
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<script>
    // Initialize client-side validation
    $(document).ready(function() {
        // Enable client-side validation for all forms
        $.validator.setDefaults({
            highlight: function(element) {
                $(element).addClass('border-red-500 focus:ring-red-500 focus:border-red-500')
                          .removeClass('border-gray-300 focus:ring-ardec-primary focus:border-ardec-primary');
            },
            unhighlight: function(element) {
                $(element).removeClass('border-red-500 focus:ring-red-500 focus:border-red-500')
                          .addClass('border-gray-300 focus:ring-ardec-primary focus:border-ardec-primary');
            },
            errorElement: 'span',
            errorClass: 'text-red-500 text-sm block mt-1',
            errorPlacement: function(error, element) {
                // Place error message after the input element
                var container = element.closest('div');
                var existingError = container.find('.text-red-500.text-sm');
                if (existingError.length > 0) {
                    existingError.replaceWith(error);
                } else {
                    error.insertAfter(element);
                }
            }
        });

        console.log('✅ Client-side validation initialized');
    });
</script>
