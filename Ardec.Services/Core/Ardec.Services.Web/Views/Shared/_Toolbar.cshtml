@model dynamic

@* Reusable Toolbar Component - Tailwind *@
<div class="flex justify-between items-center mb-6 p-4 bg-white rounded-lg shadow-ardec">
    <h1 class="text-title text-neutral-800 font-semibold">
        @(ViewBag.ToolbarTitle ?? "Gestione")
    </h1>
    <div class="flex space-x-3">
        @if (ViewBag.ShowUndoRedo == true)
        {
            <button id="btnUndo" class="bg-white border border-neutral-300 text-neutral-700 hover:bg-neutral-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium disabled:opacity-50 disabled:cursor-not-allowed" title="<PERSON><PERSON><PERSON> (Ctrl+Z)" disabled>
                <i class="fas fa-undo mr-2"></i>Annulla
            </button>
            <button id="btnRedo" class="bg-white border border-neutral-300 text-neutral-700 hover:bg-neutral-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium disabled:opacity-50 disabled:cursor-not-allowed" title="Ripristina (Ctrl+Y)" disabled>
                <i class="fas fa-redo mr-2"></i>Ripristina
            </button>
        }
        @if (ViewBag.ShowSave == true)
        {
            <button id="btnSave" class="bg-ardec-primary hover:bg-ardec-primary-dark text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm disabled:opacity-50 disabled:cursor-not-allowed" title="Salva (Ctrl+S)" disabled>
                <i class="fas fa-save mr-2"></i>Salva
            </button>
        }
        @Html.Raw(ViewBag.ExtraButtons ?? "")
    </div>
</div>