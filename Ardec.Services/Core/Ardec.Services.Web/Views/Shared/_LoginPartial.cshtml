@using Microsoft.AspNetCore.Identity
@inject SignInManager<IdentityUser> SignInManager
@inject UserManager<IdentityUser> UserManager

<div class="flex items-center space-x-2">
@if (SignInManager.IsSignedIn(User))
{
    <!-- User Profile Link -->
    <a href="@Url.Action("Profile", "Account")" 
       class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center" 
       title="Profilo">
        <i class="fas fa-user mr-2"></i>@User.Identity?.Name!
    </a>
    
    <!-- Logout Form -->
    <form asp-controller="Account" asp-action="Logout" asp-route-returnUrl="@Url.Action("Index", "Home")" class="inline">
        <button type="submit" 
                class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center bg-transparent border-0 cursor-pointer">
            <i class="fas fa-sign-out-alt mr-2"></i>Logout
        </button>
    </form>
}
else
{
    <!-- Login Link -->
    <a href="@Url.Action("Login", "Account")" 
       class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center">
        <i class="fas fa-sign-in-alt mr-2"></i>Login
    </a>
}
</div>
