<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - MIL-STD-1388 Services</title>
    
    <!-- Font Awesome 6 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Flowbite CSS -->
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
    
    <!-- DataTables CSS (Tailwind compatible) -->
    <link href="https://cdn.datatables.net/2.0.0/css/jquery.dataTables.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/3.0.0/css/buttons.dataTables.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/3.0.0/css/responsive.dataTables.min.css" rel="stylesheet">

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="~/css/tailwind.min.css?v=@DateTime.Now.ToString("yyyyMMddHHmmss")" />
    
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <nav class="bg-gradient-to-r from-ardec-primary to-ardec-primary-dark fixed top-0 left-0 right-0 z-50 shadow-ardec">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <!-- Brand -->
                <a href="@Url.Action("Index", "Home")" class="flex items-center hover:opacity-90 transition-opacity">
                    <img src="~/images/logo_icon_ardec.png" alt="ARDEC" class="h-8 w-auto mr-3" />
                    <span class="text-white font-bold text-xl tracking-wide">MIL-STD-1388 Services</span>
                </a>

                <!-- Mobile menu button -->
                <button type="button" class="md:hidden text-white hover:text-neutral-200 focus:outline-none" id="mobile-menu-button">
                    <i class="fas fa-bars text-xl"></i>
                </button>

                <!-- Desktop menu -->
                <div class="hidden md:flex items-center space-x-6">
                    <!-- Main navigation -->
                    <div class="flex space-x-2">
                        <a href="@Url.Action("Index", "Home")" class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center">
                            <i class="fas fa-home mr-2"></i>Home
                        </a>
                        <a href="@Url.Action("Index", "Catalogs")" class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center">
                            <i class="fas fa-book mr-2"></i>Cataloghi
                        </a>
                        <a href="@Url.Action("Index", "Tables")" class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center">
                            <i class="fas fa-table mr-2"></i>Tavole
                        </a>
                        <a href="@Url.Action("Index", "Parts")" class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center">
                            <i class="fas fa-puzzle-piece mr-2"></i>Parti
                        </a>
                        
                        @* Admin-only User Management *@
                        @if (User.IsInRole("Admin"))
                        {
                            <a href="@Url.Action("Index", "UserManagement")" class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center">
                                <i class="fas fa-users-cog mr-2"></i>Utenti
                            </a>
                        }

                        <!-- Tools dropdown -->
                        <div class="relative group">
                            <button class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center">
                                <i class="fas fa-tools mr-2"></i>Strumenti
                                <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-2">
                                    <a href="/api-docs" class="block px-4 py-2 text-neutral-700 hover:bg-blue-50 transition-colors">
                                        <i class="fas fa-code mr-2 text-ardec-primary"></i>API Documentation
                                    </a>
                                    <a href="#" class="block px-4 py-2 text-neutral-700 hover:bg-blue-50 transition-colors">
                                        <i class="fas fa-download mr-2 text-ardec-primary"></i>Export Tools
                                    </a>
                                    <hr class="my-2 border-neutral-200">
                                    <a href="#" class="block px-4 py-2 text-neutral-700 hover:bg-blue-50 transition-colors">
                                        <i class="fas fa-chart-bar mr-2 text-ardec-primary"></i>Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User menu -->
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <div class="relative group">
                            <button class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center">
                                <i class="fas fa-user-circle mr-2"></i>@User.Identity.Name
                                <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </button>
                            <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-2">
                                    <a href="@Url.Action("Profile", "Account")" class="block px-4 py-2 text-neutral-700 hover:bg-blue-50 transition-colors">
                                        <i class="fas fa-user mr-2 text-ardec-primary"></i>Profilo
                                    </a>
                                    <a href="@Url.Action("Settings", "Account")" class="block px-4 py-2 text-neutral-700 hover:bg-blue-50 transition-colors">
                                        <i class="fas fa-cog mr-2 text-ardec-primary"></i>Impostazioni
                                    </a>
                                    <hr class="my-2 border-neutral-200">
                                    <form method="post" asp-action="Logout" asp-controller="Account" class="block">
                                        <button type="submit" class="w-full text-left px-4 py-2 text-neutral-700 hover:bg-blue-50 transition-colors">
                                            <i class="fas fa-sign-out-alt mr-2 text-ardec-primary"></i>Logout
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <a href="@Url.Action("Login", "Account")" class="text-white hover:bg-white/10 px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center">
                            <i class="fas fa-sign-in-alt mr-2"></i>Login
                        </a>
                    }
                </div>
            </div>

            <!-- Mobile menu -->
            <div class="md:hidden hidden" id="mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-ardec-primary-dark rounded-lg mt-2 shadow-lg">
                    <a href="@Url.Action("Index", "Home")" class="block text-white hover:bg-white/10 px-3 py-2 rounded font-medium transition-colors flex items-center">
                        <i class="fas fa-home mr-2"></i>Home
                    </a>
                    <a href="@Url.Action("Index", "Catalogs")" class="block text-white hover:bg-white/10 px-3 py-2 rounded font-medium transition-colors flex items-center">
                        <i class="fas fa-book mr-2"></i>Cataloghi
                    </a>
                    <a href="@Url.Action("Index", "Tables")" class="block text-white hover:bg-white/10 px-3 py-2 rounded font-medium transition-colors flex items-center">
                        <i class="fas fa-table mr-2"></i>Tavole
                    </a>
                    <a href="@Url.Action("Index", "Parts")" class="block text-white hover:bg-white/10 px-3 py-2 rounded font-medium transition-colors flex items-center">
                        <i class="fas fa-puzzle-piece mr-2"></i>Parti
                    </a>
                    
                    @* Admin-only User Management Mobile *@
                    @if (User.IsInRole("Admin"))
                    {
                        <a href="@Url.Action("Index", "UserManagement")" class="block text-white hover:bg-white/10 px-3 py-2 rounded font-medium transition-colors flex items-center">
                            <i class="fas fa-users-cog mr-2"></i>Utenti
                        </a>
                    }
                    
                    <a href="/api-docs" class="block text-white hover:bg-white/10 px-3 py-2 rounded font-medium transition-colors flex items-center">
                        <i class="fas fa-code mr-2"></i>API Docs
                    </a>
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <a href="@Url.Action("Profile", "Account")" class="block text-white hover:bg-white/10 px-3 py-2 rounded font-medium transition-colors flex items-center">
                            <i class="fas fa-user mr-2"></i>Profilo
                        </a>
                        <form method="post" asp-action="Logout" asp-controller="Account" class="block">
                            <button type="submit" class="w-full text-left text-white hover:bg-white/10 px-3 py-2 rounded font-medium transition-colors flex items-center">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </button>
                        </form>
                    }
                    else
                    {
                        <a href="@Url.Action("Login", "Account")" class="block text-white hover:bg-white/10 px-3 py-2 rounded font-medium transition-colors flex items-center">
                            <i class="fas fa-sign-in-alt mr-2"></i>Login
                        </a>
                    }
                </div>
            </div>
        </div>
    </nav>

    <main role="main" class="pt-20 min-h-screen bg-neutral-50">
        <div class="max-w-7xl mx-auto px-4 py-6">
            @RenderBody()
        </div>
    </main>

    <footer class="bg-gradient-to-r from-ardec-primary to-ardec-primary-dark py-8 mt-16">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex flex-col items-center md:items-start mb-4 md:mb-0">
                    <div class="flex items-center mb-2">
                        <img src="~/images/logo_icon_ardec.png" alt="ARDEC" class="h-6 w-auto mr-3" />
                        <span class="text-white font-bold text-lg">MIL-STD-1388 Services</span>
                    </div>
                    <small class="text-white/70">
                        &copy; @DateTime.Now.Year ARDEC S.p.A. Tutti i diritti riservati.
                    </small>
                </div>
                <div class="text-center md:text-right">
                    <a href="https://www.ardec-spa.it" class="text-white hover:text-neutral-200 transition-colors" target="_blank">
                        <i class="fas fa-globe mr-2"></i>www.ardec-spa.it
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- jQuery 3.7 -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Flowbite JS -->
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    
    <!-- DataTables with Flowbite Integration (conditionally loaded) -->
    <script src="https://cdn.datatables.net/2.0.0/js/dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.0.0/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/3.0.0/js/dataTables.responsive.min.js"></script>

    <!-- Custom Scripts -->
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    <!-- Shared JavaScript Modules -->
    <script src="~/js/shared/api-client.js" asp-append-version="true"></script>
    <script src="~/js/shared/notifications.js" asp-append-version="true"></script>
    <script src="~/js/shared/validation.js" asp-append-version="true"></script>
    
    <!-- Feature-specific JavaScript Modules -->
    <script src="~/js/modules/user-manager.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)

    <!-- Toast Container (Tailwind) -->
    <div class="fixed bottom-4 right-4 z-50 space-y-2" id="toast-container"></div>

    <!-- Mobile menu toggle script -->
    <script>
        document.getElementById('mobile-menu-button')?.addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>
</body>
</html>