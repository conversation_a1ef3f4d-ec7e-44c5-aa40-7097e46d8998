@model Ardec.Services.Web.Features.Account.ProfileViewModel
@{
    ViewData["Title"] = "Profilo Utente";
    ViewBag.ToolbarTitle = "Profilo Utente";
    ViewBag.ToolbarSubtitle = "Visualizza le informazioni e le autorizzazioni del tuo account";
    ViewBag.ExtraButtons = $@"<a href='{Url.Action("Settings", "Account")}' class=""inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors""><i class=""fas fa-cog mr-2""></i>Modifica Profilo</a>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Profile Layout -->
<div class="max-w-4xl mx-auto space-y-6">
        
        <!-- Profile Overview Card -->
        <div class="bg-white rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-user-circle mr-3 text-blue-600"></i>
                    Profilo Utente
                </h2>
            </div>
            <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Profile Avatar -->
                        <div class="text-center">
                            <div class="mb-4">
                                <div class="w-24 h-24 mx-auto bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center border-4 border-white shadow-lg">
                                    <i class="fas fa-user text-white text-2xl"></i>
                                </div>
                            </div>
                            <h4 class="text-xl font-bold text-gray-800 mb-2">@Model.UserName</h4>
                            <p class="text-gray-600 mb-4">@Model.Email</p>
                            
                            <!-- Primary Role Badge -->
                            @if (Model.Roles.Any())
                            {
                                <div class="mb-4">
                                    @{
                                        var primaryRole = Model.Roles.First();
                                    }
                                    @if (primaryRole == "Admin")
                                    {
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                                            <i class="fas fa-crown mr-1"></i>@primaryRole
                                        </span>
                                    }
                                    else if (primaryRole == "PowerUser")
                                    {
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 border border-blue-200">
                                            <i class="fas fa-star mr-1"></i>@primaryRole
                                        </span>
                                    }
                                    else if (primaryRole == "Editor")
                                    {
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-cyan-100 text-cyan-800 border border-cyan-200">
                                            <i class="fas fa-edit mr-1"></i>@primaryRole
                                        </span>
                                    }
                                    else if (primaryRole == "Viewer")
                                    {
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 border border-gray-200">
                                            <i class="fas fa-eye mr-1"></i>@primaryRole
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 border border-gray-200">
                                            <i class="fas fa-user mr-1"></i>@primaryRole
                                        </span>
                                    }
                                </div>
                            }
                            
                            <!-- Status Badges -->
                            <div class="space-y-2">
                                @if (Model.EmailConfirmed)
                                {
                                    <div class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 border border-green-200">
                                        <i class="fas fa-check-circle mr-1"></i>Email Verificata
                                    </div>
                                }
                                else
                                {
                                    <div class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800 border border-yellow-200">
                                        <i class="fas fa-exclamation-circle mr-1"></i>Email Non Verificata
                                    </div>
                                }
                                
                                @if (Model.LockoutEnabled)
                                {
                                    <div class="block mt-2">
                                        <div class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-cyan-100 text-cyan-800 border border-cyan-200">
                                            <i class="fas fa-shield-alt mr-1"></i>Protezione Attiva
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                        
                        <!-- Account Details -->
                        <div class="md:col-span-2">
                            <h6 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                                <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                                Dettagli Account
                            </h6>
                            
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <!-- Username Field -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        <i class="fas fa-user mr-2 text-blue-600"></i>
                                        Nome Utente
                                    </label>
                                    <div class="bg-gray-50 border border-gray-200 rounded-lg px-3 py-2 text-gray-800">
                                        @Model.UserName
                                    </div>
                                </div>
                                
                                <!-- Email Field -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        <i class="fas fa-envelope mr-2 text-blue-600"></i>
                                        Email
                                    </label>
                                    <div class="bg-gray-50 border border-gray-200 rounded-lg px-3 py-2 text-gray-800 flex items-center justify-between">
                                        <span>@Model.Email</span>
                                        @if (Model.EmailConfirmed)
                                        {
                                            <i class="fas fa-check-circle text-green-500" title="Email verificata"></i>
                                        }
                                    </div>
                                </div>
                                
                                <!-- Access Failed Count -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        <i class="fas fa-exclamation-triangle mr-2 text-blue-600"></i>
                                        Tentativi Falliti
                                    </label>
                                    <div class="bg-gray-50 border border-gray-200 rounded-lg px-3 py-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium @(Model.AccessFailedCount > 0 ? "bg-yellow-100 text-yellow-800" : "bg-green-100 text-green-800")">
                                            @Model.AccessFailedCount / 5
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- Lockout Protection -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        <i class="fas fa-lock mr-2 text-blue-600"></i>
                                        Protezione Account
                                    </label>
                                    <div class="bg-gray-50 border border-gray-200 rounded-lg px-3 py-2">
                                        @if (Model.LockoutEnabled)
                                        {
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i class="fas fa-check mr-1"></i>Abilitata
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                                <i class="fas fa-times mr-1"></i>Disabilitata
                                            </span>
                                        }
                                    </div>
                                </div>
                            </div>

                            <!-- Account Stats -->
                            <div class="mt-6 pt-4 border-t border-gray-200">
                                <div class="grid grid-cols-3 gap-4 text-center">
                                    <div class="space-y-1">
                                        <div class="text-2xl font-bold text-blue-600">@Model.Roles.Count</div>
                                        <div class="text-sm text-gray-600 uppercase tracking-wide">Ruoli</div>
                                    </div>
                                    <div class="space-y-1">
                                        <div class="text-2xl font-bold text-green-600">
                                            @(Model.EmailConfirmed ? "100%" : "0%")
                                        </div>
                                        <div class="text-sm text-gray-600 uppercase tracking-wide">Verifica</div>
                                    </div>
                                    <div class="space-y-1">
                                        <div class="text-2xl font-bold @(Model.AccessFailedCount == 0 ? "text-green-600" : "text-yellow-600")">
                                            @(5 - Model.AccessFailedCount)
                                        </div>
                                        <div class="text-sm text-gray-600 uppercase tracking-wide">Tentativi</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <!-- Roles Card -->
        <div class="bg-white rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-users mr-3 text-blue-600"></i>
                    Ruoli e Autorizzazioni
                </h2>
            </div>
            <div class="p-6">
                    @if (Model.Roles.Any())
                    {
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach (var role in Model.Roles)
                            {
                                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:bg-blue-50 transition-colors">
                                    <div class="flex items-start space-x-3">
                                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-white shadow-sm flex items-center justify-center">
                                            @switch (role)
                                            {
                                                case "Admin":
                                                    <i class="fas fa-crown text-yellow-500 text-xl"></i>
                                                    break;
                                                case "PowerUser":
                                                    <i class="fas fa-star text-blue-600 text-xl"></i>
                                                    break;
                                                case "Editor":
                                                    <i class="fas fa-edit text-cyan-500 text-xl"></i>
                                                    break;
                                                case "Viewer":
                                                    <i class="fas fa-eye text-gray-500 text-xl"></i>
                                                    break;
                                                default:
                                                    <i class="fas fa-user text-gray-400 text-xl"></i>
                                                    break;
                                            }
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="font-semibold text-blue-600 mb-1">@role</h6>
                                            <p class="text-sm text-gray-600 leading-relaxed">
                                                @switch (role)
                                                {
                                                    case "Admin":
                                                        <text>Accesso completo al sistema con tutte le funzionalità</text>
                                                        break;
                                                    case "PowerUser":
                                                        <text>Modifica avanzata ed export di cataloghi e parti</text>
                                                        break;
                                                    case "Editor":
                                                        <text>Modifica di parti e tavole tecniche assegnate</text>
                                                        break;
                                                    case "Viewer":
                                                        <text>Visualizzazione di cataloghi e documenti</text>
                                                        break;
                                                    default:
                                                        <text>Ruolo personalizzato con permessi specifici</text>
                                                        break;
                                                }
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-12">
                            <i class="fas fa-user-times text-4xl text-gray-300 mb-4"></i>
                            <h6 class="text-lg font-semibold text-gray-600 mb-2">Nessun ruolo assegnato</h6>
                            <p class="text-gray-500">Contatta l'amministratore per richiedere autorizzazioni</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
