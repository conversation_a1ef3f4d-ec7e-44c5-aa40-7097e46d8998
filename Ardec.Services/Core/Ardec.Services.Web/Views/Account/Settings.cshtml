@model Ardec.Services.Web.Features.Account.AccountSettingsViewModel
@{
    ViewData["Title"] = "Impostazioni Account";
    ViewBag.ToolbarTitle = "Impostazioni Account";
    ViewBag.ToolbarSubtitle = "Gestisci le impostazioni del tuo account e la sicurezza";
    ViewBag.ExtraButtons = $@"<a href='{Url.Action("Profile", "Account")}' class=""bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm""><i class=""fas fa-user mr-2""></i>Visualizza Profilo</a>";
}

@await Html.PartialAsync("_Toolbar")

<!-- Success Message -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="mb-6">
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 flex items-start space-x-3">
            <i class="fas fa-check-circle text-green-600 flex-shrink-0 mt-0.5"></i>
            <div class="flex-1">
                <span class="text-neutral-800">@TempData["SuccessMessage"]</span>
            </div>
            <button onclick="this.parentElement.parentElement.style.display='none'" class="text-neutral-400 hover:text-neutral-600 transition-colors">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
}

<!-- Error Messages -->
@if (!ViewData.ModelState.IsValid)
{
    <div class="mb-6">
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3">
            <i class="fas fa-exclamation-triangle text-red-600 flex-shrink-0 mt-0.5"></i>
            <div class="flex-1">
                <strong class="font-semibold text-neutral-800">Attenzione:</strong>
                <span class="text-neutral-700 ml-2">Ci sono errori nel form che devono essere corretti.</span>
            </div>
            <button onclick="this.parentElement.parentElement.style.display='none'" class="text-neutral-400 hover:text-neutral-600 transition-colors">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
}

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Main Settings Form -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h5 class="text-lg font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-user-edit mr-2 text-ardec-primary"></i>
                    Informazioni Account
                </h5>
            </div>
            <div class="px-6 py-6">
                <form asp-action="Settings" method="post" class="space-y-6">
                    <!-- Account Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Username Field -->
                        <div>
                            <label asp-for="UserName" class="block text-sm font-medium text-neutral-700 mb-2">
                                <i class="fas fa-user mr-2 text-ardec-primary"></i>Nome Utente
                            </label>
                            <input asp-for="UserName" type="text" class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ardec-primary focus:border-transparent" placeholder="Inserisci nome utente">
                            <span asp-validation-for="UserName" class="text-red-600 text-sm mt-1 block"></span>
                        </div>
                        
                        <!-- Email Field -->
                        <div>
                            <label asp-for="Email" class="block text-sm font-medium text-neutral-700 mb-2">
                                <i class="fas fa-envelope mr-2 text-ardec-primary"></i>Email
                            </label>
                            <input asp-for="Email" type="email" class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ardec-primary focus:border-transparent" placeholder="<EMAIL>">
                            <span asp-validation-for="Email" class="text-red-600 text-sm mt-1 block"></span>
                            @if (!Model.EmailConfirmed)
                            {
                                <div class="text-sm text-amber-600 mt-1 flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>Email non confermata
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Account Status -->
                    <div>
                        <h6 class="text-base font-semibold text-neutral-700 mb-3">Stato Account</h6>
                        <div class="flex flex-wrap gap-2">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @(Model.EmailConfirmed ? "bg-green-50 text-green-700 border border-green-200" : "bg-amber-50 text-amber-700 border border-amber-200")">
                                <i class="fas fa-@(Model.EmailConfirmed ? "check" : "exclamation-triangle") mr-1"></i>
                                Email @(Model.EmailConfirmed ? "Confermata" : "Non Confermata")
                            </span>
                            @foreach (var role in Model.Roles)
                            {
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <i class="fas fa-shield-alt mr-1"></i>@role
                                </span>
                            }
                        </div>
                    </div>

                    <!-- Password Change Section -->
                    <div class="border-t border-neutral-200 pt-6">
                        <h6 class="text-base font-semibold text-neutral-700 mb-2 flex items-center">
                            <i class="fas fa-key mr-2 text-ardec-primary"></i>Cambia Password
                        </h6>
                        <p class="text-sm text-neutral-600 mb-4">
                            Lascia vuoti questi campi se non vuoi cambiare la password
                        </p>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Current Password -->
                            <div>
                                <label asp-for="CurrentPassword" class="block text-sm font-medium text-neutral-700 mb-1">Password Attuale</label>
                                <div class="relative">
                                    <input asp-for="CurrentPassword" type="password" class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ardec-primary focus:border-transparent pr-10" id="currentPasswordInput">
                                    <button type="button" class="absolute inset-y-0 right-0 px-3 flex items-center text-neutral-400 hover:text-neutral-600 toggle-password" data-target="currentPasswordInput">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="CurrentPassword" class="text-red-600 text-sm mt-1 block"></span>
                            </div>
                            
                            <!-- New Password -->
                            <div>
                                <label asp-for="NewPassword" class="block text-sm font-medium text-neutral-700 mb-1">Nuova Password</label>
                                <div class="relative">
                                    <input asp-for="NewPassword" type="password" class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ardec-primary focus:border-transparent pr-10" id="newPasswordInput">
                                    <button type="button" class="absolute inset-y-0 right-0 px-3 flex items-center text-neutral-400 hover:text-neutral-600 toggle-password" data-target="newPasswordInput">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="NewPassword" class="text-red-600 text-sm mt-1 block"></span>
                                <div id="password-strength" class="mt-1"></div>
                            </div>
                            
                            <!-- Confirm Password -->
                            <div>
                                <label asp-for="ConfirmNewPassword" class="block text-sm font-medium text-neutral-700 mb-1">Conferma Password</label>
                                <div class="relative">
                                    <input asp-for="ConfirmNewPassword" type="password" class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ardec-primary focus:border-transparent pr-10" id="confirmPasswordInput">
                                    <button type="button" class="absolute inset-y-0 right-0 px-3 flex items-center text-neutral-400 hover:text-neutral-600 toggle-password" data-target="confirmPasswordInput">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="ConfirmNewPassword" class="text-red-600 text-sm mt-1 block"></span>
                            </div>
                        </div>

                        <div class="mt-3">
                            <p class="text-sm text-neutral-600">
                                La password deve essere di almeno 6 caratteri e contenere lettere, numeri e caratteri speciali.
                            </p>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-between items-center pt-6 border-t border-neutral-200">
                        <div class="text-sm text-neutral-600 flex items-center">
                            <i class="fas fa-info-circle mr-1"></i>
                            Le modifiche avranno effetto immediato
                        </div>
                        <div class="flex space-x-3">
                            <a href="@Url.Action("Dashboard", "Home")" class="bg-white border border-neutral-300 text-neutral-700 hover:bg-neutral-50 px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium">
                                <i class="fas fa-times mr-2"></i>Annulla
                            </a>
                            <button type="submit" class="bg-ardec-primary hover:bg-ardec-primary-dark text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center font-medium shadow-sm" id="save-button">
                                <i class="fas fa-save mr-2"></i>Salva Modifiche
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1 space-y-6">
        <!-- Security & Privacy -->
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h6 class="font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-shield-alt mr-2 text-ardec-primary"></i>
                    Sicurezza & Privacy
                </h6>
            </div>
            <div class="px-6 py-6">
                <div class="space-y-4">
                    <!-- Secure Authentication -->
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <i class="fas fa-lock text-green-600 mr-2"></i>
                            <span class="font-medium text-neutral-700">Autenticazione Sicura</span>
                        </div>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700">Attivo</span>
                    </div>
                    
                    <!-- 2FA Status -->
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <i class="fas fa-mobile-alt text-neutral-400 mr-2"></i>
                            <span class="font-medium text-neutral-700">Autenticazione 2FA</span>
                        </div>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium @(Model.TwoFactorEnabled ? "bg-green-50 text-green-700" : "bg-neutral-100 text-neutral-600")">
                            @(Model.TwoFactorEnabled ? "Attivo" : "Non Attivo")
                        </span>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-neutral-200">
                    <div class="text-xs text-neutral-500 flex items-start">
                        <i class="fas fa-info-circle mr-1 mt-0.5 flex-shrink-0"></i>
                        <span>L'account è protetto da autenticazione basata su cookie con timeout di sessione automatico.</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-neutral-200">
            <div class="px-6 py-4 border-b border-neutral-200">
                <h6 class="font-semibold text-neutral-800 flex items-center">
                    <i class="fas fa-bolt mr-2 text-ardec-primary"></i>
                    Azioni Rapide
                </h6>
            </div>
            <div class="px-6 py-6">
                <div class="space-y-3">
                    <a href="@Url.Action("Dashboard", "Home")" class="w-full bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center justify-center font-medium">
                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                    </a>
                    <a href="@Url.Action("Profile", "Account")" class="w-full bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center justify-center font-medium">
                        <i class="fas fa-user mr-2"></i>Visualizza Profilo
                    </a>
                    <a href="/api-docs" target="_blank" class="w-full bg-white border border-ardec-primary text-ardec-primary hover:bg-ardec-primary hover:text-white px-4 py-2 rounded-lg transition-all duration-200 inline-flex items-center justify-center font-medium">
                        <i class="fas fa-code mr-2"></i>API Docs
                    </a>
                </div>
                
                <div class="mt-4 pt-4 border-t border-neutral-200">
                    <form asp-action="Logout" method="post">
                        <button type="submit" class="w-full px-4 py-2 border border-red-600 text-red-600 rounded-lg hover:bg-red-600 hover:text-white transition-colors" onclick="return confirm('Sei sicuro di voler uscire?')">
                            <i class="fas fa-sign-out-alt mr-2"></i>Esci dal Sistema
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle password visibility
            document.querySelectorAll('.toggle-password').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const input = document.getElementById(targetId);
                    const icon = this.querySelector('i');
                    
                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        input.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            });

            // Password strength indicator
            const newPasswordInput = document.getElementById('newPasswordInput');
            const strengthContainer = document.getElementById('password-strength');
            
            if (newPasswordInput) {
                newPasswordInput.addEventListener('input', function() {
                    const password = this.value;
                    const strength = calculatePasswordStrength(password);
                    
                    if (password.length > 0) {
                        strengthContainer.innerHTML = `
                            <div class="text-sm text-${strength.colorClass} flex items-center mt-1">
                                <i class="fas fa-${strength.icon} mr-1"></i>
                                Sicurezza: ${strength.text}
                            </div>
                        `;
                    } else {
                        strengthContainer.innerHTML = '';
                    }
                });
            }

            // Form validation feedback
            document.querySelectorAll('input').forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() !== '') {
                        this.classList.add('border-green-500');
                        this.classList.remove('border-red-500');
                    }
                });
            });

            // Enhanced loading state on form submit
            document.querySelector('form').addEventListener('submit', function(e) {
                const submitBtn = document.getElementById('save-button');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Salvataggio...';
            });
        });

        function calculatePasswordStrength(password) {
            if (password.length < 6) {
                return { colorClass: 'red-600', icon: 'times-circle', text: 'Troppo Corta' };
            }
            
            let score = 0;
            
            // Length check
            if (password.length >= 8) score += 1;
            if (password.length >= 12) score += 1;
            
            // Character variety
            if (/[a-z]/.test(password)) score += 1;
            if (/[A-Z]/.test(password)) score += 1;
            if (/[0-9]/.test(password)) score += 1;
            if (/[^A-Za-z0-9]/.test(password)) score += 1;
            
            if (score < 3) {
                return { colorClass: 'amber-600', icon: 'exclamation-triangle', text: 'Debole' };
            } else if (score < 5) {
                return { colorClass: 'blue-600', icon: 'check-circle', text: 'Media' };
            } else {
                return { colorClass: 'green-600', icon: 'shield-alt', text: 'Forte' };
            }
        }
    </script>
}
