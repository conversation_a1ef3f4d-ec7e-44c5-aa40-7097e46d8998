@model Ardec.Services.Web.Features.Account.LoginViewModel
@{
    ViewData["Title"] = "Accesso Sistema";
    Layout = null; // Login page ha layout custom
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ARDEC Services</title>
    
    <!-- Font Awesome 6 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="~/css/tailwind.min.css?v=@DateTime.Now.ToString("yyyyMMddHHmmss")" />
</head>
<body class="min-h-screen bg-gradient-to-br from-ardec-50 to-ardec-100 flex items-center justify-center py-8 px-4">
    <div class="w-full max-w-md">
        <div class="bg-white rounded-2xl shadow-ardec-xl overflow-hidden">
            <!-- Login Header -->
            <div class="bg-gradient-to-r from-ardec-primary to-ardec-primary-dark text-white text-center py-8 px-6">
                <div class="mb-4">
                    <img src="~/images/logo_ardec.png" alt="ARDEC" class="mx-auto h-16 w-auto">
                </div>
                <h3 class="text-2xl font-bold mb-2 text-white">MIL-STD-1388 Services</h3>
                <p class="text-white/80">Accesso al Sistema di Gestione Logistica</p>
            </div>
            
            <!-- Login Form -->
            <div class="p-8">
                @if (!ViewData.ModelState.IsValid)
                {
                    <div class="bg-danger/10 border border-danger/20 text-danger rounded-lg p-4 mb-6 flex items-start" role="alert">
                        <i class="fas fa-exclamation-triangle mr-3 mt-1"></i>
                        <div>
                            @Html.ValidationSummary(false, "", new { @class = "mb-0" })
                        </div>
                    </div>
                }
                
                <form asp-action="Login" asp-controller="Account" method="post" autocomplete="off" class="space-y-6">
                    <input type="hidden" name="returnUrl" value="@ViewData["ReturnUrl"]" />
                    
                    <div>
                        <label asp-for="Email" class="block text-sm font-semibold text-neutral-700 mb-2">
                            <i class="fas fa-envelope mr-2 text-ardec-primary"></i>Email
                        </label>
                        <input asp-for="Email" type="email" 
                               class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-ardec-primary focus:border-ardec-primary transition-colors" 
                               placeholder="<EMAIL>" required autocomplete="email">
                        <span asp-validation-for="Email" class="text-danger text-sm mt-1 block"></span>
                    </div>
                    
                    <div>
                        <label asp-for="Password" class="block text-sm font-semibold text-neutral-700 mb-2">
                            <i class="fas fa-lock mr-2 text-ardec-primary"></i>Password
                        </label>
                        <div class="relative">
                            <input asp-for="Password" type="password" 
                                   class="w-full px-4 py-3 pr-12 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-ardec-primary focus:border-ardec-primary transition-colors" 
                                   placeholder="Inserisci la password" required autocomplete="current-password" id="passwordInput">
                            <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600 transition-colors" id="togglePassword">
                                <i class="fas fa-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                        <span asp-validation-for="Password" class="text-danger text-sm mt-1 block"></span>
                    </div>
                    
                    <div class="flex items-center">
                        <input asp-for="RememberMe" type="checkbox" class="h-4 w-4 text-ardec-primary focus:ring-ardec-primary border-neutral-300 rounded" id="rememberMe">
                        <label class="ml-3 text-sm text-neutral-700" for="rememberMe">
                            Ricordami per 30 giorni
                        </label>
                    </div>
                    
                    <button type="submit" class="w-full bg-ardec-primary text-white py-3 px-6 rounded-lg font-semibold hover:bg-ardec-primary-dark transition-colors shadow-ardec">
                        <i class="fas fa-sign-in-alt mr-2"></i>Accedi al Sistema
                    </button>
                </form>
                
                <!-- Demo Credentials Info -->
                <div class="mt-6 pt-6 border-t border-neutral-200">
                    <p class="text-sm text-neutral-600">
                        <strong>Credenziali di test:</strong><br>
                        <code class="bg-neutral-100 px-2 py-1 rounded text-sm"><EMAIL></code> / <code class="bg-neutral-100 px-2 py-1 rounded text-sm">Admin123!</code>
                    </p>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="bg-neutral-50 text-center py-6 px-6 border-t border-neutral-200">
                <p class="text-sm text-neutral-600 mb-2">
                    <i class="fas fa-shield-alt mr-2"></i>
                    Sistema protetto con crittografia avanzata
                </p>
                <p class="text-sm text-neutral-500">
                    <a href="https://www.ardec-spa.it" target="_blank" class="text-neutral-500 hover:text-ardec-primary transition-colors">
                        <i class="fas fa-external-link-alt mr-1"></i>www.ardec-spa.it
                    </a>
                </p>
            </div>
        </div>
        
        <!-- Return to Homepage -->
        <div class="text-center mt-6">
            <a href="@Url.Action("Index", "Home")" class="text-ardec-primary hover:text-ardec-primary-dark transition-colors font-medium">
                <i class="fas fa-arrow-left mr-2"></i>Torna alla Homepage
            </a>
        </div>
    </div>

    <!-- jQuery 3.7 -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <script>
    $(document).ready(function() {
        // Toggle password visibility
        $('#togglePassword').click(function() {
            const passwordInput = $('#passwordInput');
            const toggleIcon = $('#toggleIcon');
            
            if (passwordInput.attr('type') === 'password') {
                passwordInput.attr('type', 'text');
                toggleIcon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                passwordInput.attr('type', 'password');
                toggleIcon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });
        
        // Focus first input
        $('#Email').focus();
        
        // Enhanced loading state
        $('form').on('submit', function(e) {
            const submitBtn = $(this).find('button[type="submit"]');
            submitBtn.prop('disabled', true)
                     .html('<i class="fas fa-spinner fa-spin mr-2"></i>Accesso in corso...');
        });
        
        // Auto-fill demo credentials (development only)
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            $(document).on('keydown', function(e) {
                // Ctrl+D fills demo credentials
                if (e.ctrlKey && e.key === 'd') {
                    e.preventDefault();
                    $('#Email').val('<EMAIL>');
                    $('#Password').val('Admin123!');
                    
                    // Simple toast notification
                    showToast('Demo credentials filled!', 'success');
                }
            });
        }
        
        // Simple toast notification function (Tailwind)
        function showToast(message, type = 'info') {
            const toastId = 'toast-' + Date.now();
            const bgColor = type === 'success' ? 'bg-success' : 'bg-ardec-primary';
            const toastHtml = `
                <div id="${toastId}" class="${bgColor} text-white px-4 py-3 rounded-lg shadow-lg mb-2 flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-${type === 'success' ? 'check' : 'info'} mr-2"></i>${message}
                    </div>
                    <button type="button" class="ml-4 text-white hover:text-neutral-200" onclick="$('#${toastId}').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // Create toast container if not exists
            if ($('#toast-container').length === 0) {
                $('body').append('<div id="toast-container" class="fixed bottom-4 right-4 z-50 space-y-2"></div>');
            }
            
            $('#toast-container').append(toastHtml);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                $('#' + toastId).fadeOut(() => $('#' + toastId).remove());
            }, 3000);
        }
    });
    </script>
</body>
</html>