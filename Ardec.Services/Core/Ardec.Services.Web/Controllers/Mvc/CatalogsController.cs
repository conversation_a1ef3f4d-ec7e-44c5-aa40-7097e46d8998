using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Ardec.Services.Web.Services.Catalogs;
using Ardec.Services.Web.Data.Models;

namespace Ardec.Services.Web.Controllers.Mvc;

/// <summary>
/// MVC Controller for Catalogs management with desktop-like experience
/// </summary>
[Authorize]
[Route("[controller]")]
public class CatalogsController : Controller
{
    private readonly ICatalogService _catalogService;
    private readonly ILogger<CatalogsController> _logger;

    public CatalogsController(ICatalogService catalogService, ILogger<CatalogsController> logger)
    {
        _catalogService = catalogService;
        _logger = logger;
    }

    /// <summary>
    /// Catalogs Index - Desktop-like master-detail view
    /// </summary>
    [HttpGet("")]
    public async Task<IActionResult> Index()
    {
        ViewData["Title"] = "Gestione Cataloghi - MIL-STD-1388 Services";
        
        try
        {
            var catalogs = await _catalogService.GetAllAsync();
            
            var viewModel = new CatalogsIndexViewModel
            {
                Catalogs = catalogs.Select(c => new CatalogSummaryViewModel
                {
                    TER = c.TER,
                    TitoloBreve = c.TitoloBreve,
                    Revisione = c.Revi,
                    Versione = c.Base,
                    // Summary stats (da implementare)
                    TotTavole = 0, // TODO: get from service
                    TotParti = 0   // TODO: get from service
                }).ToList(),
                TotalCount = catalogs.Count()
            };

            // Set first catalog as selected if available
            if (viewModel.Catalogs.Any())
            {
                viewModel.SelectedCatalogTER = viewModel.Catalogs.First().TER;
            }

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading catalogs index");
            TempData["ErrorMessage"] = "Errore nel caricamento dei cataloghi.";
            return View(new CatalogsIndexViewModel());
        }
    }

    /// <summary>
    /// Get catalog detail for master-detail view (AJAX)
    /// </summary>
    [HttpGet("api/detail/{ter}")]
    public async Task<IActionResult> GetCatalogDetail(string ter)
    {
        try
        {
            var catalog = await _catalogService.GetByIdAsync(ter);
            if (catalog == null)
            {
                return NotFound(new { error = "Catalogo non trovato" });
            }

            var detailModel = new CatalogDetailViewModel
            {
                TER = catalog.TER,
                TitoloBreve = catalog.TitoloBreve,
                TitoloCompleto = catalog.Titolo,
                TitoloSecondario = catalog.Titolo2,
                Revisione = catalog.Revi,
                Versione = catalog.Base,
                Rapporto = catalog.Reparto,
                PubNUC = catalog.PubNUC,
                Lingua = catalog.CEDLanguage,
                NoteCatalogo = catalog.CEDNote,
                Applique = catalog.Applique,
                
                // Flags (da TB_Cataloghi fields)
                ToNumber = catalog.CEDTONUMBER.HasValue,
                S1000D = catalog.S1000D ?? false,
                USA = catalog.IsUSA ?? false,
                MT = catalog.IsMT ?? false,
                ExtraNato = catalog.IsExNato ?? false,
                
                // Paginazione fields
                BasePage = catalog.Base,
                ReviPage = catalog.Revi,
                
                // Date fields
                Stampato = catalog.NStampato,
                
                // Computed fields  
                IsModified = false
            };

            return Json(detailModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading catalog detail for TER: {TER}", ter);
            return StatusCode(500, new { error = "Errore nel caricamento del dettaglio catalogo." });
        }
    }

    /// <summary>
    /// Save catalog changes (AJAX)
    /// </summary>
    [HttpPost("api/save")]
    public async Task<IActionResult> SaveCatalog([FromBody] CatalogDetailViewModel model)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                return BadRequest(new { error = "Dati non validi", details = errors });
            }

            var catalog = await _catalogService.GetByIdAsync(model.TER);
            if (catalog == null)
            {
                return NotFound(new { error = "Catalogo non trovato" });
            }

            // Update fields from model
            catalog.TitoloBreve = model.TitoloBreve;
            catalog.Titolo = model.TitoloCompleto;
            catalog.Titolo2 = model.TitoloSecondario;
            catalog.Revi = model.Revisione;
            catalog.Base = model.Versione;
            catalog.Reparto = model.Rapporto;
            catalog.PubNUC = model.PubNUC;
            catalog.CEDNote = model.NoteCatalogo;
            catalog.Applique = model.Applique;
            
            // Update flags
            catalog.CEDTONUMBER = model.ToNumber ? 1.0 : null;
            catalog.S1000D = model.S1000D;
            catalog.IsUSA = model.USA;
            catalog.IsMT = model.MT;
            catalog.IsExNato = model.ExtraNato;
            
            // Update pagination
            catalog.Base = model.BasePage;
            catalog.Revi = model.ReviPage;
            
            // Update fields
            catalog.NStampato = model.Stampato;

            await _catalogService.UpdateAsync(catalog);

            return Json(new { 
                success = true, 
                message = "Catalogo salvato con successo",
                timestamp = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving catalog: {TER}", model.TER);
            return StatusCode(500, new { error = "Errore nel salvataggio del catalogo" });
        }
    }

    /// <summary>
    /// Search/Filter catalogs (AJAX)
    /// </summary>
    [HttpGet("api/search")]
    public async Task<IActionResult> SearchCatalogs(
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? filterTER = null,
        [FromQuery] string? filterTitolo = null,
        [FromQuery] string? filterRevisione = null)
    {
        try
        {
            var catalogs = await _catalogService.SearchAsync(searchTerm ?? "");

            // Apply additional filters if needed
            // (Stato field doesn't exist in real model)

            var results = catalogs.Select(c => new CatalogSummaryViewModel
            {
                TER = c.TER,
                TitoloBreve = c.TitoloBreve,
                Revisione = c.Revi,
                Versione = c.Base,
                TotTavole = 0, // TODO: implement counts
                TotParti = 0
            }).ToList();

            return Json(new { 
                success = true, 
                catalogs = results,
                count = results.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching catalogs with term: {SearchTerm}", searchTerm);
            return StatusCode(500, new { error = "Errore nella ricerca dei cataloghi" });
        }
    }

    /// <summary>
    /// Show catalog details
    /// </summary>
    [HttpGet("Details/{id}")]
    public async Task<IActionResult> Details(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        try
        {
            var catalog = await _catalogService.GetByIdAsync(id);
            if (catalog == null)
            {
                return NotFound();
            }

            ViewData["Title"] = $"Dettagli Catalogo - {catalog.TER}";
            return View(catalog);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading catalog details for TER: {TER}", id);
            TempData["ErrorMessage"] = "Errore nel caricamento dei dettagli del catalogo.";
            return RedirectToAction("Index");
        }
    }

    /// <summary>
    /// Show create catalog form
    /// </summary>
    [HttpGet("Create")]
    public IActionResult Create()
    {
        ViewData["Title"] = "Nuovo Catalogo";
        return View(new TB_Cataloghi());
    }

    /// <summary>
    /// Handle create catalog form submission
    /// </summary>
    [HttpPost("Create")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create([Bind("TER,TitoloBreve,Titolo,Titolo2,PubNUC,Reparto,Base,Revi,NStampato,CEDNote,Applique,InfoName,S1000D,IsUSA,IsMT,IsExNato")] TB_Cataloghi catalog)
    {
        try
        {
            if (ModelState.IsValid)
            {
                // Check if TER already exists
                if (await _catalogService.ExistsAsync(catalog.TER))
                {
                    ModelState.AddModelError("TER", "Un catalogo con questo TER esiste già.");
                    ViewData["Title"] = "Nuovo Catalogo";
                    return View(catalog);
                }

                await _catalogService.CreateAsync(catalog);
                TempData["SuccessMessage"] = $"Catalogo '{catalog.TER}' creato con successo.";
                return RedirectToAction("Details", new { id = catalog.TER });
            }

            ViewData["Title"] = "Nuovo Catalogo";
            return View(catalog);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating catalog: {TER}", catalog.TER);
            TempData["ErrorMessage"] = "Errore nella creazione del catalogo.";
            ViewData["Title"] = "Nuovo Catalogo";
            return View(catalog);
        }
    }

    /// <summary>
    /// Show edit catalog form
    /// </summary>
    [HttpGet("Edit/{id}")]
    public async Task<IActionResult> Edit(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        try
        {
            var catalog = await _catalogService.GetByIdAsync(id);
            if (catalog == null)
            {
                return NotFound();
            }

            ViewData["Title"] = $"Modifica Catalogo - {catalog.TER}";
            return View(catalog);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading catalog for edit: {TER}", id);
            TempData["ErrorMessage"] = "Errore nel caricamento del catalogo.";
            return RedirectToAction("Index");
        }
    }

    /// <summary>
    /// Handle edit catalog form submission
    /// </summary>
    [HttpPost("Edit/{id}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(string id, [Bind("TER,TitoloBreve,Titolo,Titolo2,PubNUC,Reparto,Base,Revi,NStampato,CEDNote,Applique,InfoName,S1000D,IsUSA,IsMT,IsExNato")] TB_Cataloghi catalog)
    {
        if (id != catalog.TER)
        {
            return NotFound();
        }

        try
        {
            if (ModelState.IsValid)
            {
                await _catalogService.UpdateAsync(catalog);
                TempData["SuccessMessage"] = $"Catalogo '{catalog.TER}' aggiornato con successo.";
                return RedirectToAction("Details", new { id = catalog.TER });
            }

            ViewData["Title"] = $"Modifica Catalogo - {catalog.TER}";
            return View(catalog);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating catalog: {TER}", catalog.TER);
            TempData["ErrorMessage"] = "Errore nell'aggiornamento del catalogo.";
            ViewData["Title"] = $"Modifica Catalogo - {catalog.TER}";
            return View(catalog);
        }
    }

    /// <summary>
    /// Show delete catalog confirmation
    /// </summary>
    [HttpGet("Delete/{id}")]
    public async Task<IActionResult> Delete(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        try
        {
            var catalog = await _catalogService.GetByIdAsync(id);
            if (catalog == null)
            {
                return NotFound();
            }

            ViewData["Title"] = $"Elimina Catalogo - {catalog.TER}";
            return View(catalog);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading catalog for delete: {TER}", id);
            TempData["ErrorMessage"] = "Errore nel caricamento del catalogo.";
            return RedirectToAction("Index");
        }
    }

    /// <summary>
    /// Handle delete catalog confirmation
    /// </summary>
    [HttpPost("Delete/{id}"), ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(string id)
    {
        try
        {
            var result = await _catalogService.DeleteAsync(id);
            if (result)
            {
                TempData["SuccessMessage"] = $"Catalogo '{id}' eliminato con successo.";
            }
            else
            {
                TempData["ErrorMessage"] = "Catalogo non trovato.";
            }

            return RedirectToAction("Index");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting catalog: {TER}", id);
            TempData["ErrorMessage"] = "Errore nell'eliminazione del catalogo.";
            return RedirectToAction("Index");
        }
    }

    /// <summary>
    /// Navigate to Tables management for specific catalog
    /// </summary>
    [HttpGet("Tables/{ter}")]
    public async Task<IActionResult> Tables(string ter)
    {
        ViewData["Title"] = $"Gestione Tavole - Catalogo {ter}";
        
        try
        {
            var catalog = await _catalogService.GetByIdAsync(ter);
            if (catalog == null)
            {
                TempData["ErrorMessage"] = $"Catalogo {ter} non trovato.";
                return RedirectToAction("Index");
            }

            ViewBag.CatalogTER = ter;
            ViewBag.CatalogTitle = catalog.TitoloBreve;
            
            return View("Tables");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading tables view for catalog: {TER}", ter);
            TempData["ErrorMessage"] = "Errore nel caricamento della gestione tavole.";
            return RedirectToAction("Index");
        }
    }

    /// <summary>
    /// Navigate to Parts management for specific table
    /// </summary>
    [HttpGet("Parts/{ter}/{tavola}")]
    public async Task<IActionResult> Parts(string ter, string tavola)
    {
        ViewData["Title"] = $"Gestione Parti - Tavola {tavola}";
        
        try
        {
            var catalog = await _catalogService.GetByIdAsync(ter);
            if (catalog == null)
            {
                TempData["ErrorMessage"] = $"Catalogo {ter} non trovato.";
                return RedirectToAction("Index");
            }

            ViewBag.CatalogTER = ter;
            ViewBag.CatalogTitle = catalog.TitoloBreve;
            ViewBag.TableName = tavola;
            
            return View("Parts");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading parts view for table: {Tavola} in catalog: {TER}", tavola, ter);
            TempData["ErrorMessage"] = "Errore nel caricamento della gestione parti.";
            return RedirectToAction("Tables", new { ter });
        }
    }
}

/// <summary>
/// ViewModels for Catalogs MVC
/// </summary>
public class CatalogsIndexViewModel
{
    public List<CatalogSummaryViewModel> Catalogs { get; set; } = new();
    public int TotalCount { get; set; }
    public string? SelectedCatalogTER { get; set; }
    public CatalogSearchFiltersViewModel Filters { get; set; } = new();
}

public class CatalogSummaryViewModel
{
    public string TER { get; set; } = string.Empty;
    public string TitoloBreve { get; set; } = string.Empty;
    public string? Revisione { get; set; }
    public string? Versione { get; set; }
    public int TotTavole { get; set; }
    public int TotParti { get; set; }
}

public class CatalogDetailViewModel
{
    public string TER { get; set; } = string.Empty;
    public string TitoloBreve { get; set; } = string.Empty;
    public string? TitoloCompleto { get; set; }
    public string? TitoloSecondario { get; set; }
    public string? Revisione { get; set; }
    public string? Versione { get; set; }
    public string? Rapporto { get; set; }
    public string? PubNUC { get; set; }
    public string? Lingua { get; set; }
    public string? NoteCatalogo { get; set; }
    public string? Applique { get; set; }
    
    // Flags
    public bool ToNumber { get; set; }
    public bool S1000D { get; set; }
    public bool USA { get; set; }
    public bool MT { get; set; }
    public bool ExtraNato { get; set; }
    
    // Pagination
    public string? BasePage { get; set; }
    public string? ReviPage { get; set; }
    
    // Dates
    public string? Stampato { get; set; }
    
    // State tracking
    public bool IsModified { get; set; }
}

public class CatalogSearchFiltersViewModel
{
    public string? SearchTerm { get; set; }
    public string? FilterTER { get; set; }
    public string? FilterTitolo { get; set; }
    public string? FilterRevisione { get; set; }
}