using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Ardec.Services.Web.Services.Tables;
using Ardec.Services.Web.Services.Compositions;
using Ardec.Services.Web.Services.Catalogs;
using Ardec.Services.Web.Services.Parts;
using Ardec.Services.Web.Services.CID;
using Ardec.Services.Web.Services.Componenti;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Models;
using Ardec.Services.Web.Models.Tables;
using Microsoft.EntityFrameworkCore;

namespace Ardec.Services.Web.Controllers.Mvc;

/// <summary>
/// MVC Controller for Tables management - Clean implementation
/// </summary>
[Authorize]
[Route("[controller]")]
public class TablesController : Controller
{
    private readonly ITableService _tableService;
    private readonly ICompositionService _compositionService;
    private readonly ICatalogService _catalogService;
    private readonly IPartService _partService;
    private readonly ICIDService _cidService;
    private readonly IComponentiService _componentiService;
    private readonly ILogger<TablesController> _logger;

    public TablesController(
        ITableService tableService, 
        ICompositionService compositionService,
        ICatalogService catalogService,
        IPartService partService,
        ICIDService cidService,
        IComponentiService componentiService,
        ILogger<TablesController> logger)
    {
        _tableService = tableService;
        _compositionService = compositionService;
        _catalogService = catalogService;
        _partService = partService;
        _cidService = cidService;
        _componentiService = componentiService;
        _logger = logger;
    }

    /// <summary>
    /// Tables Index - Clean grid with TER filter
    /// Shows all tables with real versioning data
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Index(string? ter = null)
    {
        try
        {
            // Decode URL-encoded TER parameter to handle special characters (+, %, ^, etc.)
            if (!string.IsNullOrEmpty(ter))
            {
                ter = Uri.UnescapeDataString(ter);
            }
            var tables = new List<TableRowData>();
            TB_Cataloghi? catalog = null;
            
            // Get all catalogs for filter dropdown
            var allCatalogs = await _catalogService.GetAllAsync();
            ViewBag.AllCatalogs = allCatalogs.OrderBy(c => c.TER).ToList();
            
            if (!string.IsNullOrEmpty(ter))
            {
                // Get tables for specific catalog
                catalog = await _catalogService.GetByTerAsync(ter);
                if (catalog != null)
                {
                    tables = await GetTablesByTerAsync(ter);
                }
            }
            else
            {
                // Get all tables
                tables = await GetAllTablesAsync();
            }
            
            var viewModel = new TablesIndexViewModel
            {
                FilterTER = ter,
                CatalogTitle = catalog?.TitoloBreve,
                Tables = tables,
                TotalCount = tables.Count,
                VersionsCount = tables.Sum(t => t.VersionCount)
            };
            
            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading Tables Index for TER: {TER}", ter);
            TempData["ErrorMessage"] = "Errore nel caricamento delle tavole. Riprovare più tardi.";
            return RedirectToAction("Index", "Home");
        }
    }

    /// <summary>
    /// Get all tables with versioning data - Using Framework pattern
    /// Base query from TB_DettagliTavole with joins to TB_Composizione and TB_Cataloghi
    /// </summary>
    private async Task<List<TableRowData>> GetAllTablesAsync()
    {
        // Use Dashboard service that implements the correct Framework pattern
        var dashboardResult = await _tableService.GetTablesDashboardAsync(page: 1, pageSize: 10000);
        
        return dashboardResult.Tables.Select(t => new TableRowData
        {
            Tavola = t.Tavola,
            CodiceTecnico = t.CodiceTecnico ?? "N/A",
            TER = t.CatalogTER,
            VersionStatusPairs = t.AvailableVersions.Select(v => new VersionStatusPair
            {
                Versione = v.Versione,
                Stato = v.Stato ?? "In lavorazione"
            }).OrderByDescending(v => v.Versione).ToList(),
            LastModified = t.LastUpdate ?? DateTime.MinValue
        }).OrderBy(t => t.Tavola).ToList();
    }

    /// <summary>
    /// Get tables for specific catalog with versioning data - Using Framework pattern
    /// </summary>
    private async Task<List<TableRowData>> GetTablesByTerAsync(string ter)
    {
        // Use Dashboard service that implements the correct Framework pattern
        var dashboardResult = await _tableService.GetTablesDashboardAsync(ter: ter, page: 1, pageSize: 10000);
        
        return dashboardResult.Tables.Select(t => new TableRowData
        {
            Tavola = t.Tavola,
            CodiceTecnico = t.CodiceTecnico ?? "N/A",
            TER = t.CatalogTER,
            VersionStatusPairs = t.AvailableVersions.Select(v => new VersionStatusPair
            {
                Versione = v.Versione,
                Stato = v.Stato ?? "In lavorazione"
            }).OrderByDescending(v => v.Versione).ToList(),
            LastModified = t.LastUpdate ?? DateTime.MinValue
        }).OrderBy(t => t.Tavola).ToList();
    }


    /// <summary>
    /// Tables Details - mostra info tavola e parti (aggregando TB_Parti + TB_Componenti)
    /// Supporta filtro per TER (catalogo), CID e Versione via query string
    /// </summary>
    [HttpGet("{tavola}")]
    public async Task<IActionResult> Details(string tavola, string? ter = null, string? cid = null, string? versione = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(tavola))
            {
                return NotFound();
            }

            if (!string.IsNullOrEmpty(ter))
            {
                ter = Uri.UnescapeDataString(ter);
            }

            // Recupera tutte le versioni disponibili per la tavola
            var allVersions = await _tableService.GetTableDetailsByTavolaAsync(tavola);
            if (allVersions == null || !allVersions.Any())
            {
                TempData["ErrorMessage"] = $"Nessun dettaglio trovato per la tavola {tavola}.";
                return RedirectToAction("Index");
            }

            // Se non specificata, usa l'ultima versione
            var selectedVersion = !string.IsNullOrWhiteSpace(versione)
                ? versione
                : allVersions.OrderByDescending(v => v.Versione).First().Versione;

            var details = allVersions.FirstOrDefault(v => v.Versione == selectedVersion)
                          ?? allVersions.OrderByDescending(v => v.Versione).First();

            // Composizione per TER (se fornito)
            TB_Composizione? composition = null;
            if (!string.IsNullOrEmpty(ter))
            {
                composition = await _compositionService.GetCompositionAsync(ter, tavola);
            }

            // Costruzione ViewModel base
            var model = new TableDetailsViewModel
            {
                TER = ter ?? composition?.TER ?? string.Empty,
                CID = cid ?? string.Empty,
                Tavola = tavola,
                CodiceTecnico = details.CodiceTecnico,
                Versione = details.Versione,
                Stato = details.Stato ?? "00",
                Data = details.Data,
                Descrizione1IT = details.Descrizione1IT,
                Descrizione2IT = details.Descrizione2IT,
                Descrizione3IT = details.Descrizione3IT,
                nTavola = composition?.nTavola,
                CEDNPagina = composition?.CEDNPagina,
                CEDIGNPagina = composition?.CEDIGNPagina,
                DMC = details.DMC,
                SBC = details.SBC,
                SNS = details.SNS,
                // Campi non presenti in TB_DettagliTavole lasciati vuoti per ora
                TECHNAME = null,
                INFONAME = null,
                ICN_TITLE = null,
                ModificaTavola = (details.ModificaTavola ?? string.Empty).Trim() == "-1" ||
                                 string.Equals(details.ModificaTavola, "true", StringComparison.OrdinalIgnoreCase),
                DataModificaTavola = TryParseDate(details.DataModificaTavola),
                ModificaTesto = (details.ModificaTesto ?? string.Empty).Trim() == "-1" ||
                                string.Equals(details.ModificaTesto, "true", StringComparison.OrdinalIgnoreCase),
                DataModificaTesto = TryParseDate(details.DataModificaTesto),
                Logo = details.Logo,
                Figura = details.Figura,
                Note = details.Note
            };

            // CIDs disponibili per TER (selector)
            if (!string.IsNullOrEmpty(model.TER))
            {
                var cids = await _cidService.GetCidsByTerAsync(model.TER);
                model.AvailableCIDs = cids.Select(c => new CidSelectorOption
                {
                    CID = c.CID,
                    DisplayTitle = !string.IsNullOrWhiteSpace(c.Titolo) ? c.Titolo! : c.CID,
                    Date = c.Data,
                    TableCount = 0,
                    OverallStatus = TableVersionStatus.Empty,
                    IsSelected = !string.IsNullOrEmpty(model.CID) && string.Equals(model.CID, c.CID, StringComparison.OrdinalIgnoreCase)
                }).ToList();

                // Se nessun CID selezionato, non filtrare le parti ma mantieni l'elenco selezionabile
            }

            // Parti della tavola (versione selezionata), filtrate per CID se fornito
            var parts = await _partService.GetByTavolaAndVersionAsync(tavola, model.Versione!);
            if (!string.IsNullOrWhiteSpace(model.CID))
            {
                parts = parts.Where(p => string.Equals(p.CID ?? string.Empty, model.CID, StringComparison.OrdinalIgnoreCase));
            }

            var partList = parts.ToList();
            model.Parts = new List<CIDPartModel>();

            // Nota: ComponentiService non espone bulk-get per PART; per ora singola chiamata per PART
            foreach (var p in partList)
            {
                var comp = await _componentiService.GetByPartAsync(p.PART);
                int? qty = null;
                if (int.TryParse(p.QTAV, out var qParsed)) qty = qParsed;

                model.Parts.Add(new CIDPartModel
                {
                    TER = model.TER,
                    CID = p.CID ?? string.Empty,
                    Tavola = p.Tavola,
                    PART = p.PART,
                    ITEM = p.ITEM,
                    Versione = p.Versione,
                    QTAV = qty,
                    NotaRVT = p.NotaRVT,
                    Assieme = p.Assieme,
                    DescrIT = comp?.DescrIT,
                    DescrEN = comp?.DescrEN,
                    IsUniqueToCID = false,
                    IsSharedAcrossCIDs = false
                });
            }

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Errore nel caricamento dettagli tavola {Tavola}", tavola);
            TempData["ErrorMessage"] = "Errore nel caricamento del dettaglio tavola.";
            return RedirectToAction("Index");
        }
    }

    private static DateTime? TryParseDate(string? s)
    {
        if (string.IsNullOrWhiteSpace(s)) return null;
        if (DateTime.TryParse(s, out var dt)) return dt;
        return null;
    }
}