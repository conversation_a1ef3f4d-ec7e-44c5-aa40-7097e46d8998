using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Ardec.Services.Web.Services.Catalogs;
using Ardec.Services.Web.Services.Tables;
using Ardec.Services.Web.Services.Parts;

namespace Ardec.Services.Web.Controllers.Mvc;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ICatalogService _catalogService;
    private readonly ITableService _tableService;
    private readonly IPartService _partService;

    public HomeController(ILogger<HomeController> logger, ICatalogService catalogService, ITableService tableService, IPartService partService)
    {
        _logger = logger;
        _catalogService = catalogService;
        _tableService = tableService;
        _partService = partService;
    }

    /// <summary>
    /// Dashboard principale - versione semplificata per test
    /// </summary>
    public IActionResult Index()
    {
        ViewBag.Title = "Dashboard Ardec Services";
        ViewBag.PageDescription = "Sistema di gestione cataloghi tecnici e parti";

        return View();
    }

    /// <summary>
    /// Pagina di test per il design system Tailwind CSS
    /// </summary>
    public IActionResult TailwindTest()
    {
        return View();
    }

    /// <summary>
    /// About page
    /// </summary>
    [AllowAnonymous]
    public IActionResult About()
    {
        ViewBag.Title = "Informazioni";
        return View();
    }

    /// <summary>
    /// Error page
    /// </summary>
    [AllowAnonymous]
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error(int? statusCode = null)
    {
        ViewBag.Title = "Errore";
        ViewBag.StatusCode = statusCode;
        
        return View();
    }

    /// <summary>
    /// Dashboard utente autenticato
    /// </summary>
    [Authorize]
    public async Task<IActionResult> Dashboard()
    {
        ViewBag.Title = "Dashboard - MIL-STD-1388 Services";
        
        try
        {
            // Get statistics from services
            var catalogsCount = await _catalogService.GetTotalCountAsync();
            var tablesCount = 0; // TODO: await _tableService.GetTotalCountAsync();
            var partsCount = 0; // TODO: await _partService.GetTotalCountAsync();
            
            // Get recent items (limited)
            var recentCatalogs = await _catalogService.GetRecentAsync(5) ?? new List<Data.Models.TB_Cataloghi>();
            var recentTables = new List<object>(); // TODO: await _tableService.GetRecentAsync(5);
            var recentParts = new List<object>(); // TODO: await _partService.GetRecentAsync(5);

            var dashboardModel = new
            {
                Statistics = new
                {
                    CatalogsCount = catalogsCount,
                    TablesCount = tablesCount,
                    PartsCount = partsCount,
                    LastUpdate = DateTime.Now
                },
                RecentItems = new
                {
                    Catalogs = recentCatalogs,
                    Tables = recentTables,
                    Parts = recentParts
                },
                UserInfo = new
                {
                    Name = User.Identity?.Name ?? "Utente",
                    Roles = User.Claims.Where(c => c.Type == "role").Select(c => c.Value).ToList()
                }
            };

            return View(dashboardModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard data");
            
            var errorModel = new
            {
                Statistics = new
                {
                    CatalogsCount = 0,
                    TablesCount = 0,
                    PartsCount = 0,
                    LastUpdate = DateTime.Now
                },
                Error = "Errore nel caricamento dei dati"
            };
            
            return View(errorModel);
        }
    }

    /// <summary>
    /// Privacy policy page
    /// </summary>
    [AllowAnonymous]
    public IActionResult Privacy()
    {
        ViewBag.Title = "Privacy Policy - MIL-STD-1388 Services";
        return View();
    }

    /// <summary>
    /// Cookie policy page
    /// </summary>
    [AllowAnonymous]
    public IActionResult CookiePolicy()
    {
        ViewBag.Title = "Cookie Policy - MIL-STD-1388 Services";
        return View();
    }

    /// <summary>
    /// Accessibility declaration page
    /// </summary>
    [AllowAnonymous]
    public IActionResult Accessibility()
    {
        ViewBag.Title = "Dichiarazione di Accessibilità - MIL-STD-1388 Services";
        return View();
    }

}
