using Microsoft.AspNetCore.Mvc;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Services.Rvt;

namespace Ardec.Services.Web.Controllers.Api;

[Route("api/[controller]")]
[ApiController]
public class RvtController : ControllerBase
{
    private readonly IRvtService _rvtService;
    private readonly ILogger<RvtController> _logger;

    public RvtController(IRvtService rvtService, ILogger<RvtController> logger)
    {
        _rvtService = rvtService;
        _logger = logger;
    }

    /// <summary>
    /// Get all RVT entries
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<TB_RVT>>> GetAll()
    {
        try
        {
            var rvts = await _rvtService.GetAllAsync();
            return Ok(rvts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all RVT");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get RVT by composite key (TER + RVT)
    /// </summary>
    [HttpGet("{ter}/{rvt}")]
    public async Task<ActionResult<TB_RVT>> GetByCompositeKey(string ter, string rvt)
    {
        try
        {
            var rvtEntity = await _rvtService.GetByCompositeKeyAsync(ter, rvt);
            if (rvtEntity == null)
                return NotFound($"RVT not found: {ter}/{rvt}");

            return Ok(rvtEntity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving RVT: {TER}/{RVT}", ter, rvt);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get all RVT entries for a specific TER
    /// </summary>
    [HttpGet("by-ter/{ter}")]
    public async Task<ActionResult<IEnumerable<TB_RVT>>> GetByTer(string ter)
    {
        try
        {
            var rvts = await _rvtService.GetByTerAsync(ter);
            return Ok(rvts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving RVT for TER: {TER}", ter);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Search RVT entries
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<TB_RVT>>> Search([FromQuery] string searchTerm)
    {
        try
        {
            var rvts = await _rvtService.SearchAsync(searchTerm);
            return Ok(rvts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching RVT with term: {SearchTerm}", searchTerm);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new RVT entry
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<TB_RVT>> Create([FromBody] TB_RVT rvt)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var createdRvt = await _rvtService.CreateAsync(rvt);
            return CreatedAtAction(
                nameof(GetByCompositeKey), 
                new { ter = createdRvt.TER, rvt = createdRvt.RVT }, 
                createdRvt);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating RVT: {TER}/{RVT}", rvt?.TER, rvt?.RVT);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update an existing RVT entry
    /// </summary>
    [HttpPut("{ter}/{rvt}")]
    public async Task<ActionResult<TB_RVT>> Update(string ter, string rvtCode, [FromBody] TB_RVT rvt)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            if (ter != rvt.TER || rvtCode != rvt.RVT)
                return BadRequest("Route parameters do not match entity properties");

            var updatedRvt = await _rvtService.UpdateAsync(rvt);
            return Ok(updatedRvt);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating RVT: {TER}/{RVT}", ter, rvtCode);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete an RVT entry
    /// </summary>
    [HttpDelete("{ter}/{rvt}")]
    public async Task<ActionResult> Delete(string ter, string rvt)
    {
        try
        {
            var deleted = await _rvtService.DeleteAsync(ter, rvt);
            if (!deleted)
                return NotFound($"RVT not found: {ter}/{rvt}");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting RVT: {TER}/{RVT}", ter, rvt);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get total count of RVT entries
    /// </summary>
    [HttpGet("count")]
    public async Task<ActionResult<int>> GetCount()
    {
        try
        {
            var count = await _rvtService.GetTotalCountAsync();
            return Ok(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting RVT count");
            return StatusCode(500, "Internal server error");
        }
    }
}
