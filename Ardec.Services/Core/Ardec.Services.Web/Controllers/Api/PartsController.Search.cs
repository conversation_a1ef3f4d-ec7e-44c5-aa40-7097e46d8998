using Ardec.Services.Web.Data.Models;
using Microsoft.AspNetCore.Mvc;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// PartsController partial class - Search Endpoints
/// Handles all part search operations with various criteria
/// </summary>
public partial class PartsController
{
    #region Search Endpoints

    /// <summary>
    /// General search across all part fields (All authenticated users)
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<TB_Parti>>> Search([FromQuery] string q, [FromQuery] int limit = 50)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(q))
                return BadRequest(new { error = "Search query 'q' is required" });

            var results = await _partService.SearchAsync(q);
            var limitedResults = results.Take(limit).ToList();
            
            _logger.LogInformation("Search for '{Query}' returned {Count} results (limited to {Limit}) for user {User}", 
                q, limitedResults.Count, limit, User.Identity?.Name);
            
            return Ok(new 
            { 
                parts = limitedResults, 
                totalCount = results.Count(),
                returnedCount = limitedResults.Count,
                hasMore = results.Count() > limit,
                query = q
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching parts with query: {Query} by user {User}", q, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Search parts by Part Number (All authenticated users)
    /// </summary>
    [HttpGet("search/part-number")]
    public async Task<ActionResult<IEnumerable<TB_Parti>>> SearchByPartNumber([FromQuery] string partNumber, [FromQuery] int limit = 50)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(partNumber))
                return BadRequest(new { error = "partNumber is required" });

            var results = await _partService.SearchByPartAsync(partNumber);
            var limitedResults = results.Take(limit).ToList();
            
            _logger.LogInformation("Part number search for '{PartNumber}' returned {Count} results for user {User}", 
                partNumber, limitedResults.Count, User.Identity?.Name);
            
            return Ok(limitedResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching by part number: {PartNumber}", partNumber);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Search parts by CSNREF (All authenticated users)
    /// </summary>
    [HttpGet("search/csnref")]
    public async Task<ActionResult<IEnumerable<TB_Parti>>> SearchByCsnref([FromQuery] string csnref, [FromQuery] int limit = 50)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(csnref))
                return BadRequest(new { error = "csnref is required" });

            var results = await _partService.SearchByCsnrefAsync(csnref);
            var limitedResults = results.Take(limit).ToList();
            
            _logger.LogInformation("CSNREF search for '{Csnref}' returned {Count} results for user {User}", 
                csnref, limitedResults.Count, User.Identity?.Name);
            
            return Ok(limitedResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching by CSNREF: {Csnref}", csnref);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Search parts by LCN (All authenticated users)
    /// </summary>
    [HttpGet("search/lcn")]
    public async Task<ActionResult<IEnumerable<TB_Parti>>> SearchByLcn([FromQuery] string lcn, [FromQuery] int limit = 50)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(lcn))
                return BadRequest(new { error = "lcn is required" });

            var results = await _partService.SearchByLcnAsync(lcn);
            var limitedResults = results.Take(limit).ToList();
            
            _logger.LogInformation("LCN search for '{Lcn}' returned {Count} results for user {User}", 
                lcn, limitedResults.Count, User.Identity?.Name);
            
            return Ok(limitedResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching by LCN: {Lcn}", lcn);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    // TODO: Advanced search endpoint - requires AdvancedSearchAsync implementation in PartService

    // TODO: Search by Assieme endpoint - requires SearchByAssiemeAsync implementation in PartService
    // TODO: Search by ILS endpoint - requires SearchByILSAsync implementation in PartService

    #endregion

    #region Helper Methods

    private static int GetNonEmptyCriteriaCount(PartSearchCriteria criteria)
    {
        int count = 0;
        if (!string.IsNullOrWhiteSpace(criteria.Tavola)) count++;
        if (!string.IsNullOrWhiteSpace(criteria.PartNumber)) count++;
        if (!string.IsNullOrWhiteSpace(criteria.Item)) count++;
        if (!string.IsNullOrWhiteSpace(criteria.Csnref)) count++;
        if (!string.IsNullOrWhiteSpace(criteria.Lcn)) count++;
        if (!string.IsNullOrWhiteSpace(criteria.Assieme)) count++;
        if (criteria.ILS.HasValue) count++;
        if (!string.IsNullOrWhiteSpace(criteria.Versione)) count++;
        return count;
    }

    #endregion
}

/// <summary>
/// Search criteria for advanced part search
/// </summary>
public class PartSearchCriteria
{
    public string? Tavola { get; set; }
    public string? PartNumber { get; set; }
    public string? Item { get; set; }
    public string? Csnref { get; set; }
    public string? Lcn { get; set; }
    public string? Assieme { get; set; }
    public bool? ILS { get; set; }
    public string? Versione { get; set; }
    public int? Limit { get; set; } = 50;
}
