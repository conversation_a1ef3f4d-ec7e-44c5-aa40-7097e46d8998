using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Ardec.Services.Web.Shared.Authorization;
using Ardec.Services.Web.Services.DescrizioniCustom;
using Ardec.Services.Web.DTOs.DescrizioniCustom;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// Controller per la gestione delle descrizioni personalizzate (TB_DescrizioniCustom)
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DescrizioniCustomController : ControllerBase
{
    private readonly IDescrizioniCustomService _descrizioniCustomService;
    private readonly ILogger<DescrizioniCustomController> _logger;

    public DescrizioniCustomController(
        IDescrizioniCustomService descrizioniCustomService,
        ILogger<DescrizioniCustomController> logger)
    {
        _descrizioniCustomService = descrizioniCustomService;
        _logger = logger;
    }

    [HttpGet]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<DescrizioniCustomPagedResult>> GetAll(
        [FromQuery] int skip = 0,
        [FromQuery] int take = 20,
        CancellationToken cancellationToken = default)
    {
        if (take > 100) take = 100;
        var result = await _descrizioniCustomService.GetAllAsync(skip, take, cancellationToken);
        return Ok(result);
    }

    [HttpGet("{part}/{ter}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<DescrizioniCustomResponse>> GetByKeys(
        string part, 
        string ter, 
        CancellationToken cancellationToken = default)
    {
        var result = await _descrizioniCustomService.GetByKeysAsync(part, ter, cancellationToken);
        return result == null ? NotFound($"Descrizione custom per PART '{part}' e TER '{ter}' non trovata") : Ok(result);
    }

    [HttpPost("search")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<DescrizioniCustomPagedResult>> Search(
        [FromBody] DescrizioniCustomSearchRequest request,
        CancellationToken cancellationToken = default)
    {
        if (request.Take > 100) request = request with { Take = 100 };
        var result = await _descrizioniCustomService.SearchAsync(request, cancellationToken);
        return Ok(result);
    }

    [HttpGet("by-part/{part}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<DescrizioniCustomResponse>>> GetByPart(
        string part, 
        CancellationToken cancellationToken = default)
    {
        var result = await _descrizioniCustomService.GetByPartAsync(part, cancellationToken);
        return Ok(result);
    }

    [HttpGet("by-ter/{ter}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<DescrizioniCustomResponse>>> GetByTer(
        string ter, 
        CancellationToken cancellationToken = default)
    {
        var result = await _descrizioniCustomService.GetByTerAsync(ter, cancellationToken);
        return Ok(result);
    }

    [HttpPost("part-descriptions")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<PartDescriptionsResponse>> GetPartDescriptions(
        [FromBody] PartDescriptionsRequest request,
        CancellationToken cancellationToken = default)
    {
        var result = await _descrizioniCustomService.GetPartDescriptionsAsync(request, cancellationToken);
        return result == null ? NotFound($"Nessuna descrizione trovata per PART '{request.PART}'") : Ok(result);
    }

    [HttpGet("{part}/{ter}/localized")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<string>> GetLocalizedDescription(
        string part,
        string ter,
        [FromQuery] string language = "IT",
        CancellationToken cancellationToken = default)
    {
        var result = await _descrizioniCustomService.GetLocalizedDescriptionAsync(part, ter, language, cancellationToken);
        return result == null ? NotFound() : Ok(result);
    }

    [HttpGet("search-content")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<DescrizioniCustomResponse>>> SearchByDescriptionContent(
        [FromQuery] string searchTerm,
        [FromQuery] string language = "IT",
        CancellationToken cancellationToken = default)
    {
        var result = await _descrizioniCustomService.SearchByDescriptionContentAsync(searchTerm, language, cancellationToken);
        return Ok(result);
    }

    [HttpPost]
    [Authorize(Policy = Policies.WriteAccess)]
    public async Task<ActionResult<DescrizioniCustomResponse>> Create(
        [FromBody] CreateDescrizioniCustomRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _descrizioniCustomService.CreateAsync(request, cancellationToken);
            return CreatedAtAction(
                nameof(GetByKeys),
                new { part = result.PART, ter = result.TER },
                result);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
    }

    [HttpPut("{part}/{ter}")]
    [Authorize(Policy = Policies.WriteAccess)]
    public async Task<ActionResult<DescrizioniCustomResponse>> Update(
        string part,
        string ter,
        [FromBody] UpdateDescrizioniCustomRequest request,
        CancellationToken cancellationToken = default)
    {
        var result = await _descrizioniCustomService.UpdateAsync(part, ter, request, cancellationToken);
        return result == null ? NotFound($"Descrizione custom per PART '{part}' e TER '{ter}' non trovata") : Ok(result);
    }

    [HttpDelete("{part}/{ter}")]
    [Authorize(Policy = Policies.DeleteAccess)]
    public async Task<ActionResult> Delete(
        string part,
        string ter,
        CancellationToken cancellationToken = default)
    {
        var success = await _descrizioniCustomService.DeleteAsync(part, ter, cancellationToken);
        return success ? NoContent() : NotFound($"Descrizione custom per PART '{part}' e TER '{ter}' non trovata");
    }

    [HttpHead("{part}/{ter}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult> Exists(
        string part,
        string ter,
        CancellationToken cancellationToken = default)
    {
        var exists = await _descrizioniCustomService.ExistsAsync(part, ter, cancellationToken);
        return exists ? Ok() : NotFound();
    }

    [HttpGet("stats")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<DescrizioniCustomStatsResponse>> GetStats(
        CancellationToken cancellationToken = default)
    {
        var result = await _descrizioniCustomService.GetStatsAsync(cancellationToken);
        return Ok(result);
    }
}
