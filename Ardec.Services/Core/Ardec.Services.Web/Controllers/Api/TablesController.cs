using Ardec.Services.Web.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Ardec.Services.Web.Services.Tables;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// Tables REST API Controller - Complete implementation with TB_Tavole and TB_DettagliTavole
/// Main class with TB_Tavole endpoints and infrastructure
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public partial class TablesController : ControllerBase
{
    private readonly ITableService _tableService;
    private readonly ILogger<TablesController> _logger;

    public TablesController(ITableService tableService, ILogger<TablesController> logger)
    {
        _tableService = tableService;
        _logger = logger;
    }

    #region TB_Tavole Endpoints

    /// <summary>
    /// Get all tables (All authenticated users)
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<TB_Tavole>>> GetAll([FromQuery] bool lazy = false)
    {
        try
        {
            var tables = lazy ? await _tableService.GetTablesLazyAsync() : await _tableService.GetAllTablesAsync();
            _logger.LogInformation("Retrieved {Count} tables (lazy: {Lazy}) for user {User}", 
                tables.Count(), lazy, User.Identity?.Name);
            return Ok(tables);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all tables");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get table by Tavola (All authenticated users)
    /// </summary>
    [HttpGet("{tavola}")]
    public async Task<ActionResult<TB_Tavole>> GetByTavola(string tavola)
    {
        try
        {
            var table = await _tableService.GetTableByTavolaAsync(tavola);
            if (table == null)
            {
                _logger.LogWarning("Table with Tavola '{Tavola}' not found", tavola);
                return NotFound(new { error = "Table not found", tavola });
            }

            _logger.LogInformation("Retrieved table with Tavola: {Tavola} for user {User}", tavola, User.Identity?.Name);
            return Ok(table);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving table with Tavola: {Tavola}", tavola);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get tables by Tavola name pattern (All authenticated users)
    /// </summary>
    [HttpGet("by-tavola/{tavola}")]
    public async Task<ActionResult<IEnumerable<TB_Tavole>>> GetTablesByTavola(string tavola)
    {
        try
        {
            var tables = await _tableService.GetTablesByTavolaAsync(tavola);
            _logger.LogInformation("Retrieved {Count} tables matching Tavola: {Tavola} for user {User}", 
                tables.Count(), tavola, User.Identity?.Name);
            return Ok(tables);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tables by Tavola: {Tavola}", tavola);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get tables by TER through TB_Composizione (All authenticated users)
    /// </summary>
    [HttpGet("by-ter/{ter}")]
    public async Task<ActionResult<IEnumerable<TB_Tavole>>> GetTablesByTer(string ter, [FromQuery] bool orderByTavola = true)
    {
        try
        {
            var tables = await _tableService.GetTablesByTerAsync(ter, orderByTavola);
            _logger.LogInformation("Retrieved {Count} tables for TER: {TER} for user {User}", 
                tables.Count(), ter, User.Identity?.Name);
            return Ok(tables);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tables by TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Create new table (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_Tavole>> Create([FromBody] TB_Tavole table)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var createdTable = await _tableService.CreateTableAsync(table);
            _logger.LogInformation("Created table with Tavola: {Tavola} by user {User}", createdTable.Tavola, User.Identity?.Name);
            
            return CreatedAtAction(nameof(GetByTavola), new { tavola = createdTable.Tavola }, createdTable);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating table by user {User}", User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Update table (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPut("{tavola}")]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_Tavole>> Update(string tavola, [FromBody] TB_Tavole table)
    {
        try
        {
            if (tavola != table.Tavola)
                return BadRequest(new { error = "Tavola mismatch between route and body" });

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var updatedTable = await _tableService.UpdateTableAsync(table);
            _logger.LogInformation("Updated table with Tavola: {Tavola} by user {User}", tavola, User.Identity?.Name);
            
            return Ok(updatedTable);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating table with Tavola: {Tavola} by user {User}", tavola, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Delete table (Admin only) - Deletes TB_Tavole and all related TB_DettagliTavole
    /// </summary>
    [HttpDelete("{tavola}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> Delete(string tavola)
    {
        try
        {
            var deleted = await _tableService.DeleteTableAsync(tavola);
            if (!deleted)
            {
                return NotFound(new { error = "Table not found", tavola });
            }

            _logger.LogInformation("Deleted table with Tavola: {Tavola} by user {User}", tavola, User.Identity?.Name);
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting table with Tavola: {Tavola} by user {User}", tavola, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Search tables (All authenticated users)
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<TB_Tavole>>> Search([FromQuery] string q)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(q))
                return BadRequest(new { error = "Search query 'q' is required" });

            var results = await _tableService.SearchTablesAsync(q);
            _logger.LogInformation("Search for '{Query}' returned {Count} results for user {User}", 
                q, results.Count(), User.Identity?.Name);
            
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching tables with query: {Query} by user {User}", q, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Check if table exists (All authenticated users)
    /// </summary>
    [HttpHead("{tavola}")]
    public async Task<ActionResult> Exists(string tavola)
    {
        try
        {
            var exists = await _tableService.TableExistsAsync(tavola);
            if (exists)
            {
                return Ok();
            }
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if table exists with Tavola: {Tavola}", tavola);
            return StatusCode(500);
        }
    }

    /// <summary>
    /// Get tables count (All authenticated users)
    /// </summary>
    [HttpGet("count")]
    public async Task<ActionResult<int>> GetCount()
    {
        try
        {
            var count = await _tableService.GetTablesCountAsync();
            _logger.LogInformation("Retrieved tables count: {Count} for user {User}", count, User.Identity?.Name);
            return Ok(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tables count");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get tables count by TER (All authenticated users)
    /// </summary>
    [HttpGet("count/{ter}")]
    public async Task<ActionResult<int>> GetCountByTer(string ter)
    {
        try
        {
            var count = await _tableService.GetTablesCountByTerAsync(ter);
            _logger.LogInformation("Retrieved tables count for TER {TER}: {Count} for user {User}", ter, count, User.Identity?.Name);
            return Ok(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tables count for TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    #endregion
}
