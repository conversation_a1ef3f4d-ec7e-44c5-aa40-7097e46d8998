using Microsoft.AspNetCore.Mvc;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// PartsController partial class - Statistics and Business Logic Endpoints
/// Handles statistics, reports and advanced business logic operations
/// </summary>
public partial class PartsController
{
    #region Statistics Endpoints

    /// <summary>
    /// Get parts statistics (All authenticated users)
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<object>> GetStatistics()
    {
        try
        {
            var parts = await _partService.GetAllAsync();
            var stats = new
            {
                totalParts = parts.Count(),
                partsWithCsnref = parts.Count(p => !string.IsNullOrEmpty(p.CSNREF)),
                partsWithLcn = parts.Count(p => !string.IsNullOrEmpty(p.LCN)),
                partsWithAssieme = parts.Count(p => !string.IsNullOrEmpty(p.Assieme)),
                partsWithILS = parts.Count(p => p.ILS == true),
                uniqueTavole = parts.Select(p => p.<PERSON>).Distinct().Count(),
                uniquePartNumbers = parts.Select(p => p.PART).Distinct().Count(),
                averagePartsPerTavola = parts.Count() > 0 ? parts.Count() / (double)parts.Select(p => p.Tavola).Distinct().Count() : 0
            };

            _logger.LogInformation("Generated parts statistics for user {User}", User.Identity?.Name);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating parts statistics");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get parts grouped by Tavola (All authenticated users)
    /// </summary>
    [HttpGet("by-tavola")]
    public async Task<ActionResult<object>> GetByTavolaGrouped([FromQuery] int limit = 20)
    {
        try
        {
            var parts = await _partService.GetAllAsync();
            var grouped = parts
                .GroupBy(p => p.Tavola)
                .Select(g => new 
                {
                    Tavola = g.Key,
                    PartCount = g.Count(),
                    UniquePartNumbers = g.Select(p => p.PART).Distinct().Count(),
                    HasCsnref = g.Any(p => !string.IsNullOrEmpty(p.CSNREF)),
                    HasLcn = g.Any(p => !string.IsNullOrEmpty(p.LCN)),
                    ILSCount = g.Count(p => p.ILS == true),
                    AssemblyCount = g.Count(p => !string.IsNullOrEmpty(p.Assieme))
                })
                .OrderByDescending(g => g.PartCount)
                .Take(limit)
                .ToList();

            _logger.LogInformation("Retrieved parts grouped by Tavola (top {Limit}) for user {User}", limit, User.Identity?.Name);
            return Ok(grouped);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving parts grouped by Tavola");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get parts statistics by Tavola (All authenticated users)
    /// </summary>
    [HttpGet("statistics/by-tavola/{tavola}")]
    public async Task<ActionResult<object>> GetStatisticsByTavola(string tavola)
    {
        try
        {
            var parts = await _partService.GetByTavolaAsync(tavola);
            var stats = new
            {
                tavola,
                totalParts = parts.Count(),
                partsWithCsnref = parts.Count(p => !string.IsNullOrEmpty(p.CSNREF)),
                partsWithLcn = parts.Count(p => !string.IsNullOrEmpty(p.LCN)),
                partsWithAssieme = parts.Count(p => !string.IsNullOrEmpty(p.Assieme)),
                partsWithILS = parts.Count(p => p.ILS == true),
                uniquePartNumbers = parts.Select(p => p.PART).Distinct().Count(),
                uniqueItems = parts.Select(p => p.ITEM).Distinct().Count(),
                uniqueVersions = parts.Select(p => p.Versione).Distinct().Count()
            };

            _logger.LogInformation("Generated parts statistics for Tavola: {Tavola} for user {User}", tavola, User.Identity?.Name);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating parts statistics for Tavola: {Tavola}", tavola);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get ILS parts summary (All authenticated users)
    /// </summary>
    [HttpGet("statistics/ils")]
    public async Task<ActionResult<object>> GetILSStatistics()
    {
        try
        {
            var parts = await _partService.GetAllAsync();
            var ilsParts = parts.Where(p => p.ILS == true).ToList();
            var nonIlsParts = parts.Where(p => p.ILS == false || !p.ILS.HasValue).ToList();

            var stats = new
            {
                totalParts = parts.Count(),
                ilsParts = new
                {
                    count = ilsParts.Count,
                    percentage = parts.Count() > 0 ? (double)ilsParts.Count / parts.Count() * 100 : 0,
                    topTavole = ilsParts
                        .GroupBy(p => p.Tavola)
                        .Select(g => new { tavola = g.Key, count = g.Count() })
                        .OrderByDescending(x => x.count)
                        .Take(10)
                        .ToList()
                },
                nonIlsParts = new
                {
                    count = nonIlsParts.Count,
                    percentage = parts.Count() > 0 ? (double)nonIlsParts.Count / parts.Count() * 100 : 0
                }
            };

            _logger.LogInformation("Generated ILS statistics for user {User}", User.Identity?.Name);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating ILS statistics");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get parts count by various criteria (All authenticated users)
    /// </summary>
    [HttpGet("statistics/counts")]
    public async Task<ActionResult<object>> GetCounts()
    {
        try
        {
            var parts = await _partService.GetAllAsync();
            var counts = new
            {
                total = parts.Count(),
                byCsnref = new
                {
                    withCsnref = parts.Count(p => !string.IsNullOrEmpty(p.CSNREF)),
                    withoutCsnref = parts.Count(p => string.IsNullOrEmpty(p.CSNREF))
                },
                byLcn = new
                {
                    withLcn = parts.Count(p => !string.IsNullOrEmpty(p.LCN)),
                    withoutLcn = parts.Count(p => string.IsNullOrEmpty(p.LCN))
                },
                byAssieme = new
                {
                    withAssieme = parts.Count(p => !string.IsNullOrEmpty(p.Assieme)),
                    withoutAssieme = parts.Count(p => string.IsNullOrEmpty(p.Assieme))
                },
                byILS = new
                {
                    ils = parts.Count(p => p.ILS == true),
                    nonIls = parts.Count(p => p.ILS == false || !p.ILS.HasValue)
                }
            };

            _logger.LogInformation("Generated parts counts for user {User}", User.Identity?.Name);
            return Ok(counts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating parts counts");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get parts distribution by version (All authenticated users)
    /// </summary>
    [HttpGet("statistics/versions")]
    public async Task<ActionResult<object>> GetVersionDistribution([FromQuery] int limit = 20)
    {
        try
        {
            var parts = await _partService.GetAllAsync();
            var versionStats = parts
                .GroupBy(p => p.Versione)
                .Select(g => new 
                {
                    version = g.Key,
                    count = g.Count(),
                    percentage = parts.Count() > 0 ? (double)g.Count() / parts.Count() * 100 : 0,
                    uniqueTavole = g.Select(p => p.Tavola).Distinct().Count()
                })
                .OrderByDescending(v => v.count)
                .Take(limit)
                .ToList();

            _logger.LogInformation("Generated version distribution statistics for user {User}", User.Identity?.Name);
            return Ok(new 
            { 
                totalVersions = parts.Select(p => p.Versione).Distinct().Count(),
                distribution = versionStats
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating version distribution statistics");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    #endregion

    #region Report Endpoints

    /// <summary>
    /// Generate parts report for specific Tavola (All authenticated users)
    /// </summary>
    [HttpGet("reports/tavola/{tavola}")]
    public async Task<ActionResult<object>> GenerateTavolaReport(string tavola)
    {
        try
        {
            var parts = await _partService.GetByTavolaAsync(tavola);
            var report = new
            {
                tavola,
                generatedAt = DateTime.UtcNow,
                summary = new
                {
                    totalParts = parts.Count(),
                    uniquePartNumbers = parts.Select(p => p.PART).Distinct().Count(),
                    uniqueItems = parts.Select(p => p.ITEM).Distinct().Count(),
                    versions = parts.Select(p => p.Versione).Distinct().ToList()
                },
                details = new
                {
                    csnrefCoverage = new
                    {
                        withCsnref = parts.Count(p => !string.IsNullOrEmpty(p.CSNREF)),
                        withoutCsnref = parts.Count(p => string.IsNullOrEmpty(p.CSNREF)),
                        percentage = parts.Count() > 0 ? (double)parts.Count(p => !string.IsNullOrEmpty(p.CSNREF)) / parts.Count() * 100 : 0
                    },
                    lcnCoverage = new
                    {
                        withLcn = parts.Count(p => !string.IsNullOrEmpty(p.LCN)),
                        withoutLcn = parts.Count(p => string.IsNullOrEmpty(p.LCN)),
                        percentage = parts.Count() > 0 ? (double)parts.Count(p => !string.IsNullOrEmpty(p.LCN)) / parts.Count() * 100 : 0
                    },
                    ilsStatus = new
                    {
                        ils = parts.Count(p => p.ILS == true),
                        nonIls = parts.Count(p => p.ILS == false || !p.ILS.HasValue),
                        percentage = parts.Count() > 0 ? (double)parts.Count(p => p.ILS == true) / parts.Count() * 100 : 0
                    }
                }
            };

            _logger.LogInformation("Generated Tavola report for {Tavola} for user {User}", tavola, User.Identity?.Name);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating Tavola report for {Tavola}", tavola);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    #endregion
}
