using Ardec.Services.Web.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Ardec.Services.Web.Services.Compositions;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// API Controller for TB_Composizioni - component composition management
/// Handles hierarchy of parts/components within technical documentation system
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CompositionsController : ControllerBase
{
    private readonly ICompositionService _compositionService;
    private readonly ILogger<CompositionsController> _logger;

    public CompositionsController(ICompositionService compositionService, ILogger<CompositionsController> logger)
    {
        _compositionService = compositionService;
        _logger = logger;
    }

    /// <summary>
    /// Get all compositions (Admin view - All authenticated users)
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<TB_Composizione>>> GetAll()
    {
        try
        {
            var compositions = await _compositionService.GetAllCompositionsAsync();
            _logger.LogInformation("Retrieved {Count} compositions for user {User}", compositions.Count(), User.Identity?.Name);
            return Ok(compositions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all compositions");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get all tables in a catalog composition by TER (All authenticated users)
    /// </summary>
    [HttpGet("catalog/{ter}")]
    public async Task<ActionResult<IEnumerable<TB_Composizione>>> GetComposizioneByTer(string ter)
    {
        try
        {
            var compositions = await _compositionService.GetComposizioneByTerAsync(ter);
            _logger.LogInformation("Retrieved composition for TER {TER}: {Count} tables for user {User}", 
                ter, compositions.Count(), User.Identity?.Name);
            return Ok(compositions);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving composition for TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get ordered tables in catalog (All authenticated users)
    /// </summary>
    [HttpGet("catalog/{ter}/ordered")]
    public async Task<ActionResult<IEnumerable<TB_Composizione>>> GetOrderedTables(string ter)
    {
        try
        {
            var tables = await _compositionService.GetOrderedTablesAsync(ter);
            _logger.LogInformation("Retrieved {Count} ordered tables for TER {TER} for user {User}", 
                tables.Count(), ter, User.Identity?.Name);
            return Ok(tables);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving ordered tables for TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get specific composition entry by TER + Tavola (All authenticated users)
    /// </summary>
    [HttpGet("{ter}/{tavola}")]
    public async Task<ActionResult<TB_Composizione>> GetComposition(string ter, string tavola)
    {
        try
        {
            var composition = await _compositionService.GetCompositionAsync(ter, tavola);
            if (composition == null)
            {
                _logger.LogWarning("Composition with TER '{TER}' and Tavola '{Tavola}' not found", ter, tavola);
                return NotFound(new { error = "Composition not found", ter, tavola });
            }

            _logger.LogInformation("Retrieved composition: TER {TER}, Tavola {Tavola} for user {User}", ter, tavola, User.Identity?.Name);
            return Ok(composition);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving composition: TER {TER}, Tavola {Tavola}", ter, tavola);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Create new composition entry - Add table to catalog (Admin, PowerUser only)
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,PowerUser")]
    public async Task<ActionResult<TB_Composizione>> Create([FromBody] TB_Composizione composition)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var createdComposition = await _compositionService.CreateCompositionAsync(composition);
            _logger.LogInformation("Created composition: TER {TER}, Tavola {Tavola} by user {User}", 
                createdComposition.TER, createdComposition.Tavola, User.Identity?.Name);
            
            return CreatedAtAction(nameof(GetComposition), 
                new { ter = createdComposition.TER, tavola = createdComposition.Tavola }, 
                createdComposition);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating composition by user {User}", User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Update composition entry (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPut("{ter}/{tavola}")]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_Composizione>> Update(string ter, string tavola, [FromBody] TB_Composizione composition)
    {
        try
        {
            if (ter != composition.TER || tavola != composition.Tavola)
                return BadRequest(new { error = "TER/Tavola mismatch between route and body" });

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var updatedComposition = await _compositionService.UpdateCompositionAsync(composition);
            _logger.LogInformation("Updated composition: TER {TER}, Tavola {Tavola} by user {User}", ter, tavola, User.Identity?.Name);
            
            return Ok(updatedComposition);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating composition: TER {TER}, Tavola {Tavola} by user {User}", ter, tavola, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Delete composition entry - Remove table from catalog (Admin only)
    /// </summary>
    [HttpDelete("{ter}/{tavola}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> Delete(string ter, string tavola)
    {
        try
        {
            var deleted = await _compositionService.DeleteCompositionAsync(ter, tavola);
            if (!deleted)
            {
                return NotFound(new { error = "Composition not found", ter, tavola });
            }

            _logger.LogInformation("Deleted composition: TER {TER}, Tavola {Tavola} by user {User}", ter, tavola, User.Identity?.Name);
            return NoContent();
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting composition: TER {TER}, Tavola {Tavola} by user {User}", ter, tavola, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Update table order in composition (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPatch("{ter}/{tavola}/order")]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_Composizione>> UpdateTableOrder(string ter, string tavola, [FromBody] double newOrder)
    {
        try
        {
            var composition = await _compositionService.UpdateTableOrderAsync(ter, tavola, newOrder);
            _logger.LogInformation("Updated table order: TER {TER}, Tavola {Tavola}, Order {Order} by user {User}", 
                ter, tavola, newOrder, User.Identity?.Name);
            
            return Ok(composition);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating table order: TER {TER}, Tavola {Tavola} by user {User}", ter, tavola, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Check if composition entry exists (All authenticated users)
    /// </summary>
    [HttpHead("{ter}/{tavola}")]
    public async Task<ActionResult> Exists(string ter, string tavola)
    {
        try
        {
            var exists = await _compositionService.ExistsAsync(ter, tavola);
            if (exists)
            {
                return Ok();
            }
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if composition exists: TER {TER}, Tavola {Tavola}", ter, tavola);
            return StatusCode(500);
        }
    }

    /// <summary>
    /// Get language-specific groups for MAGIRUS functionality (All authenticated users)
    /// </summary>
    [HttpGet("catalog/{ter}/groups/{language}")]
    public async Task<ActionResult<IEnumerable<string>>> GetGruppiByLanguage(string ter, string language)
    {
        try
        {
            var groups = await _compositionService.GetGruppiByLanguageAsync(ter, language);
            _logger.LogInformation("Retrieved {Count} groups for TER {TER}, Language {Language} for user {User}", 
                groups.Count(), ter, language, User.Identity?.Name);
            
            return Ok(groups);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving groups for TER: {TER}, Language: {Language}", ter, language);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get composition statistics for a catalog (All authenticated users)
    /// </summary>
    [HttpGet("catalog/{ter}/statistics")]
    public async Task<ActionResult<object>> GetCompositionStatistics(string ter)
    {
        try
        {
            var stats = await _compositionService.GetCompositionStatisticsAsync(ter);
            _logger.LogInformation("Generated composition statistics for TER {TER} for user {User}", ter, User.Identity?.Name);
            return Ok(stats);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating composition statistics for TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Search compositions (All authenticated users)
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<TB_Composizione>>> Search([FromQuery] string q)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(q))
                return BadRequest(new { error = "Search query 'q' is required" });

            var results = await _compositionService.SearchCompositionsAsync(q);
            _logger.LogInformation("Search for '{Query}' returned {Count} composition results for user {User}", 
                q, results.Count(), User.Identity?.Name);
            
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching compositions with query: {Query} by user {User}", q, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }
}
