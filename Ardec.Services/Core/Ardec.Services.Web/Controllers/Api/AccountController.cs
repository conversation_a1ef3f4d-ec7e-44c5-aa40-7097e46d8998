using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Ardec.Services.Web.Features.Account;

/// <summary>
/// Account management controller for both MVC views and API operations
/// </summary>
[Route("[controller]")]
public class AccountController : Controller
{
    private readonly SignInManager<IdentityUser> _signInManager;
    private readonly UserManager<IdentityUser> _userManager;
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly ILogger<AccountController> _logger;

    public AccountController(
        SignInManager<IdentityUser> signInManager,
        UserManager<IdentityUser> userManager,
        RoleManager<IdentityRole> roleManager,
        ILogger<AccountController> logger)
    {
        _signInManager = signInManager;
        _userManager = userManager;
        _roleManager = roleManager;
        _logger = logger;
    }

    /// <summary>
    /// Login page (GET)
    /// </summary>
    [HttpGet("Login")]
    public IActionResult Login(string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;
        ViewData["Title"] = "Accesso Sistema";
        
        if (User.Identity?.IsAuthenticated == true)
        {
            return RedirectToAction("Index", "Home");
        }
        
        return View(new LoginViewModel());
    }

    /// <summary>
    /// Login form submission (POST)
    /// </summary>
    [HttpPost("Login")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Login(LoginViewModel model, string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;
        ViewData["Title"] = "Accesso Sistema";

        _logger.LogInformation("Login POST received. Email: {Email}, ModelState.IsValid: {IsValid}", 
            model?.Email ?? "null", ModelState.IsValid);

        if (!ModelState.IsValid)
        {
            foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
            {
                _logger.LogWarning("ModelState error: {Error}", error.ErrorMessage);
            }
            return View(model);
        }

        try
        {
            _logger.LogInformation("Attempting to find user with email: {Email}", model.Email);
            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user == null)
            {
                _logger.LogWarning("User not found with email: {Email}", model.Email);
                
                // Auto-create admin user if it doesn't exist and trying to login as admin
                if (model.Email == "<EMAIL>")
                {
                    _logger.LogInformation("Creating admin user automatically...");
                    await CreateAdminUserIfNotExists();
                    user = await _userManager.FindByEmailAsync(model.Email);
                    
                    if (user != null)
                    {
                        _logger.LogInformation("Admin user created successfully");
                    }
                    else
                    {
                        _logger.LogError("Failed to create admin user");
                    }
                }
                
                if (user == null)
                {
                    ModelState.AddModelError(string.Empty, "Email o password non validi.");
                    return View(model);
                }
            }

            _logger.LogInformation("User found: Id={UserId}, UserName={UserName}, Email={Email}, LockoutEnd={LockoutEnd}, AccessFailedCount={AccessFailedCount}", 
                user.Id, user.UserName, user.Email, user.LockoutEnd, user.AccessFailedCount);

            _logger.LogInformation("Attempting password sign-in for user {Email} with password length {PasswordLength}", 
                model.Email, model.Password?.Length ?? 0);

            // Check password directly for debugging
            var passwordCheck = await _userManager.CheckPasswordAsync(user, model.Password);
            _logger.LogInformation("Direct password check result: {PasswordValid}", passwordCheck);

            var result = await _signInManager.PasswordSignInAsync(user, model.Password, model.RememberMe, lockoutOnFailure: true);

            _logger.LogInformation("SignIn result: Succeeded={Succeeded}, IsLockedOut={IsLockedOut}, RequiresTwoFactor={RequiresTwoFactor}, IsNotAllowed={IsNotAllowed}", 
                result.Succeeded, result.IsLockedOut, result.RequiresTwoFactor, result.IsNotAllowed);

            if (result.Succeeded)
            {
                _logger.LogInformation("User {Email} logged in successfully", model.Email);
                
                if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
                {
                    return Redirect(returnUrl);
                }
                
                return RedirectToAction("Index", "Home");
            }

            if (result.IsLockedOut)
            {
                ModelState.AddModelError(string.Empty, "Account bloccato per troppi tentativi falliti.");
                return View(model);
            }

            ModelState.AddModelError(string.Empty, "Email o password non validi.");
            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login attempt for {Email}", model.Email);
            ModelState.AddModelError(string.Empty, "Errore interno del sistema.");
            return View(model);
        }
    }

    /// <summary>
    /// API Login with email and password
    /// </summary>
    [HttpPost("api/login")]
    public async Task<ActionResult<LoginResponse>> ApiLogin([FromBody] LoginRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var user = await _userManager.FindByEmailAsync(request.Email);
            if (user == null)
            {
                _logger.LogWarning("API login attempt with non-existent email: {Email}", request.Email);
                return Unauthorized(new { error = "Invalid email or password" });
            }

            var result = await _signInManager.PasswordSignInAsync(user, request.Password, request.RememberMe, lockoutOnFailure: true);

            if (result.Succeeded)
            {
                var roles = await _userManager.GetRolesAsync(user);
                _logger.LogInformation("User {Email} logged in via API successfully", request.Email);
                
                return Ok(new LoginResponse
                {
                    Success = true,
                    Email = user.Email!,
                    UserName = user.UserName!,
                    Roles = roles.ToList()
                });
            }

            if (result.IsLockedOut)
            {
                _logger.LogWarning("User {Email} account locked out via API", request.Email);
                return Unauthorized(new { error = "Account locked out" });
            }

            _logger.LogWarning("Failed API login attempt for {Email}", request.Email);
            return Unauthorized(new { error = "Invalid email or password" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during API login attempt for {Email}", request.Email);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Logout form submission
    /// </summary>
    [HttpPost("Logout")]
    [Authorize]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Logout()
    {
        await _signInManager.SignOutAsync();
        _logger.LogInformation("User logged out");
        return RedirectToAction("Index", "Home");
    }

    /// <summary>
    /// API Logout current user
    /// </summary>
    [HttpPost("api/logout")]
    [Authorize]
    public async Task<ActionResult> ApiLogout()
    {
        try
        {
            await _signInManager.SignOutAsync();
            _logger.LogInformation("User logged out via API");
            return Ok(new { message = "Logged out successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during API logout");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Profile page (GET)
    /// </summary>
    [HttpGet("Profile")]
    [Authorize]
    public async Task<IActionResult> Profile()
    {
        ViewData["Title"] = "Profilo Utente";
        
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return RedirectToAction("Login");
        }

        var roles = await _userManager.GetRolesAsync(user);

        var model = new ProfileViewModel
        {
            Email = user.Email!,
            UserName = user.UserName!,
            Roles = roles.ToList(),
            EmailConfirmed = user.EmailConfirmed,
            LockoutEnabled = user.LockoutEnabled,
            AccessFailedCount = user.AccessFailedCount
        };

        return View(model);
    }

    /// <summary>
    /// API Get current user profile
    /// </summary>
    [HttpGet("api/profile")]
    [Authorize]
    public async Task<ActionResult<UserProfileResponse>> ApiGetProfile()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
                return Unauthorized();

            var roles = await _userManager.GetRolesAsync(user);

            return Ok(new UserProfileResponse
            {
                Email = user.Email!,
                UserName = user.UserName!,
                Roles = roles.ToList(),
                EmailConfirmed = user.EmailConfirmed,
                LockoutEnabled = user.LockoutEnabled,
                AccessFailedCount = user.AccessFailedCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user profile");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get current user roles
    /// </summary>
    [HttpGet("roles")]
    [Authorize]
    public async Task<ActionResult<IEnumerable<string>>> GetUserRoles()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
                return Unauthorized();

            var roles = await _userManager.GetRolesAsync(user);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user roles");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Check if user has specific role
    /// </summary>
    [HttpGet("check-role/{roleName}")]
    [Authorize]
    public async Task<ActionResult<bool>> CheckRole(string roleName)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
                return Unauthorized();

            var hasRole = await _userManager.IsInRoleAsync(user, roleName);
            return Ok(new { role = roleName, hasRole });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user role: {RoleName}", roleName);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get all available roles (Admin only)
    /// </summary>
    [HttpGet("all-roles")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<IEnumerable<string>>> GetAllRoles()
    {
        try
        {
            var roles = await Task.FromResult(_roleManager.Roles.Select(r => r.Name!).ToList());
            return Ok(roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all roles");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Development endpoint to reset admin password
    /// </summary>
    [HttpGet("reset-admin-password")]
    public async Task<IActionResult> ResetAdminPassword()
    {
        if (!HttpContext.Request.Host.Host.Contains("localhost"))
        {
            return NotFound();
        }

        try
        {
            var user = await _userManager.FindByEmailAsync("<EMAIL>");
            if (user == null)
            {
                return Json(new { success = false, message = "Admin user not found" });
            }

            // Remove current password
            await _userManager.RemovePasswordAsync(user);
            
            // Set new password with current Identity version
            var result = await _userManager.AddPasswordAsync(user, "Admin123!");
            
            if (result.Succeeded)
            {
                // Also reset lockout
                await _userManager.SetLockoutEndDateAsync(user, null);
                await _userManager.ResetAccessFailedCountAsync(user);
                
                _logger.LogInformation("Admin password reset successfully");
                
                return Json(new { 
                    success = true, 
                    message = "Admin password reset to Admin123! successfully"
                });
            }
            else
            {
                return Json(new { 
                    success = false, 
                    message = "Failed to reset password",
                    errors = result.Errors.Select(e => e.Description)
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting admin password");
            return Json(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Account Settings page (GET)
    /// </summary>
    [HttpGet("Settings")]
    [Authorize]
    public async Task<IActionResult> Settings()
    {
        ViewData["Title"] = "Impostazioni Account";
        
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return RedirectToAction("Login");
        }

        var roles = await _userManager.GetRolesAsync(user);

        var model = new AccountSettingsViewModel
        {
            Email = user.Email!,
            UserName = user.UserName!,
            EmailConfirmed = user.EmailConfirmed,
            CurrentEmail = user.Email!,
            TwoFactorEnabled = user.TwoFactorEnabled,
            Roles = roles.ToList()
        };

        return View(model);
    }

    /// <summary>
    /// Update account settings (POST)
    /// </summary>
    [HttpPost("Settings")]
    [Authorize]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Settings(AccountSettingsViewModel model)
    {
        ViewData["Title"] = "Impostazioni Account";

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return RedirectToAction("Login");
        }

        var hasChanges = false;

        // Update username if changed
        if (!string.IsNullOrEmpty(model.UserName) && model.UserName != user.UserName)
        {
            var setUserNameResult = await _userManager.SetUserNameAsync(user, model.UserName);
            if (setUserNameResult.Succeeded)
            {
                hasChanges = true;
                _logger.LogInformation("Username updated for user {UserId}", user.Id);
            }
            else
            {
                foreach (var error in setUserNameResult.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
                return View(model);
            }
        }

        // Update email if changed
        if (!string.IsNullOrEmpty(model.Email) && model.Email != user.Email)
        {
            var setEmailResult = await _userManager.SetEmailAsync(user, model.Email);
            if (setEmailResult.Succeeded)
            {
                hasChanges = true;
                user.EmailConfirmed = false; // Email needs to be confirmed again
                await _userManager.UpdateAsync(user);
                _logger.LogInformation("Email updated for user {UserId}", user.Id);
            }
            else
            {
                foreach (var error in setEmailResult.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
                return View(model);
            }
        }

        // Update password if provided
        if (!string.IsNullOrEmpty(model.NewPassword))
        {
            if (string.IsNullOrEmpty(model.CurrentPassword))
            {
                ModelState.AddModelError("CurrentPassword", "Password attuale richiesta per cambiarla.");
                return View(model);
            }

            var changePasswordResult = await _userManager.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);
            if (changePasswordResult.Succeeded)
            {
                hasChanges = true;
                _logger.LogInformation("Password changed for user {UserId}", user.Id);
                
                // Re-sign in to update security stamp
                await _signInManager.RefreshSignInAsync(user);
            }
            else
            {
                foreach (var error in changePasswordResult.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
                return View(model);
            }
        }

        if (hasChanges)
        {
            TempData["SuccessMessage"] = "Le impostazioni sono state aggiornate con successo.";
        }

        return RedirectToAction("Settings");
    }

    /// <summary>
    /// Development endpoint to unlock admin user
    /// </summary>
    [HttpGet("unlock-admin")]
    public async Task<IActionResult> UnlockAdmin()
    {
        if (!HttpContext.Request.Host.Host.Contains("localhost"))
        {
            return NotFound();
        }

        try
        {
            var user = await _userManager.FindByEmailAsync("<EMAIL>");
            if (user == null)
            {
                return Json(new { success = false, message = "Admin user not found" });
            }

            // Reset lockout
            await _userManager.SetLockoutEndDateAsync(user, null);
            await _userManager.ResetAccessFailedCountAsync(user);
            
            _logger.LogInformation("Admin user lockout reset");
            
            return Json(new { 
                success = true, 
                message = "Admin user unlocked successfully",
                user = new { 
                    user.Email, 
                    user.LockoutEnabled,
                    LockoutEnd = user.LockoutEnd?.ToString() ?? "null",
                    user.AccessFailedCount
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unlocking admin user");
            return Json(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Creates the admin user if it doesn't exist (fallback for missing seed data)
    /// </summary>
    private async Task CreateAdminUserIfNotExists()
    {
        try
        {
            // Create Admin role if it doesn't exist
            var adminRole = await _roleManager.FindByNameAsync("Admin");
            if (adminRole == null)
            {
                adminRole = new IdentityRole("Admin");
                await _roleManager.CreateAsync(adminRole);
                _logger.LogInformation("Created Admin role");
            }

            // Create admin user
            var adminUser = new IdentityUser
            {
                Id = "550e8400-e29b-41d4-a716-446655440000",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                EmailConfirmed = true,
                LockoutEnabled = true
            };

            var result = await _userManager.CreateAsync(adminUser, "Admin123!");
            if (result.Succeeded)
            {
                await _userManager.AddToRoleAsync(adminUser, "Admin");
                _logger.LogInformation("Admin user created with password Admin123!");
            }
            else
            {
                _logger.LogError("Failed to create admin user: {Errors}", 
                    string.Join(", ", result.Errors.Select(e => e.Description)));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating admin user");
        }
    }
}

