using Ardec.Services.Web.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// PartsController partial class - CID and Versioning Operations
/// Handles CID validation, versioning, and advanced part management
/// </summary>
public partial class PartsController
{
    #region CID and Versioning Endpoints

    /// <summary>
    /// Get parts by CID (All authenticated users)
    /// </summary>
    [HttpGet("by-cid/{cid}")]
    public async Task<ActionResult<IEnumerable<TB_Parti>>> GetPartsByCid(string cid, [FromQuery] int limit = 50)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(cid))
                return BadRequest(new { error = "CID is required" });

            var parts = await _partService.GetByCidAsync(cid);
            var limitedParts = parts.Take(limit).ToList();
            
            _logger.LogInformation("Retrieved {Count} parts for CID: {CID} for user {User}", 
                limitedParts.Count, cid, User.Identity?.Name);
            
            return Ok(limitedParts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving parts by CID: {CID}", cid);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get all versions of a specific part (All authenticated users)
    /// </summary>
    [HttpGet("versions/{tavola}/{part}/{item}")]
    public async Task<ActionResult<IEnumerable<TB_Parti>>> GetPartVersions(string tavola, string part, string item)
    {
        try
        {
            var parts = await _partService.GetVersionsAsync(tavola, part, item);
            _logger.LogInformation("Retrieved {Count} versions for part: {Part}/{Item} for user {User}", 
                parts.Count(), part, item, User.Identity?.Name);
            
            return Ok(parts.OrderBy(p => p.Versione));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving versions for part: {Part}/{Item}", part, item);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get latest version of a specific part (All authenticated users)
    /// </summary>
    [HttpGet("latest/{tavola}/{part}/{item}")]
    public async Task<ActionResult<TB_Parti>> GetLatestPartVersion(string tavola, string part, string item)
    {
        try
        {
            var latestPart = await _partService.GetLatestVersionAsync(tavola, part, item);
            if (latestPart == null)
            {
                return NotFound(new { error = "Part not found", tavola, part, item });
            }

            _logger.LogInformation("Retrieved latest version for part: {Part}/{Item} v{Version} for user {User}", 
                part, item, latestPart.Versione, User.Identity?.Name);
            
            return Ok(latestPart);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving latest version for part: {Part}/{Item}", part, item);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Validate CID for a part (All authenticated users)
    /// </summary>
    [HttpPost("validate-cid")]
    public async Task<ActionResult<object>> ValidateCid([FromBody] CidValidationRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var isValid = await _partService.ValidateCidAsync(request.CID, request.Tavola, request.Part, request.Item);
            var existingPart = await _partService.GetByCidAsync(request.CID);
            
            var response = new
            {
                isValid,
                cid = request.CID,
                existingParts = existingPart.Count(),
                conflicts = existingPart.Where(p => 
                    p.Tavola != request.Tavola || 
                    p.PART != request.Part || 
                    p.ITEM != request.Item).ToList()
            };

            _logger.LogInformation("CID validation for {CID}: {IsValid} for user {User}", 
                request.CID, isValid, User.Identity?.Name);
            
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating CID: {CID}", request?.CID);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Create new part version (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPost("create-version")]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_Parti>> CreateNewVersion([FromBody] CreateVersionRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var newPart = await _partService.CreateNewVersionAsync(
                request.Tavola, 
                request.Part, 
                request.Item, 
                request.FromVersion, 
                request.ToVersion,
                request.NewCid);

            _logger.LogInformation("Created new version for part: {Part}/{Item} from v{FromVer} to v{ToVer} by user {User}", 
                request.Part, request.Item, request.FromVersion, request.ToVersion, User.Identity?.Name);
            
            return CreatedAtAction(nameof(GetByCompositeKey), 
                new { tavola = newPart.Tavola, part = newPart.PART, item = newPart.ITEM, versione = newPart.Versione }, 
                newPart);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating new version for part: {Part}/{Item} by user {User}", 
                request?.Part, request?.Item, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get CID statistics (All authenticated users)
    /// </summary>
    [HttpGet("statistics/cid")]
    public async Task<ActionResult<object>> GetCidStatistics()
    {
        try
        {
            var parts = await _partService.GetAllAsync();
            var stats = new
            {
                totalParts = parts.Count(),
                partsWithCid = parts.Count(p => !string.IsNullOrEmpty(p.CID)),
                partsWithoutCid = parts.Count(p => string.IsNullOrEmpty(p.CID)),
                uniqueCids = parts.Where(p => !string.IsNullOrEmpty(p.CID)).Select(p => p.CID).Distinct().Count(),
                cidCoverage = parts.Count() > 0 ? (double)parts.Count(p => !string.IsNullOrEmpty(p.CID)) / parts.Count() * 100 : 0,
                duplicatedCids = parts.Where(p => !string.IsNullOrEmpty(p.CID))
                    .GroupBy(p => p.CID)
                    .Where(g => g.Count() > 1)
                    .Count()
            };

            _logger.LogInformation("Generated CID statistics for user {User}", User.Identity?.Name);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CID statistics");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get version history for a part (All authenticated users)
    /// </summary>
    [HttpGet("history/{tavola}/{part}/{item}")]
    public async Task<ActionResult<object>> GetPartHistory(string tavola, string part, string item)
    {
        try
        {
            var versions = await _partService.GetVersionsAsync(tavola, part, item);
            var history = versions.OrderBy(p => p.Versione).Select(p => new
            {
                versione = p.Versione,
                cid = p.CID,
                qtav = p.QTAV,
                codModifica = p.CodModifica,
                // Add more historical fields as needed
            }).ToList();

            var response = new
            {
                tavola,
                part,
                item,
                totalVersions = versions.Count(),
                versions = history,
                latestVersion = versions.OrderByDescending(p => p.Versione).FirstOrDefault()?.Versione
            };

            _logger.LogInformation("Retrieved history for part: {Part}/{Item} ({Count} versions) for user {User}", 
                part, item, versions.Count(), User.Identity?.Name);
            
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving history for part: {Part}/{Item}", part, item);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    #endregion
}

/// <summary>
/// CID validation request model
/// </summary>
public class CidValidationRequest
{
    public string CID { get; set; } = string.Empty;
    public string Tavola { get; set; } = string.Empty;
    public string Part { get; set; } = string.Empty;
    public string Item { get; set; } = string.Empty;
}

/// <summary>
/// Create version request model
/// </summary>
public class CreateVersionRequest
{
    public string Tavola { get; set; } = string.Empty;
    public string Part { get; set; } = string.Empty;
    public string Item { get; set; } = string.Empty;
    public string FromVersion { get; set; } = string.Empty;
    public string ToVersion { get; set; } = string.Empty;
    public string? NewCid { get; set; }
}
