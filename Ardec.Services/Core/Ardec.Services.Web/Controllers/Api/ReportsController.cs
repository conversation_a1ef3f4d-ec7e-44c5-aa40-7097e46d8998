using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Ardec.Services.Web.Services;
using Ardec.Services.Web.DTOs.Reports;
using System.ComponentModel.DataAnnotations;

namespace Ardec.Services.Web.Features.Reports
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportsController : ControllerBase
    {
        private readonly IReportService _reportService;
        private readonly ILogger<ReportsController> _logger;

        public ReportsController(IReportService reportService, ILogger<ReportsController> logger)
        {
            _reportService = reportService;
            _logger = logger;
        }

        /// <summary>
        /// Esporta un catalogo completo in formato Excel
        /// </summary>
        /// <param name="request">Parametri per l'export del catalogo</param>
        /// <returns>File Excel del catalogo</returns>
        [HttpPost("export-catalog-excel")]
        public async Task<IActionResult> ExportCatalogExcel([FromBody] ExportCatalogRequest request)
        {
            try
            {
                _logger.LogInformation("Avvio export catalogo Excel per TER: {TER}", request.TER);

                var result = await _reportService.ExportCatalogToExcelAsync(request);
                
                return File(result.FileContent, 
                           "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                           result.FileName);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Parametri non validi per export catalogo: {Error}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Errore durante export catalogo Excel per TER: {TER}", request.TER);
                return StatusCode(500, new { error = "Errore interno durante l'export del catalogo" });
            }
        }

        /// <summary>
        /// Esporta Data Modules in formato XML S1000D
        /// </summary>
        /// <param name="request">Parametri per l'export XML</param>
        /// <returns>File ZIP contenente i Data Modules XML</returns>
        [HttpPost("export-dm-xml")]
        public async Task<IActionResult> ExportDataModulesXml([FromBody] ExportDMRequest request)
        {
            try
            {
                _logger.LogInformation("Avvio export DM XML per {Count} tavole, lingua: {Language}", 
                                     request.Tavole.Count, request.Language);

                var result = await _reportService.ExportDataModulesToXmlAsync(request);

                if (request.Tavole.Count == 1)
                {
                    // Export singolo - ritorna il file XML direttamente
                    return File(result.FileContent, "application/xml", result.FileName);
                }
                else
                {
                    // Export multiplo - ritorna ZIP
                    return File(result.FileContent, "application/zip", result.FileName);
                }
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Parametri non validi per export DM XML: {Error}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Errore durante export DM XML");
                return StatusCode(500, new { error = "Errore interno durante l'export XML" });
            }
        }

        /// <summary>
        /// Esporta tavole o parti in formato CSV per debug
        /// </summary>
        /// <param name="ter">TER del catalogo</param>
        /// <param name="type">Tipo di export (tavole/parti/singola-tavola)</param>
        /// <param name="tavola">Nome tavola (opzionale, per export singola tavola)</param>
        /// <param name="isS1000D">Se includere colonne specifiche S1000D</param>
        /// <returns>File CSV</returns>
        [HttpGet("export-csv")]
        public async Task<IActionResult> ExportCsv(
            [Required] string ter,
            [Required] string type,
            string? tavola = null,
            bool isS1000D = false)
        {
            try
            {
                _logger.LogInformation("Avvio export CSV tipo: {Type} per TER: {TER}", type, ter);

                var request = new ExportCsvRequest
                {
                    TER = ter,
                    Type = type,
                    Tavola = tavola,
                    IsS1000D = isS1000D
                };

                var result = await _reportService.ExportToCsvAsync(request);

                return File(result.FileContent, "text/csv", result.FileName);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Parametri non validi per export CSV: {Error}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Errore durante export CSV per TER: {TER}", ter);
                return StatusCode(500, new { error = "Errore interno durante l'export CSV" });
            }
        }

        /// <summary>
        /// Esporta archivio completo tramite stored procedure ottimizzata
        /// </summary>
        /// <param name="format">Formato output (excel/csv)</param>
        /// <returns>File archivio completo</returns>
        [HttpPost("export-archive")]
        public async Task<IActionResult> ExportCompleteArchive([FromQuery] string format = "excel")
        {
            try
            {
                _logger.LogInformation("Avvio export archivio completo formato: {Format}", format);

                var result = await _reportService.ExportCompleteArchiveAsync(format);

                var contentType = format.ToLower() == "excel" 
                    ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    : "text/csv";

                return File(result.FileContent, contentType, result.FileName);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Formato non valido per export archivio: {Error}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Errore durante export archivio completo");
                return StatusCode(500, new { error = "Errore interno durante l'export archivio" });
            }
        }

        /// <summary>
        /// Ottiene la lista dei TER disponibili per l'export
        /// </summary>
        /// <returns>Lista dei cataloghi disponibili</returns>
        [HttpGet("available-catalogs")]
        public async Task<IActionResult> GetAvailableCatalogs()
        {
            try
            {
                var catalogs = await _reportService.GetAvailableCatalogsAsync();
                return Ok(catalogs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Errore durante recupero cataloghi disponibili");
                return StatusCode(500, new { error = "Errore interno" });
            }
        }

        /// <summary>
        /// Ottiene la lista delle tavole disponibili per un catalogo
        /// </summary>
        /// <param name="ter">TER del catalogo</param>
        /// <returns>Lista delle tavole del catalogo</returns>
        [HttpGet("catalog-tables/{ter}")]
        public async Task<IActionResult> GetCatalogTables(string ter)
        {
            try
            {
                var tables = await _reportService.GetCatalogTablesAsync(ter);
                return Ok(tables);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("TER non valido: {Error}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Errore durante recupero tavole per TER: {TER}", ter);
                return StatusCode(500, new { error = "Errore interno" });
            }
        }
    }
}
