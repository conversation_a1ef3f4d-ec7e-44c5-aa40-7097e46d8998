using Ardec.Services.Web.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Ardec.Services.Web.Services.Parts;
using Ardec.Services.Web.DTOs.Parts;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// Parts REST API Controller - Main class with TB_Parti CRUD operations
/// Supports composite keys (Tavola, PART, ITEM, Versione) and RBAC authorization
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public partial class PartsController : ControllerBase
{
    private readonly IPartService _partService;
    private readonly ILogger<PartsController> _logger;

    public PartsController(IPartService partService, ILogger<PartsController> logger)
    {
        _partService = partService;
        _logger = logger;
    }

    #region TB_Parti CRUD Operations

    /// <summary>
    /// Get all parts (All authenticated users)
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<TB_Parti>>> GetAll([FromQuery] int limit = 100)
    {
        try
        {
            var parts = await _partService.GetAllAsync();
            var limitedParts = parts.Take(limit).ToList();
            
            _logger.LogInformation("Retrieved {Count} parts (limited to {Limit}) for user {User}", 
                limitedParts.Count, limit, User.Identity?.Name);
            
            return Ok(new 
            { 
                parts = limitedParts, 
                totalCount = parts.Count(),
                returnedCount = limitedParts.Count,
                hasMore = parts.Count() > limit
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all parts");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get parts by Tavola (All authenticated users)
    /// </summary>
    [HttpGet("tavola/{tavola}")]
    public async Task<ActionResult<IEnumerable<TB_Parti>>> GetByTavola(string tavola)
    {
        try
        {
            var parts = await _partService.GetByTavolaAsync(tavola);
            _logger.LogInformation("Retrieved {Count} parts for Tavola: {Tavola} for user {User}", 
                parts.Count(), tavola, User.Identity?.Name);
            
            return Ok(parts);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving parts for Tavola: {Tavola}", tavola);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get part by composite key (All authenticated users)
    /// </summary>
    [HttpGet("{tavola}/{part}/{item}/{versione}")]
    public async Task<ActionResult<TB_Parti>> GetByCompositeKey(string tavola, string part, string item, string versione)
    {
        try
        {
            var partEntity = await _partService.GetByCompositeKeyAsync(tavola, part, item, versione);
            if (partEntity == null)
            {
                _logger.LogWarning("Part not found: Tavola={Tavola}, Part={Part}, Item={Item}, Versione={Versione}", 
                    tavola, part, item, versione);
                return NotFound(new { error = "Part not found", tavola, part, item, versione });
            }

            _logger.LogInformation("Retrieved part: {Part}/{Item} for user {User}", part, item, User.Identity?.Name);
            return Ok(partEntity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving part: {Part}/{Item}", part, item);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Create new part (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_Parti>> Create([FromBody] TB_Parti part)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var createdPart = await _partService.CreateAsync(part);
            _logger.LogInformation("Created part: {Part}/{Item} by user {User}", 
                createdPart.PART, createdPart.ITEM, User.Identity?.Name);
            
            return CreatedAtAction(nameof(GetByCompositeKey), 
                new { tavola = createdPart.Tavola, part = createdPart.PART, item = createdPart.ITEM, versione = createdPart.Versione }, 
                createdPart);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating part by user {User}", User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Update part (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPut("{tavola}/{part}/{item}/{versione}")]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_Parti>> Update(string tavola, string part, string item, string versione, [FromBody] TB_Parti partEntity)
    {
        try
        {
            if (tavola != partEntity.Tavola || part != partEntity.PART || item != partEntity.ITEM || versione != partEntity.Versione)
                return BadRequest(new { error = "Composite key mismatch between route and body" });

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var updatedPart = await _partService.UpdateAsync(partEntity);
            _logger.LogInformation("Updated part: {Part}/{Item} by user {User}", part, item, User.Identity?.Name);
            
            return Ok(updatedPart);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating part: {Part}/{Item} by user {User}", part, item, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Delete part (Admin only)
    /// </summary>
    [HttpDelete("{tavola}/{part}/{item}/{versione}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> Delete(string tavola, string part, string item, string versione)
    {
        try
        {
            var deleted = await _partService.DeleteAsync(tavola, part, item, versione);
            if (!deleted)
            {
                return NotFound(new { error = "Part not found", tavola, part, item, versione });
            }

            _logger.LogInformation("Deleted part: {Part}/{Item} by user {User}", part, item, User.Identity?.Name);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting part: {Part}/{Item} by user {User}", part, item, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Check if part exists (All authenticated users)
    /// </summary>
    [HttpHead("{tavola}/{part}/{item}/{versione}")]
    public async Task<ActionResult> Exists(string tavola, string part, string item, string versione)
    {
        try
        {
            var exists = await _partService.ExistsAsync(tavola, part, item, versione);
            if (exists)
            {
                return Ok();
            }
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if part exists: {Part}/{Item}", part, item);
            return StatusCode(500);
        }
    }

    #endregion
}
