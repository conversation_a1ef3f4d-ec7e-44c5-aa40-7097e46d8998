using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Ardec.Services.Web.Shared.Authorization;
using Ardec.Services.Web.Services.CataloghiRVT;
using Ardec.Services.Web.DTOs.CataloghiRVT;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// Controller per la gestione delle relazioni Cataloghi-RVT (TB_CataloghiRVT)
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CataloghiRVTController : ControllerBase
{
    private readonly ICataloghiRVTService _cataloghiRVTService;
    private readonly ILogger<CataloghiRVTController> _logger;

    public CataloghiRVTController(
        ICataloghiRVTService cataloghiRVTService,
        ILogger<CataloghiRVTController> logger)
    {
        _cataloghiRVTService = cataloghiRVTService;
        _logger = logger;
    }

    /// <summary>
    /// Ottiene tutti i record CataloghiRVT con paginazione
    /// </summary>
    [HttpGet]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<CataloghiRVTPagedResult>> GetAll(
        [FromQuery] int skip = 0,
        [FromQuery] int take = 20,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetAll CataloghiRVT con skip: {Skip}, take: {Take}", skip, take);

        if (take > 100)
        {
            take = 100; // Limite massimo
        }

        var result = await _cataloghiRVTService.GetAllAsync(skip, take, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Ottiene un record specifico per TER e RVT
    /// </summary>
    [HttpGet("{ter}/{rvt}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<CataloghiRVTResponse>> GetByKeys(
        string ter, 
        string rvt, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetByKeys per TER: {TER}, RVT: {RVT}", ter, rvt);

        var result = await _cataloghiRVTService.GetByKeysAsync(ter, rvt, cancellationToken);
        
        if (result == null)
        {
            return NotFound($"CatalogoRVT con TER '{ter}' e RVT '{rvt}' non trovato");
        }

        return Ok(result);
    }

    /// <summary>
    /// Cerca record CataloghiRVT con filtri avanzati
    /// </summary>
    [HttpPost("search")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<CataloghiRVTPagedResult>> Search(
        [FromBody] CataloghiRVTSearchRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta Search CataloghiRVT con filtri: {@Request}", request);

        if (request.Take > 100)
        {
            request = request with { Take = 100 }; // Limite massimo
        }

        var result = await _cataloghiRVTService.SearchAsync(request, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Ottiene tutti i record per un TER specifico
    /// </summary>
    [HttpGet("by-ter/{ter}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<CataloghiRVTResponse>>> GetByTer(
        string ter, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetByTer per TER: {TER}", ter);

        var result = await _cataloghiRVTService.GetByTerAsync(ter, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Ottiene tutti i record per un RVT specifico
    /// </summary>
    [HttpGet("by-rvt/{rvt}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<CataloghiRVTResponse>>> GetByRvt(
        string rvt, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetByRvt per RVT: {RVT}", rvt);

        var result = await _cataloghiRVTService.GetByRvtAsync(rvt, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Crea un nuovo record CatalogoRVT
    /// </summary>
    [HttpPost]
    [Authorize(Policy = Policies.WriteAccess)]
    public async Task<ActionResult<CataloghiRVTResponse>> Create(
        [FromBody] CreateCataloghiRVTRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta creazione CatalogoRVT: {@Request}", request);

        try
        {
            var result = await _cataloghiRVTService.CreateAsync(request, cancellationToken);
            return CreatedAtAction(
                nameof(GetByKeys), 
                new { ter = result.TER, rvt = result.RVT }, 
                result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Errore durante la creazione del CatalogoRVT");
            return Conflict(ex.Message);
        }
    }

    /// <summary>
    /// Aggiorna un record CatalogoRVT esistente
    /// </summary>
    [HttpPut("{ter}/{rvt}")]
    [Authorize(Policy = Policies.WriteAccess)]
    public async Task<ActionResult<CataloghiRVTResponse>> Update(
        string ter,
        string rvt,
        [FromBody] UpdateCataloghiRVTRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta aggiornamento CatalogoRVT TER: {TER}, RVT: {RVT}", ter, rvt);

        var result = await _cataloghiRVTService.UpdateAsync(ter, rvt, request, cancellationToken);
        
        if (result == null)
        {
            return NotFound($"CatalogoRVT con TER '{ter}' e RVT '{rvt}' non trovato");
        }

        return Ok(result);
    }

    /// <summary>
    /// Elimina un record CatalogoRVT
    /// </summary>
    [HttpDelete("{ter}/{rvt}")]
    [Authorize(Policy = Policies.DeleteAccess)]
    public async Task<ActionResult> Delete(
        string ter,
        string rvt,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta eliminazione CatalogoRVT TER: {TER}, RVT: {RVT}", ter, rvt);

        var success = await _cataloghiRVTService.DeleteAsync(ter, rvt, cancellationToken);
        
        if (!success)
        {
            return NotFound($"CatalogoRVT con TER '{ter}' e RVT '{rvt}' non trovato");
        }

        return NoContent();
    }

    /// <summary>
    /// Verifica se esiste un record CatalogoRVT
    /// </summary>
    [HttpHead("{ter}/{rvt}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult> Exists(
        string ter,
        string rvt,
        CancellationToken cancellationToken = default)
    {
        var exists = await _cataloghiRVTService.ExistsAsync(ter, rvt, cancellationToken);
        return exists ? Ok() : NotFound();
    }

    /// <summary>
    /// Ottiene statistiche dei CataloghiRVT
    /// </summary>
    [HttpGet("stats")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<CataloghiRVTStatsResponse>> GetStats(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta statistiche CataloghiRVT");

        var result = await _cataloghiRVTService.GetStatsAsync(cancellationToken);
        return Ok(result);
    }
}
