using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Ardec.Services.Web.Features.UserManagement;
using Ardec.Services.Web.Shared.Authorization;

namespace Ardec.Services.Web.Features.UserManagement;

/// <summary>
/// Controller per la gestione degli utenti e dei ruoli (solo Admin)
/// </summary>
[Route("UserManagement")]
[Authorize(Policy = Policies.CanManageUsers)]
public class UserManagementController : Controller
{
    private readonly UserManager<IdentityUser> _userManager;
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly ILogger<UserManagementController> _logger;

    public UserManagementController(
        UserManager<IdentityUser> userManager,
        RoleManager<IdentityRole> roleManager,
        ILogger<UserManagementController> logger)
    {
        _userManager = userManager;
        _roleManager = roleManager;
        _logger = logger;
    }

    #region Views

    /// <summary>
    /// Pagina principale - Lista utenti
    /// </summary>
    [HttpGet("")]
    [HttpGet("Index")]
    public async Task<IActionResult> Index(int page = 1, int pageSize = 10, string? search = null, string? role = null)
    {
        ViewData["Title"] = "Gestione Utenti";

        var model = await GetUserListAsync(page, pageSize, search, role);
        return View(model);
    }

    /// <summary>
    /// Pagina creazione nuovo utente
    /// </summary>
    [HttpGet("Create")]
    public async Task<IActionResult> Create()
    {
        ViewData["Title"] = "Nuovo Utente";
        
        var model = new CreateUserViewModel
        {
            AvailableRoles = await GetRolesSelectListAsync(),
            EmailConfirmed = true,
            LockoutEnabled = true
        };

        return View(model);
    }

    /// <summary>
    /// Creazione nuovo utente (POST)
    /// </summary>
    [HttpPost("Create")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CreateUserViewModel model)
    {
        ViewData["Title"] = "Nuovo Utente";

        if (!ModelState.IsValid)
        {
            model.AvailableRoles = await GetRolesSelectListAsync();
            return View(model);
        }

        try
        {
            // Verifica se l'utente esiste già
            var existingUser = await _userManager.FindByEmailAsync(model.Email);
            if (existingUser != null)
            {
                ModelState.AddModelError("Email", "Un utente con questa email esiste già.");
                model.AvailableRoles = await GetRolesSelectListAsync();
                return View(model);
            }

            // Crea nuovo utente
            var newUser = new IdentityUser
            {
                UserName = model.UserName,
                Email = model.Email,
                EmailConfirmed = model.EmailConfirmed,
                LockoutEnabled = model.LockoutEnabled
            };

            var result = await _userManager.CreateAsync(newUser, model.Password);
            if (result.Succeeded)
            {
                // Assegna ruoli
                if (model.SelectedRoles.Any())
                {
                    await _userManager.AddToRolesAsync(newUser, model.SelectedRoles);
                }

                _logger.LogInformation("User {Email} created successfully by {AdminUser}", model.Email, User.Identity!.Name);
                TempData["SuccessMessage"] = $"Utente {model.Email} creato con successo.";
                return RedirectToAction("Index");
            }

            foreach (var error in result.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user {Email}", model.Email);
            ModelState.AddModelError(string.Empty, "Errore durante la creazione dell'utente.");
        }

        model.AvailableRoles = await GetRolesSelectListAsync();
        return View(model);
    }

    /// <summary>
    /// Pagina modifica utente
    /// </summary>
    [HttpGet("Edit/{id}")]
    public async Task<IActionResult> Edit(string id)
    {
        ViewData["Title"] = "Modifica Utente";

        var user = await _userManager.FindByIdAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        var roles = await _userManager.GetRolesAsync(user);
        var model = new EditUserViewModel
        {
            Id = user.Id,
            Email = user.Email!,
            UserName = user.UserName!,
            EmailConfirmed = user.EmailConfirmed,
            LockoutEnabled = user.LockoutEnabled,
            LockoutEnd = user.LockoutEnd,
            AccessFailedCount = user.AccessFailedCount,
            CurrentRoles = roles.ToList(),
            SelectedRoles = roles.ToList(),
            AvailableRoles = await GetRolesSelectListAsync()
        };

        return View(model);
    }

    /// <summary>
    /// Modifica utente (POST)
    /// </summary>
    [HttpPost("Edit/{id}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(string id, EditUserViewModel model)
    {
        ViewData["Title"] = "Modifica Utente";

        if (id != model.Id)
        {
            return NotFound();
        }

        if (!ModelState.IsValid)
        {
            model.AvailableRoles = await GetRolesSelectListAsync();
            return View(model);
        }

        try
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // Aggiorna proprietà utente
            bool hasChanges = false;
            
            if (user.Email != model.Email)
            {
                user.Email = model.Email;
                hasChanges = true;
            }
            
            if (user.UserName != model.UserName)
            {
                user.UserName = model.UserName;
                hasChanges = true;
            }
            
            if (user.EmailConfirmed != model.EmailConfirmed)
            {
                user.EmailConfirmed = model.EmailConfirmed;
                hasChanges = true;
            }
            
            if (user.LockoutEnabled != model.LockoutEnabled)
            {
                user.LockoutEnabled = model.LockoutEnabled;
                hasChanges = true;
            }

            if (hasChanges)
            {
                var updateResult = await _userManager.UpdateAsync(user);
                if (!updateResult.Succeeded)
                {
                    foreach (var error in updateResult.Errors)
                    {
                        ModelState.AddModelError(string.Empty, error.Description);
                    }
                    model.AvailableRoles = await GetRolesSelectListAsync();
                    return View(model);
                }
            }

            // Aggiorna ruoli
            var currentRoles = await _userManager.GetRolesAsync(user);
            var rolesToRemove = currentRoles.Except(model.SelectedRoles).ToList();
            var rolesToAdd = model.SelectedRoles.Except(currentRoles).ToList();

            if (rolesToRemove.Any())
            {
                await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
            }

            if (rolesToAdd.Any())
            {
                await _userManager.AddToRolesAsync(user, rolesToAdd);
            }

            _logger.LogInformation("User {Email} updated successfully by {AdminUser}", model.Email, User.Identity!.Name);
            TempData["SuccessMessage"] = $"Utente {model.Email} aggiornato con successo.";
            return RedirectToAction("Index");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {Email}", model.Email);
            ModelState.AddModelError(string.Empty, "Errore durante l'aggiornamento dell'utente.");
        }

        model.AvailableRoles = await GetRolesSelectListAsync();
        return View(model);
    }

    /// <summary>
    /// Pagina dettagli utente
    /// </summary>
    [HttpGet("Details/{id}")]
    public async Task<IActionResult> Details(string id)
    {
        ViewData["Title"] = "Dettagli Utente";

        var user = await _userManager.FindByIdAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        var roles = await _userManager.GetRolesAsync(user);
        var model = new EditUserViewModel
        {
            Id = user.Id,
            Email = user.Email!,
            UserName = user.UserName!,
            EmailConfirmed = user.EmailConfirmed,
            LockoutEnabled = user.LockoutEnabled,
            LockoutEnd = user.LockoutEnd,
            AccessFailedCount = user.AccessFailedCount,
            CurrentRoles = roles.ToList(),
            AvailableRoles = await GetRolesSelectListAsync()
        };

        return View(model);
    }

    /// <summary>
    /// Pagina gestione ruoli
    /// </summary>
    [HttpGet("Roles")]
    public async Task<IActionResult> Roles()
    {
        ViewData["Title"] = "Gestione Ruoli e Permessi";

        var model = new RoleManagementViewModel();

        // Carica ruoli
        var roles = await _roleManager.Roles.ToListAsync();
        foreach (var role in roles)
        {
            var userCount = await _userManager.GetUsersInRoleAsync(role.Name!);
            model.Roles.Add(new RoleItemViewModel
            {
                Name = role.Name!,
                Description = role.Name!,
                UserCount = userCount.Count
            });
        }

        // Carica policy (simulazione - in realtà dovresti avere un sistema per gestirle)
        model.Policies = GetPolicyItems();

        return View(model);
    }

    /// <summary>
    /// Dashboard statistiche
    /// </summary>
    [HttpGet("Statistics")]
    public async Task<IActionResult> Statistics()
    {
        ViewData["Title"] = "Statistiche Utenti";

        var model = new UserStatisticsViewModel();
        
        var allUsers = await _userManager.Users.ToListAsync();
        model.TotalUsers = allUsers.Count;
        model.ActiveUsers = allUsers.Count(u => !u.LockoutEnd.HasValue || u.LockoutEnd <= DateTimeOffset.UtcNow);
        model.LockedUsers = allUsers.Count(u => u.LockoutEnd.HasValue && u.LockoutEnd > DateTimeOffset.UtcNow);
        model.UnconfirmedEmails = allUsers.Count(u => !u.EmailConfirmed);

        // Statistiche per ruolo
        var roles = await _roleManager.Roles.ToListAsync();
        foreach (var role in roles)
        {
            var usersInRole = await _userManager.GetUsersInRoleAsync(role.Name!);
            model.UsersByRole[role.Name!] = usersInRole.Count;
        }

        return View(model);
    }

    #endregion

    #region API Actions

    /// <summary>
    /// API: Ottieni lista utenti
    /// </summary>
    [HttpGet("api/users")]
    public async Task<ActionResult<UserManagementResponse>> GetUsers(int page = 1, int pageSize = 10, string? search = null, string? role = null)
    {
        try
        {
            var model = await GetUserListAsync(page, pageSize, search, role);
            return Ok(new UserManagementResponse
            {
                Success = true,
                Data = model
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving users list");
            return StatusCode(500, new UserManagementResponse
            {
                Success = false,
                Message = "Errore durante il recupero degli utenti"
            });
        }
    }

    /// <summary>
    /// API: Blocca/Sblocca utente
    /// </summary>
    [HttpPost("api/users/{id}/toggle-lockout")]
    public async Task<ActionResult<UserManagementResponse>> ToggleLockout(string id)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound(new UserManagementResponse
                {
                    Success = false,
                    Message = "Utente non trovato"
                });
            }

            if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow)
            {
                // Sblocca utente
                await _userManager.SetLockoutEndDateAsync(user, null);
                await _userManager.ResetAccessFailedCountAsync(user);
                _logger.LogInformation("User {Email} unlocked by {AdminUser}", user.Email, User.Identity!.Name);
                
                return Ok(new UserManagementResponse
                {
                    Success = true,
                    Message = $"Utente {user.Email} sbloccato con successo"
                });
            }
            else
            {
                // Blocca utente per 24 ore
                await _userManager.SetLockoutEndDateAsync(user, DateTimeOffset.UtcNow.AddHours(24));
                _logger.LogInformation("User {Email} locked by {AdminUser}", user.Email, User.Identity!.Name);
                
                return Ok(new UserManagementResponse
                {
                    Success = true,
                    Message = $"Utente {user.Email} bloccato per 24 ore"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling user lockout for {UserId}", id);
            return StatusCode(500, new UserManagementResponse
            {
                Success = false,
                Message = "Errore durante l'operazione"
            });
        }
    }

    /// <summary>
    /// API: Elimina utente
    /// </summary>
    [HttpDelete("api/users/{id}")]
    public async Task<ActionResult<UserManagementResponse>> DeleteUser(string id)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound(new UserManagementResponse
                {
                    Success = false,
                    Message = "Utente non trovato"
                });
            }

            // Previeni l'eliminazione dell'admin principale
            if (user.Email == "<EMAIL>")
            {
                return BadRequest(new UserManagementResponse
                {
                    Success = false,
                    Message = "Non è possibile eliminare l'utente amministratore principale"
                });
            }

            var result = await _userManager.DeleteAsync(user);
            if (result.Succeeded)
            {
                _logger.LogInformation("User {Email} deleted by {AdminUser}", user.Email, User.Identity!.Name);
                return Ok(new UserManagementResponse
                {
                    Success = true,
                    Message = $"Utente {user.Email} eliminato con successo"
                });
            }

            return BadRequest(new UserManagementResponse
            {
                Success = false,
                Message = "Errore durante l'eliminazione",
                Errors = result.Errors.Select(e => e.Description).ToList()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId}", id);
            return StatusCode(500, new UserManagementResponse
            {
                Success = false,
                Message = "Errore durante l'eliminazione dell'utente"
            });
        }
    }

    /// <summary>
    /// API: Reset password utente
    /// </summary>
    [HttpPost("api/users/{id}/reset-password")]
    public async Task<ActionResult<UserManagementResponse>> ResetPassword(string id, [FromBody] AdminChangePasswordViewModel model)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new UserManagementResponse
                {
                    Success = false,
                    Message = "Dati non validi",
                    Errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()
                });
            }

            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound(new UserManagementResponse
                {
                    Success = false,
                    Message = "Utente non trovato"
                });
            }

            // Rimuovi password attuale e imposta nuova password
            await _userManager.RemovePasswordAsync(user);
            var result = await _userManager.AddPasswordAsync(user, model.NewPassword);

            if (result.Succeeded)
            {
                _logger.LogInformation("Password reset for user {Email} by {AdminUser}", user.Email, User.Identity!.Name);
                return Ok(new UserManagementResponse
                {
                    Success = true,
                    Message = $"Password per {user.Email} reimpostata con successo"
                });
            }

            return BadRequest(new UserManagementResponse
            {
                Success = false,
                Message = "Errore durante il reset della password",
                Errors = result.Errors.Select(e => e.Description).ToList()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password for user {UserId}", id);
            return StatusCode(500, new UserManagementResponse
            {
                Success = false,
                Message = "Errore durante il reset della password"
            });
        }
    }

    #endregion

    #region Private Methods

    /// <summary>
    /// Ottiene la lista degli utenti con paginazione e filtri
    /// </summary>
    private async Task<UserListViewModel> GetUserListAsync(int page, int pageSize, string? search, string? role)
    {
        var query = _userManager.Users.AsQueryable();

        // Filtro per ricerca
        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(u => u.Email!.Contains(search) || u.UserName!.Contains(search));
        }

        var totalUsers = await query.CountAsync();
        var users = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var userViewModels = new List<UserItemViewModel>();
        
        foreach (var user in users)
        {
            var roles = await _userManager.GetRolesAsync(user);
            
            // Filtro per ruolo (applicato dopo il caricamento per semplicità)
            if (!string.IsNullOrEmpty(role) && !roles.Contains(role))
            {
                continue;
            }

            userViewModels.Add(new UserItemViewModel
            {
                Id = user.Id,
                Email = user.Email!,
                UserName = user.UserName!,
                Roles = roles.ToList(),
                EmailConfirmed = user.EmailConfirmed,
                LockoutEnabled = user.LockoutEnabled,
                LockoutEnd = user.LockoutEnd,
                AccessFailedCount = user.AccessFailedCount
            });
        }

        return new UserListViewModel
        {
            Users = userViewModels,
            TotalUsers = totalUsers,
            CurrentPage = page,
            PageSize = pageSize,
            SearchTerm = search,
            SelectedRole = role,
            AvailableRoles = await GetRolesSelectListAsync()
        };
    }

    /// <summary>
    /// Ottiene la lista dei ruoli per SelectList
    /// </summary>
    private async Task<List<SelectListItem>> GetRolesSelectListAsync()
    {
        var roles = await _roleManager.Roles.ToListAsync();
        return roles.Select(r => new SelectListItem
        {
            Value = r.Name!,
            Text = $"{r.Name} - {GetRoleDescription(r.Name!)}"
        }).ToList();
    }

    /// <summary>
    /// Ottiene la descrizione del ruolo
    /// </summary>
    private static string GetRoleDescription(string role) => role switch
    {
        Ardec.Services.Web.Shared.Authorization.Roles.Admin => "Amministratore - Accesso completo",
        Ardec.Services.Web.Shared.Authorization.Roles.PowerUser => "Utente Avanzato - Modifica ed export avanzati",
        Ardec.Services.Web.Shared.Authorization.Roles.Editor => "Editor - Modifica parti/tavole assegnate",
        Ardec.Services.Web.Shared.Authorization.Roles.Viewer => "Visualizzatore - Solo lettura",
        _ => "Ruolo personalizzato"
    };

    /// <summary>
    /// Ottiene la lista delle policy per la visualizzazione
    /// </summary>
    private static List<PolicyItemViewModel> GetPolicyItems()
    {
        return new List<PolicyItemViewModel>
        {
            new() { Name = Policies.CanEditCatalog, RequiredRoles = new List<string> { Ardec.Services.Web.Shared.Authorization.Roles.Admin, Ardec.Services.Web.Shared.Authorization.Roles.PowerUser, Ardec.Services.Web.Shared.Authorization.Roles.Editor } },
            new() { Name = Policies.CanExportData, RequiredRoles = new List<string> { Ardec.Services.Web.Shared.Authorization.Roles.Admin, Ardec.Services.Web.Shared.Authorization.Roles.PowerUser } },
            new() { Name = Policies.CanManageUsers, RequiredRoles = new List<string> { Ardec.Services.Web.Shared.Authorization.Roles.Admin } },
            new() { Name = Policies.CanChangeTableStatus, RequiredRoles = new List<string> { Ardec.Services.Web.Shared.Authorization.Roles.Admin, Ardec.Services.Web.Shared.Authorization.Roles.PowerUser, Ardec.Services.Web.Shared.Authorization.Roles.Editor } },
            new() { Name = Policies.CanDeleteData, RequiredRoles = new List<string> { Ardec.Services.Web.Shared.Authorization.Roles.Admin } },
            new() { Name = Policies.CanCreateData, RequiredRoles = new List<string> { Ardec.Services.Web.Shared.Authorization.Roles.Admin, Ardec.Services.Web.Shared.Authorization.Roles.PowerUser, Ardec.Services.Web.Shared.Authorization.Roles.Editor } },
            new() { Name = Policies.ReadAccess, RequiredRoles = new List<string> { Ardec.Services.Web.Shared.Authorization.Roles.Admin, Ardec.Services.Web.Shared.Authorization.Roles.PowerUser, Ardec.Services.Web.Shared.Authorization.Roles.Editor, Ardec.Services.Web.Shared.Authorization.Roles.Viewer } },
            new() { Name = Policies.WriteAccess, RequiredRoles = new List<string> { Ardec.Services.Web.Shared.Authorization.Roles.Admin, Ardec.Services.Web.Shared.Authorization.Roles.PowerUser, Ardec.Services.Web.Shared.Authorization.Roles.Editor } },
            new() { Name = Policies.DeleteAccess, RequiredRoles = new List<string> { Ardec.Services.Web.Shared.Authorization.Roles.Admin } }
        };
    }

    #endregion
}
