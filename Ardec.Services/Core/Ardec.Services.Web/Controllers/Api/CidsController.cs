using Ardec.Services.Web.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Ardec.Services.Web.Services.CID;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// CIDs REST API Controller - Change Implementation Document management with RBAC authorization
/// Handles TB_CID operations for change implementation documents within MIL-STD-1388 catalogs
/// Each CID represents a configuration/variant requiring different table versions
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CidsController : ControllerBase
{
    private readonly ICIDService _cidService;
    private readonly ILogger<CidsController> _logger;

    public CidsController(ICIDService cidService, ILogger<CidsController> logger)
    {
        _cidService = cidService;
        _logger = logger;
    }

    /// <summary>
    /// Get all CID entries (Admin view - All authenticated users)
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<TB_CID>>> GetAll()
    {
        try
        {
            var cids = await _cidService.GetAllCidsAsync();
            _logger.LogInformation("Retrieved {Count} CID entries for user {User}", cids.Count(), User.Identity?.Name);
            return Ok(cids);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all CID entries");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get all CIDs for a specific catalog (TER) - All authenticated users
    /// </summary>
    [HttpGet("catalog/{ter}")]
    public async Task<ActionResult<IEnumerable<TB_CID>>> GetCidsByTer(string ter)
    {
        try
        {
            var cids = await _cidService.GetCidsByTerAsync(ter);
            _logger.LogInformation("Retrieved {Count} CIDs for TER {TER} for user {User}", 
                cids.Count(), ter, User.Identity?.Name);
            return Ok(cids);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving CIDs for TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get specific CID by composite key TER + CID - All authenticated users
    /// </summary>
    [HttpGet("{ter}/{cid}")]
    public async Task<ActionResult<TB_CID>> GetCid(string ter, string cid)
    {
        try
        {
            var cidEntry = await _cidService.GetCidAsync(ter, cid);
            if (cidEntry == null)
            {
                _logger.LogWarning("CID with TER '{TER}' and CID '{CID}' not found", ter, cid);
                return NotFound(new { error = "CID not found", ter, cid });
            }

            _logger.LogInformation("Retrieved CID: TER {TER}, CID {CID} for user {User}", ter, cid, User.Identity?.Name);
            return Ok(cidEntry);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving CID: TER {TER}, CID {CID}", ter, cid);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Check if CID exists for specific TER - All authenticated users
    /// </summary>
    [HttpHead("{ter}/{cid}")]
    public async Task<ActionResult> Exists(string ter, string cid)
    {
        try
        {
            var exists = await _cidService.ExistsAsync(ter, cid);
            if (exists)
            {
                return Ok();
            }
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if CID exists: TER {TER}, CID {CID}", ter, cid);
            return StatusCode(500);
        }
    }

    /// <summary>
    /// Create new CID entry - Add component identification to catalog (Admin, PowerUser only)
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,PowerUser")]
    public async Task<ActionResult<TB_CID>> Create([FromBody] TB_CID cidEntry)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var createdCid = await _cidService.CreateCidAsync(cidEntry);
            _logger.LogInformation("Created CID: TER {TER}, CID {CID} by user {User}", 
                createdCid.TER, createdCid.CID, User.Identity?.Name);
            
            return CreatedAtAction(nameof(GetCid), 
                new { ter = createdCid.TER, cid = createdCid.CID }, 
                createdCid);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating CID by user {User}", User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Update CID entry (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPut("{ter}/{cid}")]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_CID>> Update(string ter, string cid, [FromBody] TB_CID cidEntry)
    {
        try
        {
            if (ter != cidEntry.TER || cid != cidEntry.CID)
                return BadRequest(new { error = "TER/CID mismatch between route and body" });

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var updatedCid = await _cidService.UpdateCidAsync(cidEntry);
            _logger.LogInformation("Updated CID: TER {TER}, CID {CID} by user {User}", ter, cid, User.Identity?.Name);
            
            return Ok(updatedCid);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating CID: TER {TER}, CID {CID} by user {User}", ter, cid, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Delete CID entry - Remove component identification from catalog (Admin only)
    /// </summary>
    [HttpDelete("{ter}/{cid}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> Delete(string ter, string cid)
    {
        try
        {
            var deleted = await _cidService.DeleteCidAsync(ter, cid);
            if (!deleted)
            {
                return NotFound(new { error = "CID not found", ter, cid });
            }

            _logger.LogInformation("Deleted CID: TER {TER}, CID {CID} by user {User}", ter, cid, User.Identity?.Name);
            return NoContent();
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting CID: TER {TER}, CID {CID} by user {User}", ter, cid, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Search CIDs by text (title, applicability, etc.) - All authenticated users
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<TB_CID>>> Search([FromQuery] string q)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(q))
                return BadRequest(new { error = "Search query 'q' is required" });

            var results = await _cidService.SearchCidsAsync(q);
            _logger.LogInformation("Search for '{Query}' returned {Count} CID results for user {User}", 
                q, results.Count(), User.Identity?.Name);
            
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching CIDs with query: {Query} by user {User}", q, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get CIDs with multilingual support for specific catalog and language - All authenticated users
    /// </summary>
    [HttpGet("catalog/{ter}/language/{language}")]
    public async Task<ActionResult<IEnumerable<TB_CID>>> GetCidsWithLanguage(string ter, string language)
    {
        try
        {
            var cids = await _cidService.GetCidsWithLanguageAsync(ter, language);
            _logger.LogInformation("Retrieved {Count} CIDs for TER {TER}, Language {Language} for user {User}", 
                cids.Count(), ter, language, User.Identity?.Name);
            
            return Ok(cids);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving CIDs for TER: {TER}, Language: {Language}", ter, language);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get CID statistics for a catalog - All authenticated users
    /// </summary>
    [HttpGet("catalog/{ter}/statistics")]
    public async Task<ActionResult<object>> GetCidStatistics(string ter)
    {
        try
        {
            var stats = await _cidService.GetCidStatisticsAsync(ter);
            _logger.LogInformation("Generated CID statistics for TER {TER} for user {User}", ter, User.Identity?.Name);
            return Ok(stats);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CID statistics for TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get CIDs by date range for a catalog - All authenticated users
    /// </summary>
    [HttpGet("catalog/{ter}/daterange")]
    public async Task<ActionResult<IEnumerable<TB_CID>>> GetCidsByDateRange(
        string ter,
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate)
    {
        try
        {
            var cids = await _cidService.GetCidsByDateRangeAsync(ter, startDate, endDate);
            _logger.LogInformation("Retrieved {Count} CIDs for TER {TER} between {StartDate} and {EndDate} for user {User}", 
                cids.Count(), ter, startDate, endDate, User.Identity?.Name);
            
            return Ok(cids);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving CIDs by date range for TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Bulk update CIDs for a catalog (Admin, PowerUser only)
    /// </summary>
    [HttpPut("catalog/{ter}/bulk")]
    [Authorize(Roles = "Admin,PowerUser")]
    public async Task<ActionResult<object>> BulkUpdate(string ter, [FromBody] IEnumerable<TB_CID> cids)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var updatedCount = await _cidService.BulkUpdateCidsAsync(ter, cids);
            _logger.LogInformation("Bulk updated {Count} CIDs for TER {TER} by user {User}", 
                updatedCount, ter, User.Identity?.Name);
            
            return Ok(new { message = "Bulk update completed", updatedCount, ter });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating CIDs for TER {TER} by user {User}", ter, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }
}
