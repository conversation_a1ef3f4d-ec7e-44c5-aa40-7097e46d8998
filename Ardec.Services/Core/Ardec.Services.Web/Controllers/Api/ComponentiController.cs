using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Ardec.Services.Web.Shared.Authorization;
using Ardec.Services.Web.Services.Componenti;
using Ardec.Services.Web.DTOs.Componenti;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// Controller per la gestione dei componenti (TB_Componenti)
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ComponentiController : ControllerBase
{
    private readonly IComponentiService _componentiService;
    private readonly ILogger<ComponentiController> _logger;

    public ComponentiController(
        IComponentiService componentiService,
        ILogger<ComponentiController> logger)
    {
        _componentiService = componentiService;
        _logger = logger;
    }

    /// <summary>
    /// Ottiene tutti i componenti con paginazione
    /// </summary>
    [HttpGet]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<ComponentiPagedResult>> GetAll(
        [FromQuery] int skip = 0,
        [FromQuery] int take = 20,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetAll Componenti con skip: {Skip}, take: {Take}", skip, take);

        if (take > 100)
        {
            take = 100; // Limite massimo
        }

        var result = await _componentiService.GetAllAsync(skip, take, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Ottiene un componente specifico per PART
    /// </summary>
    [HttpGet("{part}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<ComponentiResponse>> GetByPart(
        string part,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetByPart per PART: {PART}", part);

        var result = await _componentiService.GetByPartAsync(part, cancellationToken);
        
        if (result == null)
        {
            return NotFound($"Componente con PART '{part}' non trovato");
        }

        return Ok(result);
    }

    /// <summary>
    /// Cerca componenti con filtri avanzati
    /// </summary>
    [HttpPost("search")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<ComponentiPagedResult>> Search(
        [FromBody] ComponentiSearchRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta Search Componenti con filtri: {@Request}", request);

        if (request.Take > 100)
        {
            request = request with { Take = 100 }; // Limite massimo
        }

        var result = await _componentiService.SearchAsync(request, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Ottiene componenti per codice fornitore
    /// </summary>
    [HttpGet("by-codf/{codF}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<ComponentiResponse>>> GetByCodF(
        string codF,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetByCodF per CodF: {CodF}", codF);

        var result = await _componentiService.GetByCodFAsync(codF, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Ottiene componenti per origine
    /// </summary>
    [HttpGet("by-origine/{origine}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<ComponentiResponse>>> GetByOrigine(
        string origine,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetByOrigine per Origine: {Origine}", origine);

        var result = await _componentiService.GetByOrigineAsync(origine, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Ottiene componenti per tabella
    /// </summary>
    [HttpGet("by-tabella/{tabella}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<ComponentiResponse>>> GetByTabella(
        string tabella,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetByTabella per Tabella: {Tabella}", tabella);

        var result = await _componentiService.GetByTabellaAsync(tabella, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Ottiene componenti per TONumber
    /// </summary>
    [HttpGet("by-tonumber/{toNumber}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<ComponentiResponse>>> GetByTONumber(
        string toNumber,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetByTONumber per TONumber: {TONumber}", toNumber);

        var result = await _componentiService.GetByTONumberAsync(toNumber, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Cerca componenti per descrizione in una lingua specifica
    /// </summary>
    [HttpGet("search-description")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<ComponentiResponse>>> SearchByDescription(
        [FromQuery] string searchTerm,
        [FromQuery] string language = "IT",
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta SearchByDescription per termine: {SearchTerm} in lingua: {Language}", searchTerm, language);

        var result = await _componentiService.SearchByDescriptionAsync(searchTerm, language, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Cerca componenti per note in una lingua specifica
    /// </summary>
    [HttpGet("search-note")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<ComponentiResponse>>> SearchByNote(
        [FromQuery] string searchTerm,
        [FromQuery] string language = "IT",
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta SearchByNote per termine: {SearchTerm} in lingua: {Language}", searchTerm, language);

        var result = await _componentiService.SearchByNoteAsync(searchTerm, language, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Ottiene la descrizione localizzata per un componente
    /// </summary>
    [HttpGet("{part}/description")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<string>> GetLocalizedDescription(
        string part,
        [FromQuery] string language = "IT",
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetLocalizedDescription per PART: {PART} in lingua: {Language}", part, language);

        var result = await _componentiService.GetLocalizedDescriptionAsync(part, language, cancellationToken);
        
        if (result == null)
        {
            return NotFound($"Componente con PART '{part}' non trovato o senza descrizione");
        }

        return Ok(result);
    }

    /// <summary>
    /// Ottiene la nota localizzata per un componente
    /// </summary>
    [HttpGet("{part}/note")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<string>> GetLocalizedNote(
        string part,
        [FromQuery] string language = "IT",
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetLocalizedNote per PART: {PART} in lingua: {Language}", part, language);

        var result = await _componentiService.GetLocalizedNoteAsync(part, language, cancellationToken);
        
        if (result == null)
        {
            return NotFound($"Componente con PART '{part}' non trovato o senza nota");
        }

        return Ok(result);
    }

    /// <summary>
    /// Ottiene componenti senza descrizione
    /// </summary>
    [HttpGet("without-description")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<ComponentiResponse>>> GetWithoutDescription(
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetWithoutDescription");

        var result = await _componentiService.GetWithoutDescriptionAsync(cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Ottiene componenti duplicati
    /// </summary>
    [HttpGet("duplicates")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<List<ComponentiResponse>>> GetDuplicates(
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta GetDuplicates");

        var result = await _componentiService.GetDuplicatesAsync(cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Crea un nuovo componente
    /// </summary>
    [HttpPost]
    [Authorize(Policy = Policies.WriteAccess)]
    public async Task<ActionResult<ComponentiResponse>> Create(
        [FromBody] CreateComponentiRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta creazione Componente: {@Request}", request);

        try
        {
            var result = await _componentiService.CreateAsync(request, cancellationToken);
            return CreatedAtAction(
                nameof(GetByPart),
                new { part = result.PART },
                result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Errore durante la creazione del Componente");
            return Conflict(ex.Message);
        }
    }

    /// <summary>
    /// Aggiorna un componente esistente
    /// </summary>
    [HttpPut("{part}")]
    [Authorize(Policy = Policies.WriteAccess)]
    public async Task<ActionResult<ComponentiResponse>> Update(
        string part,
        [FromBody] UpdateComponentiRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta aggiornamento Componente PART: {PART}", part);

        var result = await _componentiService.UpdateAsync(part, request, cancellationToken);
        
        if (result == null)
        {
            return NotFound($"Componente con PART '{part}' non trovato");
        }

        return Ok(result);
    }

    /// <summary>
    /// Elimina un componente
    /// </summary>
    [HttpDelete("{part}")]
    [Authorize(Policy = Policies.DeleteAccess)]
    public async Task<ActionResult> Delete(
        string part,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta eliminazione Componente PART: {PART}", part);

        var success = await _componentiService.DeleteAsync(part, cancellationToken);
        
        if (!success)
        {
            return NotFound($"Componente con PART '{part}' non trovato");
        }

        return NoContent();
    }

    /// <summary>
    /// Verifica se esiste un componente
    /// </summary>
    [HttpHead("{part}")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult> Exists(
        string part,
        CancellationToken cancellationToken = default)
    {
        var exists = await _componentiService.ExistsAsync(part, cancellationToken);
        return exists ? Ok() : NotFound();
    }

    /// <summary>
    /// Ottiene statistiche dei componenti
    /// </summary>
    [HttpGet("stats")]
    [Authorize(Policy = Policies.ReadAccess)]
    public async Task<ActionResult<ComponentiStatsResponse>> GetStats(
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Richiesta statistiche Componenti");

        var result = await _componentiService.GetStatsAsync(cancellationToken);
        return Ok(result);
    }
}
