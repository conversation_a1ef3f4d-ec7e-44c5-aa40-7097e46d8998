using Ardec.Services.Web.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Ardec.Services.Web.Services.Catalogs;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// Catalogs REST API Controller with RBAC authorization
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CatalogsController : ControllerBase
{
    private readonly ICatalogService _catalogService;
    private readonly ILogger<CatalogsController> _logger;

    public CatalogsController(ICatalogService catalogService, ILogger<CatalogsController> logger)
    {
        _catalogService = catalogService;
        _logger = logger;
    }

    /// <summary>
    /// Get all catalogs (All authenticated users)
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<TB_Cataloghi>>> GetAll()
    {
        try
        {
            var catalogs = await _catalogService.GetAllAsync();
            _logger.LogInformation("Retrieved {Count} catalogs for user {User}", catalogs.Count(), User.Identity?.Name);
            return Ok(catalogs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all catalogs");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get catalog by TER (All authenticated users)
    /// </summary>
    [HttpGet("{ter}")]
    public async Task<ActionResult<TB_Cataloghi>> GetByTer(string ter)
    {
        try
        {
            var catalog = await _catalogService.GetByTerAsync(ter);
            if (catalog == null)
            {
                _logger.LogWarning("Catalog with TER '{TER}' not found", ter);
                return NotFound(new { error = "Catalog not found", ter });
            }

            _logger.LogInformation("Retrieved catalog with TER: {TER} for user {User}", ter, User.Identity?.Name);
            return Ok(catalog);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving catalog with TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Create new catalog (Admin, PowerUser only)
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,PowerUser")]
    public async Task<ActionResult<TB_Cataloghi>> Create([FromBody] TB_Cataloghi catalog)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var createdCatalog = await _catalogService.CreateAsync(catalog);
            _logger.LogInformation("Created catalog with TER: {TER} by user {User}", createdCatalog.TER, User.Identity?.Name);
            
            return CreatedAtAction(nameof(GetByTer), new { ter = createdCatalog.TER }, createdCatalog);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating catalog by user {User}", User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Update catalog (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPut("{ter}")]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_Cataloghi>> Update(string ter, [FromBody] TB_Cataloghi catalog)
    {
        try
        {
            if (ter != catalog.TER)
                return BadRequest(new { error = "TER mismatch between route and body" });

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var updatedCatalog = await _catalogService.UpdateAsync(catalog);
            _logger.LogInformation("Updated catalog with TER: {TER} by user {User}", ter, User.Identity?.Name);
            
            return Ok(updatedCatalog);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating catalog with TER: {TER} by user {User}", ter, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Delete catalog (Admin only)
    /// </summary>
    [HttpDelete("{ter}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> Delete(string ter)
    {
        try
        {
            var deleted = await _catalogService.DeleteAsync(ter);
            if (!deleted)
            {
                return NotFound(new { error = "Catalog not found", ter });
            }

            _logger.LogInformation("Deleted catalog with TER: {TER} by user {User}", ter, User.Identity?.Name);
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting catalog with TER: {TER} by user {User}", ter, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Search catalogs (All authenticated users)
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<TB_Cataloghi>>> Search([FromQuery] string q)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(q))
                return BadRequest(new { error = "Search query 'q' is required" });

            var results = await _catalogService.SearchAsync(q);
            _logger.LogInformation("Search for '{Query}' returned {Count} results for user {User}", 
                q, results.Count(), User.Identity?.Name);
            
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching catalogs with query: {Query} by user {User}", q, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Check if catalog exists (All authenticated users)
    /// </summary>
    [HttpHead("{ter}")]
    public async Task<ActionResult> Exists(string ter)
    {
        try
        {
            var exists = await _catalogService.ExistsAsync(ter);
            if (exists)
            {
                return Ok();
            }
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if catalog exists with TER: {TER}", ter);
            return StatusCode(500);
        }
    }

    /// <summary>
    /// Get catalog statistics (All authenticated users)
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<object>> GetStatistics()
    {
        try
        {
            var catalogs = await _catalogService.GetAllAsync();
            var stats = new
            {
                totalCatalogs = catalogs.Count(),
                catalogsWithMultiLanguage = catalogs.Count(c => 
                    !string.IsNullOrEmpty(c.TitoloEN) || 
                    !string.IsNullOrEmpty(c.TitoloES) || 
                    !string.IsNullOrEmpty(c.TitoloFR)),
                catalogsWithS1000D = catalogs.Count(c => c.S1000D == true),
                catalogsWithMT = catalogs.Count(c => c.IsMT == true),
                catalogsWithUSA = catalogs.Count(c => c.IsUSA == true)
            };

            _logger.LogInformation("Generated catalog statistics for user {User}", User.Identity?.Name);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating catalog statistics");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }
}