using Ardec.Services.Web.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Ardec.Services.Web.Controllers.Api;

/// <summary>
/// TablesController partial class - TB_DettagliTavole Endpoints
/// Handles all table details operations with versioning support
/// </summary>
public partial class TablesController
{
    #region TB_DettagliTavole Endpoints

    /// <summary>
    /// Get all table details (All authenticated users)
    /// </summary>
    [HttpGet("details")]
    public async Task<ActionResult<IEnumerable<TB_DettagliTavole>>> GetAllTableDetails([FromQuery] bool lazy = false)
    {
        try
        {
            var details = lazy ? await _tableService.GetTableDetailsLazyAsync() : await _tableService.GetAllTableDetailsAsync();
            _logger.LogInformation("Retrieved {Count} table details (lazy: {Lazy}) for user {User}", 
                details.Count(), lazy, User.Identity?.Name);
            return Ok(details);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all table details");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get table details by tavola (All authenticated users)
    /// </summary>
    [HttpGet("details/{tavola}")]
    public async Task<ActionResult<IEnumerable<TB_DettagliTavole>>> GetTableDetailsByTavola(string tavola)
    {
        try
        {
            var details = await _tableService.GetTableDetailsByTavolaAsync(tavola);
            _logger.LogInformation("Retrieved {Count} table details for Tavola: {Tavola} for user {User}", 
                details.Count(), tavola, User.Identity?.Name);
            return Ok(details);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving table details for Tavola: {Tavola}", tavola);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get specific table details by tavola and version (All authenticated users)
    /// </summary>
    [HttpGet("details/{tavola}/{versione}")]
    public async Task<ActionResult<TB_DettagliTavole>> GetTableDetails(string tavola, string versione)
    {
        try
        {
            var details = await _tableService.GetTableDetailsAsync(tavola, versione);
            if (details == null)
            {
                return NotFound(new { error = "Table details not found", tavola, versione });
            }

            _logger.LogInformation("Retrieved table details for Tavola: {Tavola} Version: {Versione} for user {User}", 
                tavola, versione, User.Identity?.Name);
            return Ok(details);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving table details for Tavola: {Tavola} Version: {Versione}", tavola, versione);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get table details by tavola and version list (All authenticated users)
    /// </summary>
    [HttpGet("details/{tavola}/version/{versione}")]
    public async Task<ActionResult<IEnumerable<TB_DettagliTavole>>> GetTableDetailsByTavolaAndVersion(string tavola, string versione)
    {
        try
        {
            var details = await _tableService.GetTableDetailsByTavolaAndVersionAsync(tavola, versione);
            _logger.LogInformation("Retrieved {Count} table details for Tavola: {Tavola} Version: {Versione} for user {User}", 
                details.Count(), tavola, versione, User.Identity?.Name);
            return Ok(details);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving table details by tavola and version: {Tavola}, {Versione}", tavola, versione);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get last table details by tavola (All authenticated users)
    /// </summary>
    [HttpGet("details/{tavola}/latest")]
    public async Task<ActionResult<IEnumerable<TB_DettagliTavole>>> GetLastTableDetailsByTavola(string tavola)
    {
        try
        {
            var details = await _tableService.GetLastTableDetailsByTavolaAsync(tavola);
            _logger.LogInformation("Retrieved {Count} latest table details for Tavola: {Tavola} for user {User}", 
                details.Count(), tavola, User.Identity?.Name);
            return Ok(details);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving latest table details for Tavola: {Tavola}", tavola);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get table details by TER (All authenticated users)
    /// </summary>
    [HttpGet("details/by-ter/{ter}")]
    public async Task<ActionResult<IEnumerable<TB_DettagliTavole>>> GetTableDetailsByTer(string ter, [FromQuery] bool orderByTavola = true)
    {
        try
        {
            var details = await _tableService.GetAllTableDetailsByTerAsync(ter, orderByTavola);
            _logger.LogInformation("Retrieved {Count} table details for TER: {TER} for user {User}", 
                details.Count(), ter, User.Identity?.Name);
            return Ok(details);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving table details by TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get last table details by TER (All authenticated users) - Latest version for each table
    /// </summary>
    [HttpGet("details/by-ter/{ter}/latest")]
    public async Task<ActionResult<IEnumerable<TB_DettagliTavole>>> GetLastTableDetailsByTer(string ter, [FromQuery] bool orderByTavola = true)
    {
        try
        {
            var details = await _tableService.GetLastTableDetailsByTerAsync(ter, orderByTavola);
            _logger.LogInformation("Retrieved {Count} latest table details for TER: {TER} for user {User}", 
                details.Count(), ter, User.Identity?.Name);
            return Ok(details);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving latest table details by TER: {TER}", ter);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Create new table details (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPost("details")]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_DettagliTavole>> CreateTableDetails([FromBody] TB_DettagliTavole details)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var created = await _tableService.CreateTableDetailsAsync(details);
            _logger.LogInformation("Created table details for Tavola: {Tavola} Version: {Versione} by user {User}", 
                created.Tavola, created.Versione, User.Identity?.Name);
            
            return CreatedAtAction(nameof(GetTableDetails), 
                new { tavola = created.Tavola, versione = created.Versione }, created);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating table details by user {User}", User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Update table details (Admin, PowerUser, Editor)
    /// </summary>
    [HttpPut("details/{tavola}/{versione}")]
    [Authorize(Roles = "Admin,PowerUser,Editor")]
    public async Task<ActionResult<TB_DettagliTavole>> UpdateTableDetails(string tavola, string versione, [FromBody] TB_DettagliTavole details)
    {
        try
        {
            if (tavola != details.Tavola || versione != details.Versione)
                return BadRequest(new { error = "Route parameters don't match body values" });

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var updated = await _tableService.UpdateTableDetailsAsync(details);
            _logger.LogInformation("Updated table details for Tavola: {Tavola} Version: {Versione} by user {User}", 
                tavola, versione, User.Identity?.Name);
            
            return Ok(updated);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating table details for Tavola: {Tavola} Version: {Versione} by user {User}", 
                tavola, versione, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Delete table details (Admin only)
    /// </summary>
    [HttpDelete("details/{tavola}/{versione}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteTableDetails(string tavola, string versione)
    {
        try
        {
            var deleted = await _tableService.DeleteTableDetailsAsync(tavola, versione);
            if (!deleted)
            {
                return NotFound(new { error = "Table details not found", tavola, versione });
            }

            _logger.LogInformation("Deleted table details for Tavola: {Tavola} Version: {Versione} by user {User}", 
                tavola, versione, User.Identity?.Name);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting table details for Tavola: {Tavola} Version: {Versione} by user {User}", 
                tavola, versione, User.Identity?.Name);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Check if table details exist (All authenticated users)
    /// </summary>
    [HttpHead("details/{tavola}/{versione}")]
    public async Task<ActionResult> TableDetailsExists(string tavola, string versione)
    {
        try
        {
            var exists = await _tableService.TableDetailsExistsAsync(tavola, versione);
            return exists ? Ok() : NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if table details exist for Tavola: {Tavola} Version: {Versione}", tavola, versione);
            return StatusCode(500);
        }
    }

    #endregion
}
