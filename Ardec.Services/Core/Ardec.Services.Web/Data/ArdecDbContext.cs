using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Ardec.Services.Web.Data.Models;

namespace Ardec.Services.Web.Data;

/// <summary>
/// DbContext that mirrors the existing Framework database structure
/// </summary>
public class ArdecDbContext : IdentityDbContext
{
    public ArdecDbContext(DbContextOptions<ArdecDbContext> options) : base(options)
    {
    }

    // Main entities - Using exact Framework model names
    public DbSet<TB_Cataloghi> TB_Cataloghi { get; set; }
    public DbSet<TB_Tavole> TB_Tavole { get; set; }
    public DbSet<TB_Parti> TB_Parti { get; set; }
    public DbSet<TB_CataloghiRVT> TB_CataloghiRVT { get; set; }
    public DbSet<TB_Composizione> TB_Composizione { get; set; }
    
    // Extended entities - Aligned with Framework
    public DbSet<TB_RVT> TB_RVT { get; set; }
    public DbSet<TB_CID> TB_CID { get; set; }
    public DbSet<TB_Componenti> TB_Componenti { get; set; }
    public DbSet<TB_Subfornitori> TB_Subfornitori { get; set; }
    public DbSet<TB_DettagliTavole> TB_DettagliTavole { get; set; }
    public DbSet<TB_DescrizioniCustom> TB_DescrizioniCustom { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure composite keys (matches Framework exactly)
        modelBuilder.Entity<TB_Parti>()
            .HasKey(p => new { p.Tavola, p.PART, p.ITEM, p.Versione });

        modelBuilder.Entity<TB_CataloghiRVT>()
            .HasKey(cr => new { cr.TER, cr.RVT });

        modelBuilder.Entity<TB_Composizione>()
            .HasKey(c => new { c.TER, c.Tavola });

        modelBuilder.Entity<TB_RVT>()
            .HasKey(r => new { r.TER, r.RVT });

        modelBuilder.Entity<TB_CID>()
            .HasKey(c => new { c.TER, c.CID });

        modelBuilder.Entity<TB_DettagliTavole>()
            .HasKey(dt => new { dt.Tavola, dt.Versione });

        modelBuilder.Entity<TB_DescrizioniCustom>()
            .HasKey(dc => new { dc.PART, dc.TER });

        // Index optimizations (basic ones for performance)
        modelBuilder.Entity<TB_Cataloghi>()
            .HasIndex(c => c.TER);

        modelBuilder.Entity<TB_Parti>()
            .HasIndex(p => p.CSNREF);

        modelBuilder.Entity<TB_Parti>()
            .HasIndex(p => p.LCN);

        // TODO: Add relationships when navigation properties are needed
    }
}