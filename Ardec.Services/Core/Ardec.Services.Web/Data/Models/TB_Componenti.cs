using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ardec.Services.Web.Data.Models;

/// <summary>
/// Modello TB_Componenti allineato ESATTAMENTE al Framework
/// PRIMARY KEY: PART
/// Gestisce i componenti delle parti con descrizioni multilingue
/// </summary>
[Table("TB_Componenti")]
public partial class TB_Componenti
{

    [Key]
    [StringLength(255)]
    public string PART { get; set; } = string.Empty;

    // Descrizioni multilingue - ESATTE dal Framework
    public string? DescrIT { get; set; }
    public string? DescrES { get; set; }
    public string? DescrEN { get; set; }
    public string? DescrPORT { get; set; }
    public string? DescrFR { get; set; }
    public string? DescrTED { get; set; }
    public string? DescrUSA { get; set; }

    // Note multilingue - ESATTE dal Framework
    public string? NotaIT { get; set; }
    public string? NotaES { get; set; }
    public string? NotaEN { get; set; }
    public string? NotaPORT { get; set; }
    public string? NotaFR { get; set; }
    public string? NotaTED { get; set; }
    public string? NotaUSA { get; set; }

    // Codici identificativi - ESATTI dal Framework
    [StringLength(255)]
    public string? NUC { get; set; }

    [StringLength(255)]
    public string? CodF { get; set; }

    [StringLength(255)]
    public string? PARTF { get; set; }

    [StringLength(255)]
    public string? Tabella { get; set; }

    [StringLength(255)]
    public string? Origine { get; set; }

    [StringLength(255)]
    public string? TONumber { get; set; }

    // Navigation properties removed - use queries instead
}
