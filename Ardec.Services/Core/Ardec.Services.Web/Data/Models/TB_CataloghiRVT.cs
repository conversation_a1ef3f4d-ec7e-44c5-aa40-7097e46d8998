using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ardec.Services.Web.Data.Models;

/// <summary>
/// TB_CataloghiRVT - Relazione Cataloghi-RVT ESATTA dal Framework
/// </summary>
[Table("TB_CataloghiRVT")]
public partial class TB_CataloghiRVT
{
    [StringLength(155)]
    public string TER { get; set; } = string.Empty;

    [StringLength(255)]
    public string RVT { get; set; } = string.Empty;

    [StringLength(255)]
    public string? Applicabilità { get; set; }

    public string? ApplicabilitàEN { get; set; }
    public string? ApplicabilitàES { get; set; }
    public string? ApplicabilitàPT { get; set; }
    public string? ApplicabilitàFR { get; set; }
    public string? ApplicabilitàUSA { get; set; }

    // Navigation properties
    public virtual TB_Cataloghi? TB_Cataloghi { get; set; }
    // public virtual TB_RVT? TB_RVT { get; set; } // TODO: quando servirà
}