using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ardec.Services.Web.Data.Models;

/// <summary>
/// Modello TB_DescrizioniCustom allineato ESATTAMENTE al Framework Model
/// PRIMARY KEY: PART + TER
/// Gestisce descrizioni personalizzate per parti specifiche per catalogo
/// </summary>
[Table("TB_DescrizioniCustom")]
public partial class TB_DescrizioniCustom
{
    [Key]
    [Column(Order = 0)]
    [StringLength(255)]
    public string PART { get; set; } = string.Empty;

    [Key]
    [Column(Order = 1)]
    [StringLength(155)]
    public string TER { get; set; } = string.Empty;

    public string? DescrIT { get; set; }
    public string? DescrES { get; set; }
    public string? DescrFR { get; set; }
    public string? DescrEN { get; set; }
    public string? DescrPORT { get; set; }
    public string? DescrTED { get; set; }
    public string? DescrUSA { get; set; }
}
