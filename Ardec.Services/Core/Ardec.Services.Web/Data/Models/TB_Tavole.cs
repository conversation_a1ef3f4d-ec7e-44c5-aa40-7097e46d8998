using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ardec.Services.Web.Data.Models;

/// <summary>
/// Modello TB_Tavole allineato ESATTAMENTE al Framework parti_model_versioning
/// PRIMARY KEY: Tavola
/// Aggiornato con tutti i campi del Framework per compatibilità completa
/// </summary>
[Table("TB_Tavole")]
public partial class TB_Tavole
{
    [Key]
    [StringLength(155)]
    public string Tavola { get; set; } = string.Empty;

    [StringLength(155)]
    public string? TER { get; set; }

    [StringLength(255)]
    public string? CodiceTecnico { get; set; }

    [StringLength(255)]
    public string? Versione { get; set; }

    [StringLength(255)]
    public string? Stato { get; set; }

    [StringLength(255)]
    public string? Data { get; set; }

    public bool? NonForniti { get; set; }

    // Descrizioni multilingue - ESATTE dal Framework
    public string? Descrizione1IT { get; set; }
    public string? Descrizione2IT { get; set; }
    public string? Descrizione3IT { get; set; }
    public string? Descrizione1ES { get; set; }
    public string? Descrizione2ES { get; set; }
    public string? Descrizione3ES { get; set; }
    public string? Descrizione1EN { get; set; }
    public string? Descrizione2EN { get; set; }
    public string? Descrizione3EN { get; set; }
    public string? Descrizione1PT { get; set; }
    public string? Descrizione2PT { get; set; }
    public string? Descrizione3PT { get; set; }
    public string? Descrizione1FR { get; set; }
    public string? Descrizione2FR { get; set; }
    public string? Descrizione3FR { get; set; }
    public string? Descrizione1TED { get; set; }
    public string? Descrizione2TED { get; set; }
    public string? Descrizione3TED { get; set; }
    public string? Descrizione1USA { get; set; }
    public string? Descrizione2USA { get; set; }
    public string? Descrizione3USA { get; set; }

    // Gruppi multilingue - ESATTI dal Framework
    public string? GruppoITA { get; set; }
    public string? GruppoENG { get; set; }
    public string? GruppoFRA { get; set; }
    public string? GruppoPOR { get; set; }
    public string? GruppoESP { get; set; }
    public string? GruppoTED { get; set; }
    public string? GruppoUSA { get; set; }

    [StringLength(255)]
    public string? Logo { get; set; }

    [StringLength(255)]
    public string? Figura { get; set; }

    public string? Note { get; set; }

    // Modifiche
    public bool ModificaTavola { get; set; }
    [StringLength(255)]
    public string? DataModificaTavola { get; set; }

    public bool ModificaTesto { get; set; }
    [StringLength(255)]
    public string? DataModificaTesto { get; set; }

    // S1000D Fields
    [StringLength(255)]
    public string? DMC { get; set; }

    [StringLength(255)]
    public string? SBC { get; set; }

    [StringLength(255)]
    public string? SNS { get; set; }

    [StringLength(255)]
    public string? TECHNAME { get; set; }

    [StringLength(255)]
    public string? INFONAME { get; set; }

    [Column("ICN TITLE")]
    public string? ICN_TITLE { get; set; }

    [Column("ICN Tavola")]
    public string? ICN_Tavola { get; set; }

    [Column("ICN Logo")]
    public string? ICN_Logo { get; set; }

    // CED Fields
    public double? nTavola { get; set; }
    public double? CEDNPagina { get; set; }
    [StringLength(255)]
    public string? CEDIGNPagina { get; set; }
}
