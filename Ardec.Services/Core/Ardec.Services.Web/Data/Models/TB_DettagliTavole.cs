using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ardec.Services.Web.Data.Models;

/// <summary>
/// Modello TB_DettagliTavole allineato ESATTAMENTE al Framework
/// PRIMARY KEY: Tavola + Versione
/// Gestisce i dettagli delle tavole con versioning
/// </summary>
[Table("TB_DettagliTavole")]
public partial class TB_DettagliTavole
{
    [StringLength(255)]
    public string? CodiceTecnico { get; set; }

    [Key]
    [Column(Order = 0)]
    [StringLength(155)]
    public string Tavola { get; set; } = string.Empty;

    [Key]
    [Column(Order = 1)]
    [StringLength(2)]
    public string Versione { get; set; } = string.Empty;

    public DateTime? Data { get; set; }

    public string? Stato { get; set; }

    [StringLength(255)]
    public string? NonForniti { get; set; }

    // Descrizioni multilingue - ESATTE dal Framework
    [StringLength(255)]
    public string? Descrizione1IT { get; set; }

    [StringLength(255)]
    public string? Descrizione2IT { get; set; }

    [StringLength(255)]
    public string? Descrizione3IT { get; set; }

    [StringLength(255)]
    public string? Descrizione1ES { get; set; }

    [StringLength(255)]
    public string? Descrizione2ES { get; set; }

    [StringLength(255)]
    public string? Descrizione3ES { get; set; }

    [StringLength(255)]
    public string? Descrizione1EN { get; set; }

    [StringLength(255)]
    public string? Descrizione2EN { get; set; }

    [StringLength(255)]
    public string? Descrizione3EN { get; set; }

    [StringLength(255)]
    public string? Descrizione1PT { get; set; }

    [StringLength(255)]
    public string? Descrizione2PT { get; set; }

    [StringLength(255)]
    public string? Descrizione3PT { get; set; }

    [StringLength(255)]
    public string? Descrizione1FR { get; set; }

    [StringLength(255)]
    public string? Descrizione2FR { get; set; }

    [StringLength(255)]
    public string? Descrizione3FR { get; set; }

    [StringLength(255)]
    public string? Descrizione1TED { get; set; }

    [StringLength(255)]
    public string? Descrizione2TED { get; set; }

    [StringLength(255)]
    public string? Descrizione3TED { get; set; }

    [StringLength(255)]
    public string? Descrizione1USA { get; set; }

    [StringLength(255)]
    public string? Descrizione2USA { get; set; }

    [StringLength(255)]
    public string? Descrizione3USA { get; set; }

    public string? Logo { get; set; }

    public string? Figura { get; set; }

    [StringLength(255)]
    public string? Note { get; set; }

    [StringLength(255)]
    public string? ModificaTavola { get; set; }

    [StringLength(255)]
    public string? DataModificaTavola { get; set; }

    [StringLength(255)]
    public string? ModificaTesto { get; set; }

    [StringLength(255)]
    public string? DataModificaTesto { get; set; }

    // S1000D Fields
    [StringLength(255)]
    public string? DMC { get; set; }

    [StringLength(255)]
    public string? SBC { get; set; }

    [StringLength(255)]
    public string? SNS { get; set; }
}
