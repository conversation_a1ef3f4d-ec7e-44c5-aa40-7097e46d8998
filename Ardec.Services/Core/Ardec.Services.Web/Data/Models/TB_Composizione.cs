using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ardec.Services.Web.Data.Models;

/// <summary>
/// TB_Composizione - Composizione Cataloghi-Tavole ESATTA dal Framework
/// </summary>
[Table("TB_Composizione")]
public partial class TB_Composizione
{
    [Key]
    [Column(Order = 0)]
    [StringLength(155)]
    public string TER { get; set; } = string.Empty;

    [Key]
    [Column(Order = 1)]
    [StringLength(155)]
    public string Tavola { get; set; } = string.Empty;

    public double? nTavola { get; set; }
    public double? CEDNPagina { get; set; }

    [StringLength(255)]
    public string? CEDIGNPagina { get; set; }

    // Raggruppamenti per MAGIRUS - ESATTO dal Framework
    public string? GruppoITA { get; set; }
    public string? GruppoENG { get; set; }
    public string? GruppoFRA { get; set; }
    public string? GruppoPOR { get; set; }
    public string? GruppoESP { get; set; }
    public string? GruppoTED { get; set; }
    public string? GruppoUSA { get; set; }

    // Navigation properties
    public virtual TB_Cataloghi? TB_Cataloghi { get; set; }
    // public virtual TB_Tavole? TB_Tavole { get; set; } // TODO: quando servirà
}