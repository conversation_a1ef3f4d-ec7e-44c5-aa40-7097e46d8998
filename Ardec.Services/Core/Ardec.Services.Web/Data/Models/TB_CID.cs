using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ardec.Services.Web.Data.Models;

/// <summary>
/// Modello TB_CID allineato ESATTAMENTE al Framework - Change Implementation Document
/// PRIMARY KEY: TER + CID
/// Gestisce i documenti di implementazione dei cambiamenti che richiedono versioni diverse delle tavole
/// Ogni CID rappresenta una configurazione/variante specifica del catalogo
/// </summary>
[Table("TB_CID")]
public partial class TB_CID
{

    [Key]
    [Column(Order = 0)]
    [StringLength(155)]
    public string TER { get; set; } = string.Empty;

    [Key]
    [Column(Order = 1)]
    [StringLength(255)]
    public string CID { get; set; } = string.Empty;

    public DateTime? Data { get; set; }

    public string? Titolo { get; set; }
    public string? TitoloES { get; set; }
    public string? <PERSON><PERSON><PERSON> { get; set; }
    public string? TitoloPT { get; set; }
    public string? TitoloFR { get; set; }
    public string? TitoloUSA { get; set; }
    public string? TitoloTED { get; set; }

    public string? Applicabilità { get; set; }
    public string? ApplicabilitàES { get; set; }
    public string? ApplicabilitàEN { get; set; }
    public string? ApplicabilitàPT { get; set; }
    public string? ApplicabilitàFR { get; set; }
    public string? ApplicabilitàUSA { get; set; }
    public string? ApplicabilitàTED { get; set; }

    // Navigation properties removed - use queries instead
}
