using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ardec.Services.Web.Data.Models;

/// <summary>
/// Modello TB_Cataloghi ESATTO dal Framework - NON MODIFICARE CAMPI!
/// PRIMARY KEY: TER
/// Deve funzionare sul DB esistente condiviso con Framework
/// </summary>
[Table("TB_Cataloghi")]
public partial class TB_Cataloghi
{
    public TB_Cataloghi()
    {
        TB_CataloghiRVT = new HashSet<TB_CataloghiRVT>();
        TB_Composizione = new HashSet<TB_Composizione>();
    }

    [Key]
    [StringLength(155)]
    public string TER { get; set; } = string.Empty;

    [StringLength(255)]
    public string? PubNUC { get; set; }

    [StringLength(255)]
    public string? TitoloBreve { get; set; }

    [StringLength(255)]
    public string? <PERSON><PERSON> { get; set; }

    [StringLength(255)]
    public string? Titolo2 { get; set; }

    [StringLength(255)]
    public string? TitoloGabbia { get; set; }

    [StringLength(255)]
    public string? Reparto { get; set; }

    [StringLength(255)]
    public string? Base { get; set; }

    [StringLength(255)]
    public string? Revi { get; set; }

    [StringLength(255)]
    public string? NStampato { get; set; }

    [StringLength(255)]
    public string? DescPerCDNavigabile { get; set; }

    [StringLength(255)]
    public string? Preliminari { get; set; }

    [StringLength(255)]
    public string? CEDLanguage { get; set; }

    public double? CEDDivisore { get; set; }
    public double? CEDTONUMBER { get; set; }
    public string? CEDNote { get; set; }

    [StringLength(255)]
    public string? CEDTavolaAvv { get; set; }

    public double? CEDPagAvvertenze { get; set; }
    public double? CEDPagIIG { get; set; }
    public double? CEDPagSchema { get; set; }
    public double? CEDPagMCS { get; set; }
    public double? CEDPagSF { get; set; }
    public double? CEDPagASF { get; set; }
    public double? CEDPagIndNum { get; set; }
    public double? CEDPagIndNUC { get; set; }
    public double? CEDPagIndAlfa { get; set; }
    public double? CEDPagIndNonForniti { get; set; }
    public double? CEDPagSPC { get; set; }
    public double? CEDPagMPM { get; set; }
    public double? CEDCD { get; set; }

    [StringLength(255)]
    public string? CEDPagineValide { get; set; }

    [StringLength(255)]
    public string? ImmagineModoImpiego { get; set; }

    // Multi-language fields - ESATTO dal Framework
    public string? TitoloBreveEN { get; set; }
    public string? TitoloEN { get; set; }
    public string? Titolo2EN { get; set; }
    public string? TitoloGabbiaEN { get; set; }
    public string? RepartoEN { get; set; }
    public string? BaseEN { get; set; }
    public string? ReviEN { get; set; }

    public string? TitoloBreveFR { get; set; }
    public string? TitoloFR { get; set; }
    public string? Titolo2FR { get; set; }
    public string? TitoloGabbiaFR { get; set; }
    public string? RepartoFR { get; set; }
    public string? BaseFR { get; set; }
    public string? ReviFR { get; set; }

    public string? TitoloBreveES { get; set; }
    public string? TitoloES { get; set; }
    public string? Titolo2ES { get; set; }
    public string? TitoloGabbiaES { get; set; }
    public string? RepartoES { get; set; }
    public string? BaseES { get; set; }
    public string? ReviES { get; set; }

    public string? TitoloBrevePT { get; set; }
    public string? TitoloPT { get; set; }
    public string? Titolo2PT { get; set; }
    public string? TitoloGabbiaPT { get; set; }
    public string? RepartoPT { get; set; }
    public string? BasePT { get; set; }
    public string? ReviPT { get; set; }

    public string? TitoloBreveTED { get; set; }
    public string? TitoloTED { get; set; }
    public string? Titolo2TED { get; set; }
    public string? TitoloGabbiaTED { get; set; }
    public string? RepartoTED { get; set; }
    public string? BaseTED { get; set; }
    public string? ReviTED { get; set; }

    public string? TitoloBreveUSA { get; set; }
    public string? TitoloUSA { get; set; }
    public string? Titolo2USA { get; set; }
    public string? TitoloGabbiaUSA { get; set; }
    public string? RepartoUSA { get; set; }
    public string? BaseUSA { get; set; }
    public string? ReviUSA { get; set; }

    // Flags - ESATTO dal Framework
    public bool? IsMT { get; set; }
    public bool? IsExNato { get; set; }
    public bool? S1000D { get; set; }
    public bool? IsUSA { get; set; }

    public string? CEDLogoName { get; set; }
    public string? Applique { get; set; }
    public string? InfoName { get; set; }

    // Navigation properties - ESATTE dal Framework
    public virtual ICollection<TB_CataloghiRVT> TB_CataloghiRVT { get; set; }
    public virtual ICollection<TB_Composizione> TB_Composizione { get; set; }
}