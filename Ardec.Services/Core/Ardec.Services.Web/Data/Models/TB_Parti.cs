using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ardec.Services.Web.Data.Models;

/// <summary>
/// Modello TB_Parti allineato ESATTAMENTE al Framework parti_model_versioning
/// PRIMARY KEY: Tavola + PART + ITEM + Versione
/// </summary>
[Table("TB_Parti")]
public partial class TB_Parti
{
    [Key]
    [Column(Order = 0)]
    [StringLength(155)]
    public string Tavola { get; set; } = string.Empty;

    [Key]
    [Column(Order = 1)]
    [StringLength(255)]
    public string PART { get; set; } = string.Empty;

    [Key]
    [StringLength(16)]
    [Column(Order = 2)]
    public string ITEM { get; set; } = string.Empty;

    [Key]
    [Column(Order = 3)]
    [StringLength(2)]
    public string Versione { get; set; } = string.Empty;

    public string? CID { get; set; }

    [StringLength(255)]
    public string? QTAV { get; set; }

    [StringLength(255)]
    public string? CodModifica { get; set; }

    [StringLength(255)]
    public string? NotaRVT { get; set; }

    public string? NotaRVTEN { get; set; }
    public string? NotaRVTES { get; set; }
    public string? NotaRVTPT { get; set; }
    public string? NotaRVTFR { get; set; }
    public string? NotaRVTUSA { get; set; }
    public string? Nota2 { get; set; }

    [StringLength(255)]
    public string? Assieme { get; set; }

    [StringLength(255)]
    public string? FasciaManutentiva { get; set; }

    public double? CEDNPagina { get; set; }

    [StringLength(255)]
    public string? CEDNColonna { get; set; }

    [StringLength(255)]
    public string? CSN { get; set; }

    public string? CSNREF { get; set; }

    [StringLength(255)]
    public string? Item1 { get; set; }

    [StringLength(255)]
    public string? Variante_Item { get; set; }

    public bool? ILS { get; set; }

    // SMR FIELDS
    [StringLength(5)]
    public string? SMRCode { get; set; }
    public string? UM { get; set; }
    public string? DOT { get; set; }
    public string? LCN { get; set; }
    public string? ALC { get; set; }
    public string? MODIDX { get; set; }

    // Navigation properties
    [ForeignKey("Tavola")]
    public virtual TB_Tavole? TB_Tavole { get; set; }

    // TODO: Add other navigation properties when needed
    // public virtual TB_Componenti? TB_Componenti { get; set; }
    // public virtual TB_RVT? TB_RVT { get; set; }
    // [ForeignKey("CID")]
    // public virtual TB_CID? TB_CID { get; set; }
}