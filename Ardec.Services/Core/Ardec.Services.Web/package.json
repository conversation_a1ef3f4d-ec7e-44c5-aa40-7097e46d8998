{"name": "ardec.services.web", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build-css": "./node_modules/.bin/tailwindcss -i ./wwwroot/css/tailwind.css -o ./wwwroot/css/tailwind.min.css --minify", "watch-css": "./node_modules/.bin/tailwindcss -i ./wwwroot/css/tailwind.css -o ./wwwroot/css/tailwind.min.css --watch", "dev": "npm run watch-css", "build": "npm run build-css"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "tailwindcss": "^3.4.17"}, "dependencies": {"flowbite": "^3.1.2"}}