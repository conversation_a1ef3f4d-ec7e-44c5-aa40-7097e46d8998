using Ardec.Services.Web.Shared.Extensions;
using Ardec.Services.Web.Shared.Hubs;
using Ardec.Services.Web.Shared.Services;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .CreateLogger();

builder.Host.UseSerilog();

// Add services using extension methods with multi-provider database support
builder.Services.AddArdecDatabase(builder.Configuration);
builder.Services.AddArdecIdentity();
builder.Services.AddArdecControllers();
builder.Services.AddApiDocumentation();
builder.Services.AddApplicationServices();

// TODO: Microsoft Identity Platform (Azure AD) - disabled for initial testing
// if (builder.Configuration.GetSection("AzureAd").Exists())
// {
//     builder.Services.AddAuthentication()
//         .AddMicrosoftAccount(microsoftOptions =>
//         {
//             microsoftOptions.ClientId = builder.Configuration["AzureAd:ClientId"]!;
//             microsoftOptions.ClientSecret = builder.Configuration["AzureAd:ClientSecret"]!;
//         });
// }

// SignalR for real-time updates
builder.Services.AddSignalR();

// CORS
builder.Services.AddArdecCors();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment() || app.Environment.IsStaging())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Ardec Services API v1");
        c.RoutePrefix = "api-docs"; // Change from default "swagger"
        c.DocumentTitle = "Ardec Services API Documentation";
    });
}
else
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
    app.UseHttpsRedirection();
}
app.UseStaticFiles();

app.UseRouting();
app.UseCors("ArdecPolicy");

app.UseAuthentication();
app.UseAuthorization();

// CID-specific routes (order matters - more specific routes first)
app.MapControllerRoute(
    name: "cid_dashboard",
    pattern: "Tables/Dashboard/{ter}/{cid}",
    defaults: new { controller = "Tables", action = "Dashboard" });

app.MapControllerRoute(
    name: "cid_tables",
    pattern: "Tables/{ter}/{cid}/Tables",
    defaults: new { controller = "Tables", action = "Index" });

app.MapControllerRoute(
    name: "cid_selection",
    pattern: "Tables/Catalog/{ter}",
    defaults: new { controller = "Tables", action = "SelectCID" });

app.MapControllerRoute(
    name: "table_details_with_context",
    pattern: "Tables/Details/{tavola}",
    defaults: new { controller = "Tables", action = "Details" });

// MVC routes
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// API routes
app.MapControllerRoute(
    name: "api",
    pattern: "api/{controller}/{action=Index}/{id?}");

// app.MapRazorPages(); // Not using Razor Pages - we have custom Account controller

// TODO: SignalR hubs - disabled for initial testing
// app.MapHub<Shared.Hubs.TableStatusHub>("/hubs/tablestatus");

try
{
    Log.Information("Starting Ardec Services Web Application");

    // Initialize database on startup
    using (var scope = app.Services.CreateScope())
    {
        var dbInitService = scope.ServiceProvider.GetRequiredService<DatabaseInitializationService>();
        await dbInitService.InitializeAsync();
    }

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}