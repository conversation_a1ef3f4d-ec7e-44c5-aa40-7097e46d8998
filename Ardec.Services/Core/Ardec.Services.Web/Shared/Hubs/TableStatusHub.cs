using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;

namespace Ardec.Services.Web.Shared.Hubs;

/// <summary>
/// SignalR Hub per aggiornamenti real-time degli stati delle tavole
/// </summary>
[Authorize]
public class TableStatusHub : Hub
{
    private readonly ILogger<TableStatusHub> _logger;

    public TableStatusHub(ILogger<TableStatusHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Join a specific catalog group to receive updates
    /// </summary>
    /// <param name="catalogTER">Catalog TER to join</param>
    public async Task JoinCatalogGroup(string catalogTER)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"catalog_{catalogTER}");
        _logger.LogInformation("User {UserId} joined catalog group {CatalogTER}", 
            Context.UserIdentifier, catalogTER);
    }

    /// <summary>
    /// Leave a catalog group
    /// </summary>
    /// <param name="catalogTER">Catalog TER to leave</param>
    public async Task LeaveCatalogGroup(string catalogTER)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"catalog_{catalogTER}");
        _logger.LogInformation("User {UserId} left catalog group {CatalogTER}", 
            Context.UserIdentifier, catalogTER);
    }

    /// <summary>
    /// Broadcast table status change to catalog group
    /// </summary>
    /// <param name="catalogTER">Catalog TER</param>
    /// <param name="tableId">Table ID</param>
    /// <param name="newStatus">New status (0-3)</param>
    /// <param name="userName">User who made the change</param>
    public async Task NotifyTableStatusChange(string catalogTER, Guid tableId, int newStatus, string userName)
    {
        var notification = new
        {
            TableId = tableId,
            NewStatus = newStatus,
            StatusText = GetStatusText(newStatus),
            StatusColor = GetStatusColor(newStatus),
            ChangedBy = userName,
            Timestamp = DateTime.UtcNow
        };

        await Clients.Group($"catalog_{catalogTER}")
            .SendAsync("TableStatusChanged", notification);

        _logger.LogInformation("Notified table status change for table {TableId} in catalog {CatalogTER} to status {Status}", 
            tableId, catalogTER, newStatus);
    }

    /// <summary>
    /// Broadcast new table added to catalog
    /// </summary>
    public async Task NotifyTableAdded(string catalogTER, object tableData)
    {
        await Clients.Group($"catalog_{catalogTER}")
            .SendAsync("TableAdded", tableData);
    }

    /// <summary>
    /// Broadcast table deleted from catalog
    /// </summary>
    public async Task NotifyTableDeleted(string catalogTER, Guid tableId)
    {
        await Clients.Group($"catalog_{catalogTER}")
            .SendAsync("TableDeleted", new { TableId = tableId });
    }

    /// <summary>
    /// Send typing indicator for collaborative editing
    /// </summary>
    public async Task SendTypingIndicator(string catalogTER, string userName, string field)
    {
        await Clients.OthersInGroup($"catalog_{catalogTER}")
            .SendAsync("UserTyping", new { UserName = userName, Field = field });
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("User {UserId} connected to TableStatusHub", Context.UserIdentifier);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("User {UserId} disconnected from TableStatusHub", Context.UserIdentifier);
        await base.OnDisconnectedAsync(exception);
    }

    private static string GetStatusText(int status) => status switch
    {
        0 => "Base",
        1 => "In lavorazione",
        2 => "In revisione", 
        3 => "Problemi",
        _ => "Sconosciuto"
    };

    private static string GetStatusColor(int status) => status switch
    {
        0 => "success",
        1 => "warning",
        2 => "info",
        3 => "danger",
        _ => "secondary"
    };
}