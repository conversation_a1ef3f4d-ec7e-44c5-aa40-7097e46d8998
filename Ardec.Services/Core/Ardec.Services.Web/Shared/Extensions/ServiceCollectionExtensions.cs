using Ardec.Services.Web.Data;
using Ardec.Services.Web.Services.Catalogs;
using Ardec.Services.Web.Services.CID;
using Ardec.Services.Web.Services.Cid;
using Ardec.Services.Web.Services.Compositions;
using Ardec.Services.Web.Services.Tables;
using Ardec.Services.Web.Services.Parts;
using Ardec.Services.Web.Services.Rvt;
using Ardec.Services.Web.Services.CataloghiRVT;
using Ardec.Services.Web.Services.Componenti;
using Ardec.Services.Web.Services.DescrizioniCustom;
// using Ardec.Services.Web.Services.Subfornitori; // Not implemented yet
using Ardec.Services.Web.Shared.Authorization;
using Ardec.Services.Web.Shared.Configuration;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;

namespace Ardec.Services.Web.Shared.Extensions;

public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Register all application services (Feature Services)
    /// </summary>
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // Business Services - Using Framework models
        services.AddScoped<ICatalogService, CatalogService>();
        services.AddScoped<ITableService, TableService>();
        services.AddScoped<ITableExportService, TableExportService>();
        services.AddScoped<ITableCacheService, TableCacheService>();
        services.AddScoped<IPartService, PartService>();
        services.AddScoped<IRvtService, RvtService>();
        services.AddScoped<ICompositionService, CompositionService>();
        services.AddScoped<ICIDService, CidService>();

        // Additional Business Services - 4 new entities
        services.AddScoped<ICataloghiRVTService, CataloghiRVTService>();
        services.AddScoped<IComponentiService, ComponentiService>();
        services.AddScoped<IDescrizioniCustomService, DescrizioniCustomService>();
        // services.AddScoped<ISubfornitoriService, SubfornitoriService>(); // Not implemented yet

        // Report Services
        services.AddScoped<Ardec.Services.Web.Services.IReportService, Ardec.Services.Web.Services.ReportService>();

        // Database initialization service
        services.AddScoped<Shared.Services.DatabaseInitializationService>();
        
        // Memory cache for table caching service
        services.AddMemoryCache(options =>
        {
            options.SizeLimit = 1000; // Limit cache to 1000 entries
            options.CompactionPercentage = 0.2; // Remove 20% when limit reached
        });

        return services;
    }

    /// <summary>
    /// Configure database with multi-provider support (SQL Server, SQLite, InMemory)
    /// </summary>
    public static IServiceCollection AddArdecDatabase(this IServiceCollection services, IConfiguration configuration)
    {
        // Get database options from configuration
        var databaseOptions = new DatabaseOptions();
        configuration.GetSection(DatabaseOptions.SectionName).Bind(databaseOptions);

        // Register options for DI
        services.Configure<DatabaseOptions>(configuration.GetSection(DatabaseOptions.SectionName));

        services.AddDbContext<ArdecDbContext>(options =>
        {
            ConfigureDatabaseProvider(options, databaseOptions, configuration);

            // Development-only options
            if (databaseOptions.EnableSensitiveDataLogging)
            {
                options.EnableSensitiveDataLogging();
            }

            if (databaseOptions.EnableDetailedErrors)
            {
                options.EnableDetailedErrors();
            }
        });

        return services;
    }

    /// <summary>
    /// Configure the specific database provider based on options
    /// </summary>
    private static void ConfigureDatabaseProvider(DbContextOptionsBuilder options, DatabaseOptions databaseOptions, IConfiguration configuration)
    {
        switch (databaseOptions.Provider)
        {
            case DatabaseProvider.SqlServer:
                var connectionString = databaseOptions.SqlServerConnectionString
                    ?? configuration.GetConnectionString("DefaultConnection")
                    ?? throw new InvalidOperationException("SQL Server connection string not found");

                options.UseSqlServer(connectionString, sqlOptions =>
                {
                    sqlOptions.CommandTimeout(databaseOptions.CommandTimeoutSeconds);
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(5),
                        errorNumbersToAdd: null);
                });
                break;

            case DatabaseProvider.Sqlite:
                var sqliteConnectionString = $"Data Source={databaseOptions.SqliteFilePath}";
                options.UseSqlite(sqliteConnectionString, sqliteOptions =>
                {
                    sqliteOptions.CommandTimeout(databaseOptions.CommandTimeoutSeconds);
                });
                break;

            case DatabaseProvider.InMemory:
                options.UseInMemoryDatabase("ArdecServicesInMemory");
                break;

            default:
                throw new ArgumentOutOfRangeException(nameof(databaseOptions.Provider), databaseOptions.Provider, "Unsupported database provider");
        }
    }

    /// <summary>
    /// Configure Identity with custom options
    /// </summary>
    public static IServiceCollection AddArdecIdentity(this IServiceCollection services)
    {
        services.AddIdentity<IdentityUser, IdentityRole>(options =>
        {
            // Password settings
            options.Password.RequireDigit = true;
            options.Password.RequiredLength = 8;
            options.Password.RequireNonAlphanumeric = false;
            options.Password.RequireUppercase = true;
            options.Password.RequireLowercase = true;

            // Lockout settings
            options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
            options.Lockout.MaxFailedAccessAttempts = 5;
            options.Lockout.AllowedForNewUsers = true;

            // User settings
            options.User.RequireUniqueEmail = true;
            options.SignIn.RequireConfirmedEmail = false;
            options.SignIn.RequireConfirmedPhoneNumber = false;
        })
        .AddEntityFrameworkStores<ArdecDbContext>()
        .AddDefaultTokenProviders();

        // Configure cookie authentication to use our custom Account controller paths
        services.ConfigureApplicationCookie(options =>
        {
            options.LoginPath = "/Account/Login";        // Our custom login page
            options.LogoutPath = "/Account/Logout";      // Our custom logout
            options.AccessDeniedPath = "/Account/AccessDenied"; // Our custom access denied
            options.ExpireTimeSpan = TimeSpan.FromHours(24);
            options.SlidingExpiration = true;
            options.Cookie.HttpOnly = true;
            options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
        });

        // Configure authorization policies
        services.AddAuthorization(PolicyRequirements.ConfigurePolicies);

        return services;
    }

    /// <summary>
    /// Configure API documentation (Swagger)
    /// </summary>
    public static IServiceCollection AddApiDocumentation(this IServiceCollection services)
    {
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new()
            {
                Title = "Ardec Services API",
                Version = "v1",
                Description = "API per gestione cataloghi, tavole e parti tecniche - Sistema Ardec Defense",
                Contact = new()
                {
                    Name = "Ardec Development Team",
                    Email = "<EMAIL>",
                    Url = new Uri("https://www.ardec.com")
                },
                License = new()
                {
                    Name = "Proprietary",
                    Url = new Uri("https://www.ardec.com/license")
                }
            });

            // Include XML comments if available
            var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                c.IncludeXmlComments(xmlPath);
            }

            // Security definitions for Cookie Authentication
            c.AddSecurityDefinition("Cookie", new()
            {
                Description = "Cookie-based authentication. Use /api/account/login to authenticate.",
                Name = "Cookie Authentication",
                Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
                In = Microsoft.OpenApi.Models.ParameterLocation.Cookie,
                Scheme = "Cookie"
            });

            c.AddSecurityRequirement(new()
            {
                {
                    new()
                    {
                        Reference = new()
                        {
                            Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                            Id = "Cookie"
                        }
                    },
                    Array.Empty<string>()
                }
            });

            // Custom schema IDs to avoid conflicts
            c.CustomSchemaIds(type => type.FullName?.Replace('+', '.'));
        });

        return services;
    }

    /// <summary>
    /// Configure CORS policy
    /// </summary>
    public static IServiceCollection AddArdecCors(this IServiceCollection services)
    {
        services.AddCors(options =>
        {
            options.AddPolicy("ArdecPolicy", policy =>
            {
                policy.WithOrigins(
                        "https://localhost:5001",
                        "https://localhost:7001", 
                        "https://ardec-services.com",
                        "https://staging.ardec-services.com"
                    )
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    .AllowCredentials()
                    .SetIsOriginAllowedToAllowWildcardSubdomains();
            });

            // More permissive policy for development
            options.AddPolicy("Development", policy =>
            {
                policy.AllowAnyOrigin()
                      .AllowAnyMethod()
                      .AllowAnyHeader();
            });
        });

        return services;
    }

    /// <summary>
    /// Configure controllers with custom JSON options
    /// </summary>
    public static IServiceCollection AddArdecControllers(this IServiceCollection services)
    {
        services.AddControllersWithViews(options =>
        {
            // Global filters can be added here
            // options.Filters.Add<GlobalExceptionFilter>();
        })
        .AddJsonOptions(options =>
        {
            options.JsonSerializerOptions.PropertyNamingPolicy = null; // Keep PascalCase
            options.JsonSerializerOptions.WriteIndented = true;
            options.JsonSerializerOptions.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull;
        });

        return services;
    }
}