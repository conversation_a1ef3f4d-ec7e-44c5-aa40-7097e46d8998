namespace Ardec.Services.Web.Shared.Configuration;

/// <summary>
/// Database provider types supported by the application
/// </summary>
public enum DatabaseProvider
{
    /// <summary>
    /// SQL Server - Production database (shared with Framework)
    /// </summary>
    SqlServer,
    
    /// <summary>
    /// SQLite - Local development database
    /// </summary>
    Sqlite,
    
    /// <summary>
    /// In-Memory - Testing and development without persistence
    /// </summary>
    InMemory
}

/// <summary>
/// Database configuration options
/// </summary>
public class DatabaseOptions
{
    public const string SectionName = "Database";
    
    /// <summary>
    /// Database provider to use
    /// </summary>
    public DatabaseProvider Provider { get; set; } = DatabaseProvider.SqlServer;
    
    /// <summary>
    /// Connection string for SQL Server
    /// </summary>
    public string? SqlServerConnectionString { get; set; }
    
    /// <summary>
    /// SQLite database file path (relative to app directory)
    /// </summary>
    public string SqliteFilePath { get; set; } = "Data/ardec-services.db";
    
    /// <summary>
    /// Whether to enable sensitive data logging (development only)
    /// </summary>
    public bool EnableSensitiveDataLogging { get; set; } = false;
    
    /// <summary>
    /// Whether to enable detailed errors (development only)
    /// </summary>
    public bool EnableDetailedErrors { get; set; } = false;
    
    /// <summary>
    /// Command timeout in seconds
    /// </summary>
    public int CommandTimeoutSeconds { get; set; } = 120;
    
    /// <summary>
    /// Whether to automatically create database if it doesn't exist
    /// </summary>
    public bool EnsureCreated { get; set; } = false;
    
    /// <summary>
    /// Whether to seed sample data for development
    /// </summary>
    public bool SeedSampleData { get; set; } = false;
}
