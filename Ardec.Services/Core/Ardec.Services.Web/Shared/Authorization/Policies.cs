using Microsoft.AspNetCore.Authorization;

namespace Ardec.Services.Web.Shared.Authorization;

/// <summary>
/// Authorization roles and policies for Ardec Services
/// </summary>
public static class Roles
{
    public const string Admin = "Admin";           // Accesso completo
    public const string PowerUser = "PowerUser";   // Modifica + Export avanzati  
    public const string Editor = "Editor";         // Modifica parti/tavole assegnate
    public const string Viewer = "Viewer";         // Solo visualizzazione
}

public static class Policies
{
    public const string CanEditCatalog = "CanEditCatalog";
    public const string CanExportData = "CanExportData";
    public const string CanManageUsers = "CanManageUsers";
    public const string CanChangeTableStatus = "CanChangeTableStatus";
    public const string CanDeleteData = "CanDeleteData";
    public const string CanCreateData = "CanCreateData";
    
    // Standard RBAC policies for new APIs
    public const string ReadAccess = "ReadAccess";
    public const string WriteAccess = "WriteAccess";
    public const string DeleteAccess = "DeleteAccess";
}

/// <summary>
/// Policy requirements for granular authorization
/// </summary>
public static class PolicyRequirements
{
    /// <summary>
    /// Configure authorization policies
    /// </summary>
    public static void ConfigurePolicies(AuthorizationOptions options)
    {
        // Catalog management policies
        options.AddPolicy(Policies.CanEditCatalog, policy =>
            policy.RequireRole(Roles.Admin, Roles.PowerUser, Roles.Editor));

        // Data export policies  
        options.AddPolicy(Policies.CanExportData, policy =>
            policy.RequireRole(Roles.Admin, Roles.PowerUser));

        // User management policies
        options.AddPolicy(Policies.CanManageUsers, policy =>
            policy.RequireRole(Roles.Admin));

        // Table status management policies
        options.AddPolicy(Policies.CanChangeTableStatus, policy =>
            policy.RequireRole(Roles.Admin, Roles.PowerUser, Roles.Editor));

        // Data deletion policies (most restrictive)
        options.AddPolicy(Policies.CanDeleteData, policy =>
            policy.RequireRole(Roles.Admin));

        // Data creation policies
        options.AddPolicy(Policies.CanCreateData, policy =>
            policy.RequireRole(Roles.Admin, Roles.PowerUser, Roles.Editor));

        // Standard RBAC policies
        options.AddPolicy(Policies.ReadAccess, policy =>
            policy.RequireRole(Roles.Admin, Roles.PowerUser, Roles.Editor, Roles.Viewer));

        options.AddPolicy(Policies.WriteAccess, policy =>
            policy.RequireRole(Roles.Admin, Roles.PowerUser, Roles.Editor));

        options.AddPolicy(Policies.DeleteAccess, policy =>
            policy.RequireRole(Roles.Admin));
    }
}