using Ardec.Services.Web.Data;
using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Shared.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Ardec.Services.Web.Shared.Services;

/// <summary>
/// Service for database initialization and seeding
/// </summary>
public class DatabaseInitializationService
{
    private readonly ArdecDbContext _context;
    private readonly DatabaseOptions _databaseOptions;
    private readonly ILogger<DatabaseInitializationService> _logger;

    public DatabaseInitializationService(
        ArdecDbContext context,
        IOptions<DatabaseOptions> databaseOptions,
        ILogger<DatabaseInitializationService> logger)
    {
        _context = context;
        _databaseOptions = databaseOptions.Value;
        _logger = logger;
    }

    /// <summary>
    /// Initialize database (create if needed, run migrations, seed data)
    /// </summary>
    public async Task InitializeAsync()
    {
        try
        {
            // Ensure database is created (for SQLite and InMemory)
            if (_databaseOptions.EnsureCreated && _databaseOptions.Provider != DatabaseProvider.SqlServer)
            {
                _logger.LogInformation("Ensuring database is created for provider: {Provider}", _databaseOptions.Provider);
                await _context.Database.EnsureCreatedAsync();
            }

            // Run pending migrations (for SQL Server and SQLite)
            if (_databaseOptions.Provider != DatabaseProvider.InMemory)
            {
                var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
                if (pendingMigrations.Any())
                {
                    _logger.LogInformation("Applying {Count} pending migrations", pendingMigrations.Count());
                    await _context.Database.MigrateAsync();
                }
            }

            // Seed sample data if requested
            if (_databaseOptions.SeedSampleData)
            {
                await SeedSampleDataAsync();
            }

            _logger.LogInformation("Database initialization completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during database initialization");
            throw;
        }
    }

    /// <summary>
    /// Seed sample data for development/testing
    /// </summary>
    private async Task SeedSampleDataAsync()
    {
        _logger.LogInformation("Seeding sample data...");

        // Check if data already exists
        if (await _context.TB_Cataloghi.AnyAsync())
        {
            _logger.LogInformation("Sample data already exists, skipping seeding");
            return;
        }

        // Seed sample catalogs (based on Framework structure)
        var sampleCatalogs = new List<TB_Cataloghi>
        {
            new TB_Cataloghi
            {
                TER = "TEST001",
                PubNUC = "PUB-TEST-001",
                TitoloBreve = "Catalogo Test 1",
                Titolo = "Catalogo di Test per Sviluppo",
                Titolo2 = "Versione di Sviluppo",
                TitoloGabbia = "TEST-DEV",
                Reparto = "Sviluppo",
                Base = "01",
                Revi = "00",
                NStampato = "TEST-001-00",
                DescPerCDNavigabile = "Catalogo per test sviluppo applicazione web",
                CEDLanguage = "ITA",
                IsMT = false,
                IsExNato = false,
                S1000D = false,
                IsUSA = false
            },
            new TB_Cataloghi
            {
                TER = "TEST002", 
                PubNUC = "PUB-TEST-002",
                TitoloBreve = "Catalogo Test 2",
                Titolo = "Secondo Catalogo di Test",
                Titolo2 = "Multilingue",
                TitoloGabbia = "TEST-ML",
                Reparto = "Sviluppo",
                Base = "01",
                Revi = "01",
                NStampato = "TEST-002-01",
                DescPerCDNavigabile = "Catalogo multilingue per test",
                CEDLanguage = "ENG",
                // English versions
                TitoloBreveTED = "Test Catalog 2",
                TitoloTED = "Second Test Catalog",
                Titolo2TED = "Multilingual",
                TitoloGabbiaTED = "TEST-ML",
                RepartoTED = "Development",
                BaseTED = "01",
                ReviTED = "01",
                IsMT = true,
                IsExNato = false,
                S1000D = true,
                IsUSA = false
            }
        };

        _context.TB_Cataloghi.AddRange(sampleCatalogs);

        // Seed sample RVT relationships
        var sampleRVTs = new List<TB_CataloghiRVT>
        {
            new TB_CataloghiRVT
            {
                TER = "TEST001",
                RVT = "RVT-001",
                Applicabilità = "Tutte le versioni"
            },
            new TB_CataloghiRVT
            {
                TER = "TEST002",
                RVT = "RVT-002", 
                Applicabilità = "Versioni > 2.0",
                ApplicabilitàEN = "Versions > 2.0"
            }
        };

        _context.TB_CataloghiRVT.AddRange(sampleRVTs);

        // Seed sample tables (tavole)
        var sampleTables = new List<TB_Tavole>
        {
            new TB_Tavole
            {
                Tavola = "TEST001-001",
                CodiceTecnico = "TC-001",
                TECHNAME = "Engine Assembly",
                INFONAME = "Main engine components",
                ICN_TITLE = "Engine Parts",
                ModificaTavola = false,
                DataModificaTavola = "2024-01-15"
            },
            new TB_Tavole
            {
                Tavola = "TEST001-002",
                CodiceTecnico = "TC-002",
                TECHNAME = "Transmission System",
                INFONAME = "Transmission components",
                ICN_TITLE = "Transmission Parts",
                ModificaTavola = true,
                DataModificaTavola = "2024-02-20"
            },
            new TB_Tavole
            {
                Tavola = "TEST002-001",
                CodiceTecnico = "TC-003",
                TECHNAME = "Electrical System",
                INFONAME = "Electrical components",
                ICN_TITLE = "Electrical Parts",
                ModificaTavola = false,
                DataModificaTavola = "2024-03-10"
            }
        };

        _context.TB_Tavole.AddRange(sampleTables);

        // Seed sample compositions (catalogo-tavola relationships)
        var sampleCompositions = new List<TB_Composizione>
        {
            new TB_Composizione
            {
                TER = "TEST001",
                Tavola = "TEST001-001",
                nTavola = 1,
                CEDNPagina = 10,
                CEDIGNPagina = "Page 10",
                GruppoITA = "Motore",
                GruppoENG = "Engine",
                GruppoFRA = "Moteur",
                GruppoTED = "Motor"
            },
            new TB_Composizione
            {
                TER = "TEST001",
                Tavola = "TEST001-002",
                nTavola = 2,
                CEDNPagina = 25,
                CEDIGNPagina = "Page 25",
                GruppoITA = "Trasmissione",
                GruppoENG = "Transmission",
                GruppoFRA = "Transmission",
                GruppoTED = "Getriebe"
            },
            new TB_Composizione
            {
                TER = "TEST002",
                Tavola = "TEST002-001",
                nTavola = 1,
                CEDNPagina = 15,
                CEDIGNPagina = "Page 15",
                GruppoITA = "Elettrico",
                GruppoENG = "Electrical",
                GruppoFRA = "Électrique",
                GruppoTED = "Elektrisch"
            }
        };

        _context.TB_Composizione.AddRange(sampleCompositions);

        // Seed sample parts
        var sampleParts = new List<TB_Parti>
        {
            new TB_Parti
            {
                Tavola = "TEST001-001",
                PART = "ENG-001",
                ITEM = "001",
                Versione = "00",
                QTAV = "1",
                CSNREF = "CSN-ENG-001",
                LCN = "LCN-001",
                Assieme = "Engine Block",
                FasciaManutentiva = "A",
                CEDNPagina = 10,
                CEDNColonna = "A",
                SMRCode = "A001",
                UM = "EA",
                DOT = "DOT-001"
            },
            new TB_Parti
            {
                Tavola = "TEST001-001",
                PART = "ENG-002",
                ITEM = "002",
                Versione = "00",
                QTAV = "4",
                CSNREF = "CSN-ENG-002",
                LCN = "LCN-002",
                Assieme = "Piston Assembly",
                FasciaManutentiva = "B",
                CEDNPagina = 10,
                CEDNColonna = "B",
                SMRCode = "B002",
                UM = "EA",
                DOT = "DOT-002"
            },
            new TB_Parti
            {
                Tavola = "TEST001-002",
                PART = "TRX-001",
                ITEM = "001",
                Versione = "00",
                QTAV = "1",
                CSNREF = "CSN-TRX-001",
                LCN = "LCN-003",
                Assieme = "Gearbox",
                FasciaManutentiva = "A",
                CEDNPagina = 25,
                CEDNColonna = "A",
                SMRCode = "A003",
                UM = "EA",
                DOT = "DOT-003"
            },
            new TB_Parti
            {
                Tavola = "TEST002-001",
                PART = "ELC-001",
                ITEM = "001",
                Versione = "00",
                QTAV = "1",
                CSNREF = "CSN-ELC-001",
                LCN = "LCN-004",
                Assieme = "Control Unit",
                FasciaManutentiva = "C",
                CEDNPagina = 15,
                CEDNColonna = "A",
                SMRCode = "C001",
                UM = "EA",
                DOT = "DOT-004",
                ILS = true
            }
        };

        _context.TB_Parti.AddRange(sampleParts);

        await _context.SaveChangesAsync();

        _logger.LogInformation("Sample data seeded successfully: {CatalogCount} catalogs, {RvtCount} RVT relationships, {TableCount} tables, {CompositionCount} compositions, {PartCount} parts",
            sampleCatalogs.Count, sampleRVTs.Count, sampleTables.Count, sampleCompositions.Count, sampleParts.Count);
    }
}
