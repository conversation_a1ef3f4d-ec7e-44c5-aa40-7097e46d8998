using System.ComponentModel.DataAnnotations;

namespace Ardec.Services.Web.Features.Account;

/// <summary>
/// Login form view model
/// </summary>
public class LoginViewModel
{
    [Required(ErrorMessage = "L'email è obbligatoria")]
    [EmailAddress(ErrorMessage = "Inserisci un indirizzo email valido")]
    public string Email { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "La password è obbligatoria")]
    public string Password { get; set; } = string.Empty;
    
    public bool RememberMe { get; set; }
}

/// <summary>
/// User profile view model
/// </summary>
public class ProfileViewModel
{
    public string Email { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
    public bool EmailConfirmed { get; set; }
    public bool LockoutEnabled { get; set; }
    public int AccessFailedCount { get; set; }
}

/// <summary>
/// API Login request model
/// </summary>
public class LoginRequest
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public bool RememberMe { get; set; } = false;
}

/// <summary>
/// API Login response model
/// </summary>
public class LoginResponse
{
    public bool Success { get; set; }
    public string Email { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
}

/// <summary>
/// API User profile response model
/// </summary>
public class UserProfileResponse
{
    public string Email { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
    public bool EmailConfirmed { get; set; }
    public bool LockoutEnabled { get; set; }
    public int AccessFailedCount { get; set; }
}

/// <summary>
/// Account settings view model
/// </summary>
public class AccountSettingsViewModel
{
    [Required(ErrorMessage = "Il nome utente è obbligatorio")]
    public string UserName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "L'email è obbligatoria")]
    [EmailAddress(ErrorMessage = "Inserisci un indirizzo email valido")]
    public string Email { get; set; } = string.Empty;
    
    public string CurrentEmail { get; set; } = string.Empty;
    public bool EmailConfirmed { get; set; }
    public bool TwoFactorEnabled { get; set; }
    public List<string> Roles { get; set; } = new();
    
    [DataType(DataType.Password)]
    [Display(Name = "Password Attuale")]
    public string? CurrentPassword { get; set; }
    
    [DataType(DataType.Password)]
    [Display(Name = "Nuova Password")]
    [MinLength(6, ErrorMessage = "La password deve essere di almeno 6 caratteri")]
    public string? NewPassword { get; set; }
    
    [DataType(DataType.Password)]
    [Display(Name = "Conferma Nuova Password")]
    [Compare("NewPassword", ErrorMessage = "Le password non coincidono")]
    public string? ConfirmNewPassword { get; set; }
}