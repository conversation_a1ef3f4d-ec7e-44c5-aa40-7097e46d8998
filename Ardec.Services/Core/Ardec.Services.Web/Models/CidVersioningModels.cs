using Ardec.Services.Web.Data.Models;

namespace Ardec.Services.Web.Models;

#region CID Version Matrix Models

/// <summary>
/// Complete version matrix for a catalog showing CID vs Table status
/// Used for the dashboard version overview
/// </summary>
public class CidVersionMatrix
{
    public string TER { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public List<CidVersionInfo> CidVersions { get; set; } = new();
    
    /// <summary>
    /// Get all unique tables across all CIDs
    /// </summary>
    public HashSet<string> AllTables => CidVersions
        .SelectMany(cv => cv.TableVersions.Keys)
        .ToHashSet();
    
    /// <summary>
    /// Get table status for specific CID and table combination
    /// </summary>
    public TableVersionStatus GetTableStatus(string cid, string table)
    {
        var cidVersion = CidVersions.FirstOrDefault(cv => cv.CID == cid);
        if (cidVersion == null) return TableVersionStatus.Empty;
        
        return cidVersion.TableVersions.TryGetValue(table, out var tableInfo) 
            ? tableInfo.Status 
            : TableVersionStatus.Empty;
    }
}

/// <summary>
/// Version information for a specific CID
/// </summary>
public class CidVersionInfo
{
    public string CID { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public DateTime? Date { get; set; }
    public int TotalTables { get; set; }
    public Dictionary<string, TableVersionInfo> TableVersions { get; set; } = new();
}

/// <summary>
/// Version information for a specific table within a CID
/// </summary>
public class TableVersionInfo
{
    public string Tavola { get; set; } = string.Empty;
    public int TotalParts { get; set; }
    public DateTime? LastUpdate { get; set; }
    public TableVersionStatus Status { get; set; }
}

/// <summary>
/// Status of a table within a CID configuration
/// </summary>
public enum TableVersionStatus
{
    Empty = 0,      // No parts configured - Gray
    Incomplete = 1, // Some parts missing or invalid - Yellow  
    Complete = 2,   // All parts properly configured - Green
    Modified = 3    // Recent changes detected - Blue
}

#endregion

#region CID Comparison Models

/// <summary>
/// Result of comparing two CID configurations
/// Shows differences between table and part configurations
/// </summary>
public class CidComparisonResult
{
    public string TER { get; set; } = string.Empty;
    public string CID1 { get; set; } = string.Empty;
    public string CID2 { get; set; } = string.Empty;
    public DateTime ComparedAt { get; set; }
    public List<CidDifference> Differences { get; set; } = new();
    
    /// <summary>
    /// Get summary of differences by type
    /// </summary>
    public Dictionary<string, int> DifferenceSummary => Differences
        .GroupBy(d => d.DifferenceType)
        .ToDictionary(g => g.Key, g => g.Count());
        
    /// <summary>
    /// Check if configurations are identical
    /// </summary>
    public bool AreIdentical => !Differences.Any();
    
    /// <summary>
    /// Get differences for a specific table
    /// </summary>
    public List<CidDifference> GetTableDifferences(string table) => 
        Differences.Where(d => d.Table == table).ToList();
}

/// <summary>
/// Specific difference between two CID configurations
/// </summary>
public class CidDifference
{
    public string Table { get; set; } = string.Empty;
    public string? Position { get; set; }
    public string DifferenceType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? CID1Value { get; set; }
    public string? CID2Value { get; set; }
}

#endregion

#region CID Filtering Models

/// <summary>
/// Advanced filter for CID queries with pagination support
/// </summary>
public class CidFilter
{
    public string? TER { get; set; }
    public string? SearchText { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Language { get; set; }
    public string? SortBy { get; set; } = "cid"; // "cid", "date", "title"
    public bool SortDescending { get; set; } = false;
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    
    /// <summary>
    /// Validate filter parameters
    /// </summary>
    public bool IsValid()
    {
        if (Page < 1) return false;
        if (PageSize < 1 || PageSize > 1000) return false;
        if (StartDate.HasValue && EndDate.HasValue && StartDate > EndDate) return false;
        return true;
    }
}

/// <summary>
/// Paginated result set
/// </summary>
public class PagedResult<T>
{
    public IEnumerable<T> Items { get; set; } = Enumerable.Empty<T>();
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    
    /// <summary>
    /// Check if there are more pages
    /// </summary>
    public bool HasNextPage => CurrentPage < TotalPages;
    
    /// <summary>
    /// Check if there are previous pages
    /// </summary>
    public bool HasPreviousPage => CurrentPage > 1;
    
    /// <summary>
    /// Get page range for pagination UI
    /// </summary>
    public IEnumerable<int> GetPageRange(int maxVisible = 10)
    {
        var start = Math.Max(1, CurrentPage - maxVisible / 2);
        var end = Math.Min(TotalPages, start + maxVisible - 1);
        start = Math.Max(1, end - maxVisible + 1);
        
        return Enumerable.Range(start, end - start + 1);
    }
}

#endregion

#region CID Dashboard Models

/// <summary>
/// Dashboard data for CID version overview
/// Combines statistics, matrix, and recent activity
/// </summary>
public class CidDashboard
{
    public string TER { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public CidVersionMatrix VersionMatrix { get; set; } = new();
    public CidStatistics Statistics { get; set; } = new();
    public List<CidActivity> RecentActivity { get; set; } = new();
}

/// <summary>
/// Statistical overview of CIDs for a catalog
/// </summary>
public class CidStatistics
{
    public int TotalCids { get; set; }
    public int TotalTables { get; set; }
    public int TotalParts { get; set; }
    public Dictionary<TableVersionStatus, int> StatusDistribution { get; set; } = new();
    public DateTime? LastActivity { get; set; }
    public Dictionary<string, int> LanguageSupport { get; set; } = new();
}

/// <summary>
/// Recent activity entry for CID changes
/// </summary>
public class CidActivity
{
    public string CID { get; set; } = string.Empty;
    public string Table { get; set; } = string.Empty;
    public string ActivityType { get; set; } = string.Empty; // "Created", "Modified", "Deleted"
    public DateTime Timestamp { get; set; }
    public string? Description { get; set; }
    public string? UserInfo { get; set; }
}

#endregion

#region CID Selection Models

/// <summary>
/// CID selector option with display information
/// </summary>
public class CidSelectorOption
{
    public string CID { get; set; } = string.Empty;
    public string DisplayTitle { get; set; } = string.Empty;
    public DateTime? Date { get; set; }
    public int TableCount { get; set; }
    public TableVersionStatus OverallStatus { get; set; }
    public bool IsSelected { get; set; }
}

/// <summary>
/// CID context for the current user session
/// </summary>
public class CidContext
{
    public string TER { get; set; } = string.Empty;
    public string? SelectedCID { get; set; }
    public string? SelectedTable { get; set; }
    public string Language { get; set; } = "IT";
    public DateTime SessionStart { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Check if CID is selected
    /// </summary>
    public bool HasCidSelected => !string.IsNullOrWhiteSpace(SelectedCID);
    
    /// <summary>
    /// Check if table is selected within CID
    /// </summary>
    public bool HasTableSelected => HasCidSelected && !string.IsNullOrWhiteSpace(SelectedTable);
}

#endregion

#region Extension Methods

/// <summary>
/// Extension methods for CID versioning models
/// </summary>
public static class CidVersioningExtensions
{
    /// <summary>
    /// Get display color for table version status
    /// </summary>
    public static string GetDisplayColor(this TableVersionStatus status) => status switch
    {
        TableVersionStatus.Empty => "gray",
        TableVersionStatus.Incomplete => "yellow", 
        TableVersionStatus.Complete => "green",
        TableVersionStatus.Modified => "blue",
        _ => "gray"
    };
    
    /// <summary>
    /// Get display text for table version status
    /// </summary>
    public static string GetDisplayText(this TableVersionStatus status) => status switch
    {
        TableVersionStatus.Empty => "Empty",
        TableVersionStatus.Incomplete => "Incomplete",
        TableVersionStatus.Complete => "Complete", 
        TableVersionStatus.Modified => "Modified",
        _ => "Unknown"
    };
    
    /// <summary>
    /// Get CSS class for table version status
    /// </summary>
    public static string GetCssClass(this TableVersionStatus status) => status switch
    {
        TableVersionStatus.Empty => "status-empty",
        TableVersionStatus.Incomplete => "status-incomplete",
        TableVersionStatus.Complete => "status-complete",
        TableVersionStatus.Modified => "status-modified",
        _ => "status-unknown"
    };
    
    /// <summary>
    /// Convert to CID selector options
    /// </summary>
    public static IEnumerable<CidSelectorOption> ToSelectorOptions(this IEnumerable<TB_CID> cids, 
        Dictionary<string, CidVersionInfo>? versionInfo = null)
    {
        return cids.Select(cid => new CidSelectorOption
        {
            CID = cid.CID,
            DisplayTitle = !string.IsNullOrWhiteSpace(cid.Titolo) ? cid.Titolo : cid.CID,
            Date = cid.Data,
            TableCount = versionInfo?.TryGetValue(cid.CID, out var info) == true ? info.TotalTables : 0,
            OverallStatus = versionInfo?.TryGetValue(cid.CID, out var info2) == true 
                ? DetermineOverallStatus(info2.TableVersions.Values)
                : TableVersionStatus.Empty
        });
    }
    
    /// <summary>
    /// Determine overall status from table statuses
    /// </summary>
    private static TableVersionStatus DetermineOverallStatus(IEnumerable<TableVersionInfo> tables)
    {
        var tableList = tables.ToList();
        if (!tableList.Any()) return TableVersionStatus.Empty;
        
        if (tableList.Any(t => t.Status == TableVersionStatus.Modified))
            return TableVersionStatus.Modified;
            
        if (tableList.All(t => t.Status == TableVersionStatus.Complete))
            return TableVersionStatus.Complete;
            
        if (tableList.Any(t => t.Status == TableVersionStatus.Complete))
            return TableVersionStatus.Incomplete;
            
        return TableVersionStatus.Empty;
    }
}

#endregion
