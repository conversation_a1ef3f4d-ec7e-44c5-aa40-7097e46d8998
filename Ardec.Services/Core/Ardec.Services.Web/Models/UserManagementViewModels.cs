using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Ardec.Services.Web.Features.UserManagement;

/// <summary>
/// ViewModel per la lista degli utenti
/// </summary>
public class UserListViewModel
{
    public List<UserItemViewModel> Users { get; set; } = new();
    public int TotalUsers { get; set; }
    public int CurrentPage { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public int TotalPages => (int)Math.Ceiling((double)TotalUsers / PageSize);
    public string? SearchTerm { get; set; }
    public string? SelectedRole { get; set; }
    public List<SelectListItem> AvailableRoles { get; set; } = new();
}

/// <summary>
/// ViewModel per singolo utente nella lista
/// </summary>
public class UserItemViewModel
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
    public bool EmailConfirmed { get; set; }
    public bool LockoutEnabled { get; set; }
    public DateTimeOffset? LockoutEnd { get; set; }
    public int AccessFailedCount { get; set; }
    public DateTime? LastLoginDate { get; set; }
    public bool IsLockedOut => LockoutEnd.HasValue && LockoutEnd > DateTimeOffset.UtcNow;
    public string PrimaryRole => Roles.FirstOrDefault() ?? "Viewer";
    
    /// <summary>
    /// Ottiene la descrizione del ruolo principale
    /// </summary>
    public string RoleDescription => PrimaryRole switch
    {
        "Admin" => "Amministratore - Accesso completo",
        "PowerUser" => "Utente Avanzato - Modifica ed export avanzati",
        "Editor" => "Editor - Modifica parti/tavole assegnate",
        "Viewer" => "Visualizzatore - Solo lettura",
        _ => "Ruolo non definito"
    };
}

/// <summary>
/// ViewModel per creazione nuovo utente
/// </summary>
public class CreateUserViewModel
{
    [Required(ErrorMessage = "L'email è obbligatoria")]
    [EmailAddress(ErrorMessage = "Inserisci un indirizzo email valido")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Il nome utente è obbligatorio")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "Il nome utente deve essere tra 3 e 50 caratteri")]
    public string UserName { get; set; } = string.Empty;

    [Required(ErrorMessage = "La password è obbligatoria")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "La password deve essere di almeno 6 caratteri")]
    [DataType(DataType.Password)]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "Conferma la password")]
    [DataType(DataType.Password)]
    [Compare("Password", ErrorMessage = "Le password non coincidono")]
    public string ConfirmPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "Seleziona almeno un ruolo")]
    public List<string> SelectedRoles { get; set; } = new();

    public bool EmailConfirmed { get; set; } = true;
    public bool LockoutEnabled { get; set; } = true;

    // Lista dei ruoli disponibili per la selezione
    public List<SelectListItem> AvailableRoles { get; set; } = new();
}

/// <summary>
/// ViewModel per modifica utente esistente
/// </summary>
public class EditUserViewModel
{
    public string Id { get; set; } = string.Empty;

    [Required(ErrorMessage = "L'email è obbligatoria")]
    [EmailAddress(ErrorMessage = "Inserisci un indirizzo email valido")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Il nome utente è obbligatorio")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "Il nome utente deve essere tra 3 e 50 caratteri")]
    public string UserName { get; set; } = string.Empty;

    public List<string> SelectedRoles { get; set; } = new();
    public List<string> CurrentRoles { get; set; } = new();
    public bool EmailConfirmed { get; set; }
    public bool LockoutEnabled { get; set; }
    public DateTimeOffset? LockoutEnd { get; set; }
    public int AccessFailedCount { get; set; }

    // Lista dei ruoli disponibili per la selezione
    public List<SelectListItem> AvailableRoles { get; set; } = new();
    
    // Informazioni aggiuntive
    public DateTime? LastLoginDate { get; set; }
    public bool IsLockedOut => LockoutEnd.HasValue && LockoutEnd > DateTimeOffset.UtcNow;
}

/// <summary>
/// ViewModel per il cambio password amministratore
/// </summary>
public class AdminChangePasswordViewModel
{
    public string UserId { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;

    [Required(ErrorMessage = "La nuova password è obbligatoria")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "La password deve essere di almeno 6 caratteri")]
    [DataType(DataType.Password)]
    public string NewPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "Conferma la nuova password")]
    [DataType(DataType.Password)]
    [Compare("NewPassword", ErrorMessage = "Le password non coincidono")]
    public string ConfirmNewPassword { get; set; } = string.Empty;

    public bool ForcePasswordChange { get; set; }
}

/// <summary>
/// ViewModel per la gestione dei ruoli e permessi
/// </summary>
public class RoleManagementViewModel
{
    public List<RoleItemViewModel> Roles { get; set; } = new();
    public List<PolicyItemViewModel> Policies { get; set; } = new();
}

/// <summary>
/// ViewModel per singolo ruolo
/// </summary>
public class RoleItemViewModel
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public List<string> Policies { get; set; } = new();
    
    public string GetDescription() => Name switch
    {
        "Admin" => "Amministratore del sistema - Accesso completo a tutte le funzionalità",
        "PowerUser" => "Utente con privilegi avanzati - Può modificare dati ed esportare",
        "Editor" => "Editor - Può modificare parti e tavole assegnate",
        "Viewer" => "Visualizzatore - Accesso in sola lettura",
        _ => "Ruolo personalizzato"
    };
}

/// <summary>
/// ViewModel per singola policy
/// </summary>
public class PolicyItemViewModel
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> RequiredRoles { get; set; } = new();
    
    public string GetDescription() => Name switch
    {
        "CanEditCatalog" => "Modifica catalogo - Permette di modificare elementi del catalogo",
        "CanExportData" => "Esportazione dati - Permette di esportare dati dal sistema",
        "CanManageUsers" => "Gestione utenti - Permette di creare, modificare ed eliminare utenti",
        "CanChangeTableStatus" => "Modifica stato tavole - Permette di cambiare lo stato delle tavole",
        "CanDeleteData" => "Eliminazione dati - Permette di eliminare dati dal sistema",
        "CanCreateData" => "Creazione dati - Permette di creare nuovi dati",
        "ReadAccess" => "Accesso in lettura - Permette di visualizzare i dati",
        "WriteAccess" => "Accesso in scrittura - Permette di modificare i dati",
        "DeleteAccess" => "Accesso eliminazione - Permette di eliminare dati",
        _ => "Policy personalizzata"
    };
}

/// <summary>
/// Response per operazioni AJAX
/// </summary>
public class UserManagementResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public object? Data { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// ViewModel per statistiche utenti (dashboard)
/// </summary>
public class UserStatisticsViewModel
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int LockedUsers { get; set; }
    public int UnconfirmedEmails { get; set; }
    public Dictionary<string, int> UsersByRole { get; set; } = new();
    public List<UserActivityViewModel> RecentActivity { get; set; } = new();
}

/// <summary>
/// ViewModel per attività utente
/// </summary>
public class UserActivityViewModel
{
    public string UserId { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string IpAddress { get; set; } = string.Empty;
}
