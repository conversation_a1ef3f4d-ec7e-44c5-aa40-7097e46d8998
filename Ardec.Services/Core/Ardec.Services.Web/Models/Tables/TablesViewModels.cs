using Ardec.Services.Web.Data.Models;
using Ardec.Services.Web.Models;
using Ardec.Services.Web.Services.CID;

namespace Ardec.Services.Web.Models.Tables;


/// <summary>
/// Filter options for tables view
/// </summary>
public class TablesFilter
{
    public string? TableNameFilter { get; set; }
    public string? StatusFilter { get; set; }
    public DateTime? LastModifiedFrom { get; set; }
    public DateTime? LastModifiedTo { get; set; }
    public bool? HasParts { get; set; }
    public int? MinParts { get; set; }
    public int? MaxParts { get; set; }
    
    /// <summary>
    /// Check if any filter is applied
    /// </summary>
    public bool HasActiveFilters => 
        !string.IsNullOrWhiteSpace(TableNameFilter) ||
        !string.IsNullOrWhiteSpace(StatusFilter) ||
        LastModifiedFrom.HasValue ||
        LastModifiedTo.HasValue ||
        HasParts.HasValue ||
        MinParts.HasValue ||
        MaxParts.HasValue;
}

/// <summary>
/// Statistics for tables overview
/// </summary>
public class TablesStatistics
{
    public int TotalTables { get; set; }
    public int GreenTables { get; set; }  // Status 00
    public int YellowTables { get; set; } // Status 01
    public int BlueTables { get; set; }   // Status 02
    public int RedTables { get; set; }    // Status 03
    public int TotalParts { get; set; }
    public DateTime? LastUpdate { get; set; }
    
    public double GreenPercentage => TotalTables > 0 ? (double)GreenTables / TotalTables * 100 : 0;
    public double YellowPercentage => TotalTables > 0 ? (double)YellowTables / TotalTables * 100 : 0;
    public double BluePercentage => TotalTables > 0 ? (double)BlueTables / TotalTables * 100 : 0;
    public double RedPercentage => TotalTables > 0 ? (double)RedTables / TotalTables * 100 : 0;
}

#region Tables Details ViewModels

/// <summary>
/// ViewModel for Table Details page showing parts and configuration for specific CID
/// </summary>
public class TableDetailsViewModel
{
    public string TER { get; set; } = string.Empty;
    public string CID { get; set; } = string.Empty;
    public string Tavola { get; set; } = string.Empty;
    public string Language { get; set; } = "IT";
    
    // Legacy compatibility properties - all properties expected by existing Views
    public string? CodiceTecnico { get; set; }
    public string? Versione { get; set; }
    public string? Stato { get; set; }
    public DateTime? Data { get; set; }
    public string? Descrizione1IT { get; set; }
    public string? Descrizione2IT { get; set; }
    public string? Descrizione3IT { get; set; }
    public string? GruppoITA { get; set; }
    public string? GruppoENG { get; set; }
    public string? GruppoFRA { get; set; }
    public string? GruppoPOR { get; set; }
    public string? GruppoESP { get; set; }
    public string? GruppoTED { get; set; }
    public string? GruppoUSA { get; set; }
    public double? nTavola { get; set; }
    public double? CEDNPagina { get; set; }
    public string? CEDIGNPagina { get; set; }
    public string? DMC { get; set; }
    public string? SBC { get; set; }
    public string? SNS { get; set; }
    public string? TECHNAME { get; set; }
    public string? INFONAME { get; set; }
    public string? ICN_TITLE { get; set; }
    public bool ModificaTavola { get; set; } = false;
    public DateTime? DataModificaTavola { get; set; }
    public bool ModificaTesto { get; set; } = false;
    public DateTime? DataModificaTesto { get; set; }
    public int? NonForniti { get; set; }
    public string? Logo { get; set; }
    public string? Figura { get; set; }
    public string? Note { get; set; }
    
    // Composition and Catalog info for compatibility
    public CompositionInfo? CompositionInfo { get; set; }
    public CatalogInfo? CatalogInfo { get; set; }
    
    // CID and Table Info
    public TB_CID? CIDInfo { get; set; }
    public CIDTableVersionModel? TableInfo { get; set; }
    
    // Navigation
    public List<CidSelectorOption> AvailableCIDs { get; set; } = new();
    public List<CIDTableVersionModel> AvailableTables { get; set; } = new();
    
    // Parts Data
    public List<CIDPartModel> Parts { get; set; } = new();
    public PagedResult<CIDPartModel>? PagedParts { get; set; }
    
    // Version History
    public List<TableVersionHistoryModel> VersionHistory { get; set; } = new();
    public bool ShowVersionHistory { get; set; } = false;
    
    // Comparison
    public string? CompareWithCID { get; set; }
    public CIDComparisonModel? Comparison { get; set; }
    public bool ShowComparison { get; set; } = false;
    
    // Filtering and Search
    public TableDetailsFilter Filter { get; set; } = new();
    public string? SearchQuery { get; set; }
    
    // Statistics
    public TableDetailsStatistics Statistics { get; set; } = new();
    
    // UI State
    public string ViewMode { get; set; } = "Parts"; // "Parts", "History", "Comparison"
    public bool ShowFilters { get; set; } = false;
    public string? StatusMessage { get; set; }
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Navigation helpers
    /// </summary>
    public string? PreviousTable => GetAdjacentTable(-1);
    public string? NextTable => GetAdjacentTable(1);
    
    private string? GetAdjacentTable(int offset)
    {
        if (!AvailableTables.Any()) return null;
        
        var currentIndex = AvailableTables.FindIndex(t => t.Tavola == Tavola);
        if (currentIndex == -1) return null;
        
        var targetIndex = currentIndex + offset;
        if (targetIndex < 0 || targetIndex >= AvailableTables.Count) return null;
        
        return AvailableTables[targetIndex].Tavola;
    }
}

/// <summary>
/// Filter options for table details view
/// </summary>
public class TableDetailsFilter
{
    public string? PartNumberFilter { get; set; }
    public string? ItemFilter { get; set; }
    public string? DescriptionFilter { get; set; }
    public int? MinQuantity { get; set; }
    public int? MaxQuantity { get; set; }
    public bool? HasNotes { get; set; }
    public bool? IsUniqueToThisCID { get; set; }
    
    public bool HasActiveFilters => 
        !string.IsNullOrWhiteSpace(PartNumberFilter) ||
        !string.IsNullOrWhiteSpace(ItemFilter) ||
        !string.IsNullOrWhiteSpace(DescriptionFilter) ||
        MinQuantity.HasValue ||
        MaxQuantity.HasValue ||
        HasNotes.HasValue ||
        IsUniqueToThisCID.HasValue;
}

/// <summary>
/// Statistics for table details
/// </summary>
public class TableDetailsStatistics
{
    public int TotalParts { get; set; }
    public int UniqueParts { get; set; }
    public int SharedParts { get; set; }
    public int PartsWithQuantity { get; set; }
    public int PartsWithNotes { get; set; }
    public DateTime? LastModified { get; set; }
    public string? LastModifiedBy { get; set; }
    
    public double UniquePartsPercentage => TotalParts > 0 ? (double)UniqueParts / TotalParts * 100 : 0;
    public double SharedPartsPercentage => TotalParts > 0 ? (double)SharedParts / TotalParts * 100 : 0;
}

#endregion

#region CID Management ViewModels

/// <summary>
/// ViewModel for CID selection and management
/// </summary>
public class CidSelectionViewModel
{
    public string TER { get; set; } = string.Empty;
    public string? SelectedCID { get; set; }
    public string Language { get; set; } = "IT";
    
    public List<CidSelectorOption> AvailableCIDs { get; set; } = new();
    public CidFilter Filter { get; set; } = new();
    public PagedResult<TB_CID>? PagedCIDs { get; set; }
    
    // Statistics
    public object? CatalogStatistics { get; set; }
    
    // UI State
    public bool ShowCreateForm { get; set; } = false;
    public bool ShowFilters { get; set; } = false;
    public string? StatusMessage { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// ViewModel for CID comparison
/// </summary>
public class CidComparisonViewModel
{
    public string TER { get; set; } = string.Empty;
    public string CID1 { get; set; } = string.Empty;
    public string CID2 { get; set; } = string.Empty;
    public string Language { get; set; } = "IT";
    
    public TB_CID? CID1Info { get; set; }
    public TB_CID? CID2Info { get; set; }
    
    public CIDComparisonModel? Comparison { get; set; }
    
    // Available CIDs for comparison
    public List<CidSelectorOption> AvailableCIDs { get; set; } = new();
    
    // View options
    public string ViewMode { get; set; } = "Summary"; // "Summary", "Detailed", "SideBySide"
    public bool ShowIdenticalParts { get; set; } = false;
    public bool ShowDifferentVersions { get; set; } = true;
    public bool ShowUniqueParts { get; set; } = true;
    
    // UI State
    public string? StatusMessage { get; set; }
    public string? ErrorMessage { get; set; }
}

#endregion

#region Dashboard ViewModels

/// <summary>
/// ViewModel for version dashboard (Cruscotto Versioni)
/// Shows complete version matrix across all CIDs for a catalog
/// </summary>
public class VersionDashboardViewModel
{
    public string TER { get; set; } = string.Empty;
    public string Language { get; set; } = "IT";
    
    public CidVersionMatrix VersionMatrix { get; set; } = new();
    public List<CIDOverviewModel> CIDOverviews { get; set; } = new();
    
    // Filtering
    public DashboardFilter Filter { get; set; } = new();
    
    // Display options
    public bool ShowEmptyTables { get; set; } = true;
    public bool ShowStatistics { get; set; } = true;
    public string ColorScheme { get; set; } = "ARDEC"; // "ARDEC", "Simple", "HighContrast"
    
    // UI State
    public string ViewMode { get; set; } = "Matrix"; // "Matrix", "List", "Cards"
    public string? SelectedCID { get; set; }
    public string? SelectedTable { get; set; }
    public string? StatusMessage { get; set; }
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Get all unique tables across all CIDs
    /// </summary>
    public HashSet<string> AllTables => VersionMatrix.AllTables;
    
    /// <summary>
    /// Get CID count by status
    /// </summary>
    public Dictionary<TableVersionStatus, int> StatusDistribution => 
        CIDOverviews.SelectMany(c => c.Statistics.VersionStatusCounts)
                   .GroupBy(kvp => kvp.Key)
                   .ToDictionary(g => Enum.Parse<TableVersionStatus>(g.Key), 
                                g => g.Sum(kvp => kvp.Value));
}

/// <summary>
/// Filter options for dashboard
/// </summary>
public class DashboardFilter
{
    public List<string>? StatusFilter { get; set; }
    public DateTime? LastModifiedFrom { get; set; }
    public DateTime? LastModifiedTo { get; set; }
    public bool? HasActivity { get; set; }
    public string? CIDFilter { get; set; }
    public string? TableFilter { get; set; }
    
    public bool HasActiveFilters => 
        (StatusFilter?.Any() == true) ||
        LastModifiedFrom.HasValue ||
        LastModifiedTo.HasValue ||
        HasActivity.HasValue ||
        !string.IsNullOrWhiteSpace(CIDFilter) ||
        !string.IsNullOrWhiteSpace(TableFilter);
}

#endregion

#region Legacy Compatibility Classes

/// <summary>
/// Composition info for compatibility with existing Views
/// </summary>
public class CompositionInfo
{
    public string? TER { get; set; }
    public string? CID { get; set; }
    public string? Tavola { get; set; }
    public string? CodiceTecnico { get; set; }
    public string? Versione { get; set; }
    public string? Stato { get; set; }
    public DateTime? Data { get; set; }
    public string? Descrizione1IT { get; set; }
    public string? Descrizione2IT { get; set; }
    public string? Descrizione3IT { get; set; }
    public double? nTavola { get; set; }
    public string? Note { get; set; }
}

/// <summary>
/// Catalog info for compatibility with existing Views
/// </summary>
public class CatalogInfo
{
    public string? TER { get; set; }
    public string? TitoloBreve { get; set; }
    public string? Titolo { get; set; }
    public string? Revisione { get; set; }
    public string? Versione { get; set; }
    public string? Language { get; set; }
}

#endregion

#region CID Workflow ViewModels

/// <summary>
/// ViewModel for CID Selection page
/// First step in CID-based navigation from catalog
/// </summary>
public class CIDSelectionViewModel
{
    public string CatalogTER { get; set; } = string.Empty;
    public string CatalogTitle { get; set; } = string.Empty;
    public string? CatalogTitleComplete { get; set; }
    public List<CIDOverviewModel> AvailableCIDs { get; set; } = new();
    public string Language { get; set; } = "IT";
    
    // Statistics
    public int TotalCIDs => AvailableCIDs.Count;
    public int ActiveCIDs => AvailableCIDs.Count(c => c.IsActive);
    public int CompleteCIDs => AvailableCIDs.Count(c => c.Statistics.TablesUsed > 0);
    
    // Filtering and search
    public string? SearchQuery { get; set; }
    public string? StatusFilter { get; set; }
    
    // UI State
    public string ViewMode { get; set; } = "Grid"; // "Grid", "List", "Cards"
    public bool ShowFilters { get; set; } = false;
    public string? StatusMessage { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// ViewModel for CID Dashboard (Cruscotto Versioni)
/// Core CID functionality showing version matrix
/// </summary>
public class CIDDashboardViewModel
{
    public string CatalogTER { get; set; } = string.Empty;
    public string CatalogTitle { get; set; } = string.Empty;
    public string CID { get; set; } = string.Empty;
    public string CIDTitle { get; set; } = string.Empty;
    public string? CIDApplicability { get; set; }
    public string Language { get; set; } = "IT";
    
    // Core version matrix
    public CIDVersionMatrixModel VersionMatrix { get; set; } = new();
    public CIDOverviewModel? CIDOverview { get; set; }
    
    // Quick stats for dashboard
    public int TotalTables => VersionMatrix.TotalTables;
    public Dictionary<string, int> StatusDistribution => VersionMatrix.StatusCounts;
    
    // Health indicators
    public double HealthPercentage => TotalTables > 0 ? 
        (StatusDistribution.GetValueOrDefault("00", 0) * 100.0 / TotalTables) : 0;
    
    public string HealthStatus => HealthPercentage switch
    {
        >= 90 => "Ottimo",
        >= 70 => "Buono",
        >= 50 => "Discreto",
        _ => "Critico"
    };
    
    // Navigation
    public List<CidSelectorOption> AvailableCIDs { get; set; } = new();
    
    // Display options
    public bool ShowEmptyTables { get; set; } = true;
    public bool ShowLegend { get; set; } = true;
    public string ColorScheme { get; set; } = "ARDEC"; // "ARDEC", "Simple", "HighContrast"
    
    // UI State
    public string? SelectedTable { get; set; }
    public bool ShowTableDetails { get; set; } = false;
    public string? StatusMessage { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// ViewModel for CID Tables Index
/// Shows tables list for specific CID configuration
/// </summary>
public class CIDTablesIndexViewModel
{
    public string CatalogTER { get; set; } = string.Empty;
    public string CatalogTitle { get; set; } = string.Empty;
    public string CID { get; set; } = string.Empty;
    public string CIDTitle { get; set; } = string.Empty;
    public string Language { get; set; } = "IT";
    
    // Tables data
    public List<CIDTableVersionModel> Tables { get; set; } = new();
    public int TotalCount => Tables.Count;
    
    // CID context
    public CIDOverviewModel? CIDOverview { get; set; }
    public TB_CID? CIDInfo { get; set; }
    
    // Navigation
    public List<CidSelectorOption> AvailableCIDs { get; set; } = new();
    
    // Filtering and search
    public TablesFilter Filter { get; set; } = new();
    public string? SearchQuery { get; set; }
    
    // Statistics
    public TablesStatistics Statistics { get; set; } = new();
    
    // UI State
    public string ViewMode { get; set; } = "Grid"; // "Grid", "List", "Cards"
    public bool ShowFilters { get; set; } = false;
    public string? StatusMessage { get; set; }
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Get tables grouped by status for quick navigation
    /// </summary>
    public Dictionary<string, List<CIDTableVersionModel>> TablesByStatus => 
        Tables.GroupBy(t => t.VersionStatus)
              .ToDictionary(g => g.Key, g => g.ToList());
}

#endregion

#region Overview ViewModels

/// <summary>
/// ViewModel for Tables Overview page (navbar entry point)
/// Shows all catalogs with tables for CID navigation
/// </summary>
public class TablesOverviewViewModel
{
    public List<CatalogWithTablesModel> CatalogsWithTables { get; set; } = new();
    public int TotalCatalogs { get; set; }
    public string Language { get; set; } = "IT";
    
    // Statistics
    public int TotalTables => CatalogsWithTables.Sum(c => c.TableCount);
    public int CatalogsWithCIDs => CatalogsWithTables.Count(c => c.HasCIDs);
    
    // Search and Filter
    public string? SearchQuery { get; set; }
    public bool ShowOnlyWithCIDs { get; set; } = false;
    
    // UI State
    public string ViewMode { get; set; } = "Grid"; // "Grid", "List"
    public string? StatusMessage { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Model representing a catalog with table information
/// </summary>
public class CatalogWithTablesModel
{
    public string TER { get; set; } = string.Empty;
    public string TitoloBreve { get; set; } = string.Empty;
    public string? Titolo { get; set; }
    public int TableCount { get; set; }
    public bool HasCIDs { get; set; }
    public DateTime? LastUpdate { get; set; }
    
    // Additional statistics (can be populated later)
    public int CIDCount { get; set; } = 0;
    public int ActiveCIDCount { get; set; } = 0;
    public string? PrimaryLanguage { get; set; } = "IT";
}

#endregion

#region Tables Dashboard (Desktop-style) ViewModels

/// <summary>
/// ViewModel for Tables Dashboard - Desktop app style table list
/// Shows all tables across catalogs with version status matrix
/// Core navigation entry point for table management
/// </summary>
public class TablesDashboardViewModel
{
    public List<TableDashboardItemModel> Tables { get; set; } = new();
    public string? SelectedCatalog { get; set; }
    public string? SearchTerm { get; set; }
    public string Language { get; set; } = "IT";
    
    // Available filters
    public List<CatalogOption> AvailableCatalogs { get; set; } = new();
    public List<string> AvailableStatuses { get; set; } = new() { "00", "01", "02", "03" };
    
    // Statistics
    public TablesDashboardStatistics Statistics { get; set; } = new();
    
    // Pagination
    public int CurrentPage { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public int TotalTables { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalTables / PageSize);
    
    // View options
    public string ViewMode { get; set; } = "Dashboard"; // "Dashboard", "List", "Grid"
    public bool ShowVersionDetails { get; set; } = true;
    public bool GroupByCatalog { get; set; } = false;
    public string SortBy { get; set; } = "nTavola"; // "nTavola", "Tavola", "LastUpdate", "Status"
    public string SortDirection { get; set; } = "asc"; // "asc", "desc"
    
    // UI State
    public string? StatusMessage { get; set; }
    public string? ErrorMessage { get; set; }
    public bool ShowFilters { get; set; } = false;
    
    /// <summary>
    /// Get filtered and sorted tables
    /// </summary>
    public IEnumerable<TableDashboardItemModel> FilteredTables
    {
        get
        {
            var filtered = Tables.AsEnumerable();
            
            // Apply catalog filter
            if (!string.IsNullOrEmpty(SelectedCatalog))
                filtered = filtered.Where(t => t.CatalogTER == SelectedCatalog);
            
            // Apply search filter
            if (!string.IsNullOrEmpty(SearchTerm))
            {
                var searchLower = SearchTerm.ToLowerInvariant();
                filtered = filtered.Where(t => 
                    t.Tavola.ToLowerInvariant().Contains(searchLower) ||
                    (t.CodiceTecnico ?? "").ToLowerInvariant().Contains(searchLower) ||
                    (t.Descrizione1IT ?? "").ToLowerInvariant().Contains(searchLower));
            }
            
            // Apply sorting
            filtered = SortBy switch
            {
                "Tavola" => SortDirection == "asc" ? 
                    filtered.OrderBy(t => t.Tavola) : 
                    filtered.OrderByDescending(t => t.Tavola),
                "LastUpdate" => SortDirection == "asc" ? 
                    filtered.OrderBy(t => t.LastUpdate) : 
                    filtered.OrderByDescending(t => t.LastUpdate),
                "Status" => SortDirection == "asc" ? 
                    filtered.OrderBy(t => t.CurrentVersionStatus) : 
                    filtered.OrderByDescending(t => t.CurrentVersionStatus),
                _ => SortDirection == "asc" ? 
                    filtered.OrderBy(t => t.nTavola).ThenBy(t => t.Tavola) : 
                    filtered.OrderByDescending(t => t.nTavola).ThenByDescending(t => t.Tavola)
            };
            
            return filtered;
        }
    }
}

/// <summary>
/// Model for single table item in dashboard
/// Represents a table with all its versions and status information
/// </summary>
public class TableDashboardItemModel
{
    // Table basic info
    public string Tavola { get; set; } = string.Empty;
    public string CatalogTER { get; set; } = string.Empty;
    public string CatalogTitle { get; set; } = string.Empty;
    public string? CodiceTecnico { get; set; }
    public string? Descrizione1IT { get; set; }
    public double? nTavola { get; set; }
    
    // Current/Latest version info (what user works with by default)
    public string CurrentVersion { get; set; } = "00";
    public string CurrentVersionStatus { get; set; } = "00";
    public DateTime? LastUpdate { get; set; }
    public bool HasParts { get; set; }
    public int PartsCount { get; set; }
    
    // All available versions with status (for version matrix display)
    public List<TableVersionInfo> AvailableVersions { get; set; } = new();
    
    // CID information
    public List<string> AssociatedCIDs { get; set; } = new();
    public int CIDCount => AssociatedCIDs.Count;
    
    // Navigation helpers
    public string DetailUrl => $"/Tables/Details/{Tavola}?ter={CatalogTER}";
    public string CIDSelectionUrl => $"/Tables/Catalog/{CatalogTER}";
    
    /// <summary>
    /// Get latest version (highest version number)
    /// </summary>
    public TableVersionInfo? LatestVersion => AvailableVersions
        .OrderByDescending(v => v.Versione)
        .FirstOrDefault();
    
    /// <summary>
    /// Check if table has multiple versions
    /// </summary>
    public bool HasMultipleVersions => AvailableVersions.Count > 1;
    
    /// <summary>
    /// Get status color for current version
    /// </summary>
    public string StatusColor => GetStatusColor(CurrentVersionStatus);
    
    /// <summary>
    /// Get status text for current version
    /// </summary>
    public string StatusText => GetStatusText(CurrentVersionStatus);
    
    private static string GetStatusColor(string status) => status switch
    {
        "00" => "bg-green-100 text-green-800 border-green-200", // Verde - Approved
        "01" => "bg-yellow-100 text-yellow-800 border-yellow-200", // Giallo - In Review
        "02" => "bg-blue-100 text-blue-800 border-blue-200", // Blu - Draft
        "03" => "bg-red-100 text-red-800 border-red-200", // Rosso - Rejected/Issues
        _ => "bg-gray-100 text-gray-800 border-gray-200"
    };
    
    private static string GetStatusText(string status) => status switch
    {
        "00" => "Approvata",
        "01" => "In Revisione",
        "02" => "Bozza",
        "03" => "Problemi",
        _ => "Sconosciuto"
    };
}

/// <summary>
/// Information about specific version of a table
/// </summary>
public class TableVersionInfo
{
    public string Versione { get; set; } = string.Empty;
    public string Stato { get; set; } = string.Empty;
    public DateTime? Data { get; set; }
    public DateTime? LastModified { get; set; }
    public int PartsCount { get; set; }
    public bool IsCurrentVersion { get; set; }
    
    // Visual representation
    public string StatusColor => GetStatusColor(Stato);
    public string StatusText => GetStatusText(Stato);
    
    private static string GetStatusColor(string status) => status switch
    {
        "00" => "bg-green-500", // Verde
        "01" => "bg-yellow-500", // Giallo
        "02" => "bg-blue-500", // Blu
        "03" => "bg-red-500", // Rosso
        _ => "bg-gray-500"
    };
    
    private static string GetStatusText(string status) => status switch
    {
        "00" => "Approvata",
        "01" => "In Revisione",
        "02" => "Bozza",
        "03" => "Problemi",
        _ => "Sconosciuto"
    };
}

/// <summary>
/// Statistics for tables dashboard
/// </summary>
public class TablesDashboardStatistics
{
    public int TotalTables { get; set; }
    public int TotalCatalogs { get; set; }
    public Dictionary<string, int> StatusDistribution { get; set; } = new();
    public int TablesWithMultipleVersions { get; set; }
    public int TablesWithParts { get; set; }
    public DateTime? LastUpdate { get; set; }
    
    // Percentages
    public double ApprovedPercentage => TotalTables > 0 ? (StatusDistribution.GetValueOrDefault("00", 0) * 100.0 / TotalTables) : 0;
    public double InReviewPercentage => TotalTables > 0 ? (StatusDistribution.GetValueOrDefault("01", 0) * 100.0 / TotalTables) : 0;
    public double DraftPercentage => TotalTables > 0 ? (StatusDistribution.GetValueOrDefault("02", 0) * 100.0 / TotalTables) : 0;
    public double IssuesPercentage => TotalTables > 0 ? (StatusDistribution.GetValueOrDefault("03", 0) * 100.0 / TotalTables) : 0;
}

/// <summary>
/// Result wrapper for dashboard data from service
/// </summary>
public class TablesDashboardResult
{
    public List<TableDashboardItemModel> Tables { get; set; } = new();
    public int TotalCount { get; set; }
    public TablesDashboardStatistics Statistics { get; set; } = new();
}

/// <summary>
/// Simple catalog option for dropdown/filters
/// </summary>
public class CatalogOption
{
    public string TER { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public int TableCount { get; set; }
}

#endregion

#region Shared Components ViewModels

/// <summary>
/// ViewModel for CID selector component
/// </summary>
public class CidSelectorComponentViewModel
{
    public string TER { get; set; } = string.Empty;
    public string? SelectedCID { get; set; }
    public List<CidSelectorOption> Options { get; set; } = new();
    public bool AllowEmpty { get; set; } = true;
    public string EmptyText { get; set; } = "Seleziona CID...";
    public bool ShowStatistics { get; set; } = false;
    public string Size { get; set; } = "md"; // "sm", "md", "lg"
}

/// <summary>
/// ViewModel for version status badge component
/// </summary>
public class StatusBadgeComponentViewModel
{
    public TableVersionStatus Status { get; set; }
    public string Size { get; set; } = "md"; // "sm", "md", "lg"
    public bool ShowText { get; set; } = true;
    public bool ShowTooltip { get; set; } = true;
    public string? CustomText { get; set; }
}

/// <summary>
/// ViewModel for version matrix cell component
/// </summary>
public class MatrixCellComponentViewModel
{
    public string CID { get; set; } = string.Empty;
    public string Table { get; set; } = string.Empty;
    public TableVersionStatus Status { get; set; }
    public int? PartCount { get; set; }
    public DateTime? LastUpdate { get; set; }
    public bool IsClickable { get; set; } = true;
    public bool IsSelected { get; set; } = false;
}

#endregion
