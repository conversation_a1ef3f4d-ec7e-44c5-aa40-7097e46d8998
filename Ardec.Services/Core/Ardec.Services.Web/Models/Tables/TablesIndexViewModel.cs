using Ardec.Services.Web.Data.Models;

namespace Ardec.Services.Web.Models.Tables;

/// <summary>
/// ViewModel semplice per Tables/Index - pattern identico a Catalogs
/// </summary>
public class TablesIndexViewModel
{
    // Context
    public string? FilterTER { get; set; }
    public string? CatalogTitle { get; set; }
    public bool IsFilteredByCatalog => !string.IsNullOrEmpty(FilterTER);
    
    // Data
    public IEnumerable<TableRowData> Tables { get; set; } = new List<TableRowData>();
    
    // Stats
    public int TotalCount { get; set; }
    public int VersionsCount { get; set; }
}

/// <summary>
/// Dati per singola riga della grid Tables
/// </summary>
public class TableRowData
{
    public string Tavola { get; set; } = string.Empty;
    public string? CodiceTecnico { get; set; }
    public string? TER { get; set; }
    public List<VersionStatusPair> VersionStatusPairs { get; set; } = new List<VersionStatusPair>();
    public int VersionCount => VersionStatusPairs.Count;
    public DateTime? LastModified { get; set; }
}

/// <summary>
/// Coppia versione-stato per visualizzazione
/// </summary>
public class VersionStatusPair
{
    public string Versione { get; set; } = string.Empty;
    public string Stato { get; set; } = "In lavorazione";
    public string StatusText => GetStatusText(Stato);
    public string StatusBadgeClass => GetStatusBadgeClass(Stato);
    
    private static string GetStatusText(string stato)
    {
        // Support both numeric codes (00/01/02/03) and legacy textual values
        return stato switch
        {
            // Numeric codes from TB_DettagliTavole
            "00" => "Approvata",
            "01" => "In Revisione",
            "02" => "Bozza",
            "03" => "Problemi",

            // Legacy textual values
            "In lavorazione" => "In Revisione",
            "Pubblicata" => "Approvata",

            _ => "Sconosciuto"
        };
    }

    private static string GetStatusBadgeClass(string stato)
    {
        // Support both numeric codes (00/01/02/03) and legacy textual values
        return stato switch
        {
            // Numeric codes
            "00" => "bg-green-100 text-green-800",
            "01" => "bg-yellow-100 text-yellow-800",
            "02" => "bg-blue-100 text-blue-800",
            "03" => "bg-red-100 text-red-800",

            // Legacy textual values
            "In lavorazione" => "bg-yellow-100 text-yellow-800",
            "Pubblicata" => "bg-green-100 text-green-800",

            _ => "bg-gray-100 text-gray-800"
        };
    }
}